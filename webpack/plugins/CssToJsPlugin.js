const { RawSource, ConcatSource } = require('webpack-sources')

class CssToJsPlugin {
  apply(compiler) {
    compiler.hooks.thisCompilation.tap('CssToJsPlugin', (compilation) => {
      // Hook into the process assets stage to modify the output
      compilation.hooks.processAssets.tap(
        {
          name: 'CssToJsPlugin',
          stage: compilation.PROCESS_ASSETS_STAGE_PRE_PROCESS,
        },
        (assets) => {
          // Find CSS assets generated by MiniCssExtractPlugin
          const cssAssets = {};

          Object.keys(assets).forEach(assetName => {
            if (assetName.endsWith('.css')) {
              console.log(assetName)
              const asset = assets[assetName];
              const entryName = assetName.replace('.css', '');
              cssAssets[entryName] = asset;

              // Remove the CSS asset since we're embedding it in JS
              delete assets[assetName];
            }
          });
          
          // Inject CSS into corresponding JS files
          Object.keys(assets).forEach(assetName => {
            if (assetName.endsWith('.js')) {
              const entryName = assetName.replace('.js', '');
              const cssAsset = cssAssets[entryName];

              if (cssAsset) {
                const jsAsset = assets[assetName];
                // we need to add the Stylesheet to the global object
                const path = [compiler.options.output.uniqueName, entryName].join('.')
                const cssExport = `\nif (typeof window !== 'undefined' && window.$ebit && window.${path}) {\n  window.${path}.Stylesheet = ${JSON.stringify(cssAsset.source())};\n}`;

                assets[assetName] = new ConcatSource(
                  jsAsset,
                  new RawSource(cssExport)
                )

                return
              }
            }
          });
        }
      );
    });
  }
}

module.exports = CssToJsPlugin
