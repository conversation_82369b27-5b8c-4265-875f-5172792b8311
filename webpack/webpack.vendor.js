const path = require('path');

module.exports = {
  entry: './src/react/vendor/vendor.js',
  output: {
    path: path.resolve(__dirname, '..', 'dist'),
    filename: 'vendor.js',
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        use: 'babel-loader',
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: ['.js', '.jsx'],
  },
  mode: 'production',
  // mode: 'development',
  // devtool: 'inline-source-map',
};
