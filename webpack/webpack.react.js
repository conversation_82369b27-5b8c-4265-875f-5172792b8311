const path = require('path');

const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const CssToJsPlugin = require('./plugins/CssToJsPlugin');

const pagePath = (...paths) => path.join(__dirname, '..', 'src', 'react', 'pages', ...paths)
const componentPath = (...paths) => path.join(__dirname, '..', 'src', 'react', 'components', ...paths)
const globalPath = (...paths) => path.join(__dirname, '..', 'src', 'react', 'global', ...paths)
const modifierPath = (...paths) => path.join(__dirname, '..', 'src', 'react', 'modifiers', ...paths)

module.exports = {
  entry: {
    core: './src/react/core.jsx',
    playground: './src/react/playground.js',

    Styles: globalPath('Styles.jsx'),

    ImageSlider: componentPath('ImageSlider', 'ImageSlider.jsx'),
    MainCarousel: componentPath('MainCarousel', 'MainCarousel.jsx'),
    ProviderCarousel: componentPath('ProviderCarousel', 'ProviderCarousel.jsx'),
    Hero: componentPath('Hero', 'Hero.jsx'),
    Sportsbook: componentPath('Sportsbook', 'Sportsbook.jsx'),
    BetrozCards: componentPath('BetrozCards', 'BetrozCards.jsx'),
    VipClubBanner: componentPath('VipClubBanner', 'VipClubBanner.jsx'),
    PromotionCards: componentPath('PromotionCards', 'PromotionCards.jsx'),
    MissionsModal: componentPath('MissionsModal', 'MissionsModal.jsx'),
    SidebarButtons: componentPath('SidebarButtons', 'SidebarButtons.jsx'),
    SidebarButtonsSmall: componentPath('SidebarButtons', 'SidebarButtonsSmall.jsx'),

    RemoveMainSlider: modifierPath('RemoveMainSlider.jsx'),
    RemoveProvidersMarquee: modifierPath('RemoveProvidersMarquee.jsx'),
    RemoveVIPProgress: modifierPath('RemoveVIPProgress.jsx'),

    OriginalsPage: pagePath('originals', 'OriginalsPage.jsx'),
    MissionsPage: pagePath('missions', 'MissionsPage.jsx'),
    MarketPage: pagePath('market', 'MarketPage.jsx'),
  },
  output: {
    path: path.resolve(__dirname, '..', 'dist'),
    filename: '[name].js',
    library: ['$ebit', '[name]'],
    libraryTarget: 'window', // or 'window' or 'umd,
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
          "presets": [
            "@babel/preset-env",
            ["@babel/preset-react", { "runtime": "automatic" }]
          ]
        }
        },
      },
      // CSS Modules for local files
      {
        test: /\.module\.css$/i,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              emit: true
            }
          },
          {
            loader: 'css-loader',
            options: {
              modules: {
                namedExport: false,
                localIdentName: '[name]_[local]__[hash:base64:5]'
              },
              importLoaders: 1
            },
          },
        ],
      },
      // Regular CSS for node_modules and global styles
      {
        test: /\.css$/i,
        exclude: /\.module\.css$/i,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              emit: true
            }
          },
          {
            loader: 'css-loader',
            options: {
              modules: false,
              importLoaders: 1
            },
          },
        ],
      },
      {
        test: /\.svg$/,
        type: 'asset/source',
      },
    ],
  },
  externals: require('../src/react/vendor/vendor.externals.json'),
  resolve: {
    extensions: ['.js', '.jsx'],
  },
  plugins: [
    new MiniCssExtractPlugin(),
    new CssToJsPlugin()
  ],
  // devtool: 'inline-source-map',
  // mode: 'development',
  mode: 'production',
};
