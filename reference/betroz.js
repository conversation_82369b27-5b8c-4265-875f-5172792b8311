// Tarayıcı konsoluna ve ekrana debug bilgisi yazmak için
const DEBUG = false;
const LANG = 'tr';
function writeToConsole(message) {
    if (DEBUG) {
        console.log(message);

        const logDiv = document.getElementById('debug-log') || createDebugDiv();
        const logMessage = document.createElement('div');
        logMessage.textContent = message;
        logDiv.appendChild(logMessage);
    }
}

function createDebugDiv() {
    const logDiv = document.createElement('div');
    logDiv.id = 'debug-log';
    logDiv.style.position = 'fixed';
    logDiv.style.bottom = '0';
    logDiv.style.right = '0';
    logDiv.style.width = '300px';
    logDiv.style.height = '90px';
    logDiv.style.overflowY = 'scroll';
    logDiv.style.backgroundColor = '#000';
    logDiv.style.color = '#fff';
    logDiv.style.padding = '10px';
    logDiv.style.fontSize = '12px';
    document.body.appendChild(logDiv);
    return logDiv;
}

function setTheme(bodyTheme = 'dark', htmlTheme = 'light') {
    document.body.setAttribute('data-theme', bodyTheme);
    document.documentElement.setAttribute('data-bs-theme', htmlTheme);
}
setTheme('dark', 'dark'); // Tersi

function readPath() {
    const currentPath = window.location.pathname;
    var root = document.getElementById('root');
    var removeHighlightBar = document.getElementById('highlightArea');
    var removeFastAccessSection = document.getElementById('fastAccessSection');
    var removeBannerSection = document.getElementById('bannerSection');
    var headerMenuCheck = document.getElementById('main-menu');
    var headerTag = document.getElementsByTagName('headerTag');
    writeToConsole(`Reading path: ${currentPath}`);
    writeToConsole(currentPath.includes('casino'));
    if(!headerMenuCheck && !currentPath.includes('casino'))
    {
        document.getElementById('main__content').classList.add('with-additional-menu');
        headAdditionalMenu();
    }

    if (currentPath === '/en/' || currentPath === '/tr/' || currentPath === '/') {
        writeToConsole(`Executing homepage manipulations for: ${currentPath}`);
        root.classList.add('homepage');

        // Hem dinamik hem de statik kontrol
        Promise.race([waitForDomLoad(), staticTimeout()])
            .then(() => homePageManipulation())
            .catch((err) => writeToConsole(`Error: ${err.message}`));
    }else if(currentPath.includes('casino')){
        headerMenuCheck.remove();
        document.getElementById('main__content').classList.remove('with-additional-menu');
        root.classList.remove('homepage');
        if(removeHighlightBar)
        {
            removeHighlightBar.remove();
        }
        if(removeBannerSection)
        {
            removeBannerSection.remove();
        }
        if(removeFastAccessSection)
        {
            removeFastAccessSection.remove();
        }
    } else {
        root.classList.remove('homepage');
        if(removeHighlightBar)
        {
            removeHighlightBar.remove();
        }
        if(removeBannerSection)
        {
            removeBannerSection.remove();
        }
        if(removeFastAccessSection)
        {
            removeFastAccessSection.remove();
        }
    }
}

function headMenuItem(text, icon, link = '#') {
    const menuItem = document.createElement('div');
    menuItem.className = 'main-menu-item';

    const menuLink = document.createElement('a');
    menuLink.href = link;
    menuLink.className = 'main-menu-text';

    // If icon SVG is provided
    if (icon) {
        menuLink.innerHTML = icon + text;
    } else {
        menuLink.textContent = text;
    }

    menuItem.appendChild(menuLink);
    return menuItem;
}

function headAdditionalMenu() {
    const navbar = document.createElement('div');
    navbar.className = 'main-menu';
    navbar.id = 'main-menu';

    // Menu items data
    const menuItems = [
        {
            text: 'Anasayfa',
            link: '/'+LANG+'/',
            icon: '<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2061_16939)"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.9836 1.43736C5.08486 0.320848 6.91514 0.320848 8.0164 1.43736C8.02086 1.44187 8.02518 1.44649 8.0294 1.4512L11.9028 5.77977C12.005 5.894 12.0288 6.05519 11.9637 6.19235C11.8985 6.32951 11.7565 6.41747 11.6 6.41747H10.7148L9.85438 9.8496C9.6607 10.749 8.84748 11.4 7.89714 11.4H3.90286C2.95252 11.4 2.1393 10.749 1.94562 9.8496L1.08518 6.41747H0.400006C0.243554 6.41747 0.101464 6.32951 0.0363242 6.19235C-0.0288137 6.05519 -0.00504375 5.894 0.0971662 5.77977L3.9706 1.4512C3.97482 1.44649 3.97914 1.44187 3.9836 1.43736ZM4 6.96428C4 5.89917 4.89544 5.03571 6 5.03571C7.10456 5.03571 8 5.89917 8 6.96428C8 8.02939 7.10456 8.89285 6 8.89285C4.89544 8.89285 4 8.02939 4 6.96428Z" fill="currentColor"></path></g><defs><clipPath id="clip0_2061_16939"><rect width="12" height="12" fill="white"></rect></clipPath></defs></svg>'
        },
        {
            text: 'Spor',
            link: '/'+LANG+'/sportsbook',
            icon: '<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2061_16944)"><path d="M11.2968 1.53026C10.8433 0.957861 10.2048 0.557062 9.49901 0.401675L7.7693 0.0209047C7.46658 -0.0457241 7.15648 0.0490745 6.93989 0.274505L6.37172 0.865808C6.25373 0.988562 6.0959 1.05684 5.92725 1.05803C5.7592 1.0593 5.59983 0.993193 5.48023 0.872135L4.88573 0.270494C4.66678 0.0489074 4.34433 -0.0429549 4.0443 0.030788L2.43609 0.425953C1.74283 0.596309 1.11862 1.00489 0.678467 1.57643C0.238334 2.14794 -0.00260378 2.86274 2.12252e-05 3.58911L0.00109935 3.88752C0.0028806 4.37536 0.248201 4.81806 0.657326 5.07171C1.06647 5.32535 1.56518 5.34395 1.99141 5.1215L2.17451 5.02594L2.17385 11.1805C2.17383 11.3994 2.2575 11.6052 2.40944 11.76C2.56139 11.9147 2.76342 12 2.97834 12H9.08831C9.30342 12 9.50557 11.9146 9.65756 11.7596C9.80953 11.6046 9.89309 11.3986 9.89278 11.1795L9.88491 5.06786L9.94394 5.10417C10.3682 5.36515 10.8791 5.37286 11.3107 5.1248C11.7423 4.87671 12 4.42728 12 3.9225V3.55848C12 2.82294 11.7503 2.10265 11.2968 1.53026ZM5.63018 8.06219C5.63018 8.2802 5.36934 8.38918 5.10848 8.38918C4.84762 8.38918 4.58679 8.28018 4.58679 8.06219V4.59458L4.39282 4.7172C4.33263 4.75807 4.27244 4.77167 4.22559 4.77167C4.03162 4.77167 3.89787 4.56049 3.89787 4.34932C3.89787 4.20627 3.95805 4.07001 4.09183 3.98827L4.9747 3.44325C5.02819 3.40921 5.09508 3.39558 5.16867 3.39558C5.3827 3.39558 5.63018 3.52501 5.63018 3.72939V8.06219ZM7.16845 8.38918C6.90759 8.38918 6.64673 8.28018 6.64673 8.06219V4.59458L6.45276 4.7172C6.39258 4.75807 6.33239 4.77167 6.28556 4.77167C6.09159 4.77167 5.95781 4.56049 5.95781 4.34932C5.95781 4.20627 6.018 4.07001 6.15178 3.98827L7.03465 3.44325C7.08815 3.40921 7.15502 3.39558 7.22862 3.39558C7.44267 3.39558 7.69012 3.52501 7.69012 3.72939V8.06219H7.69015C7.69015 8.2802 7.42929 8.38918 7.16845 8.38918Z" fill="currentColor"></path></g><defs><clipPath id="clip0_2061_16944"><rect width="12" height="12" fill="white"></rect></clipPath></defs></svg>'
        },
        {
            text: 'Canlı Bahis',
            link: '/'+LANG+'/sportsbook/live-betting',
            icon: '<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 2.625V9.375C0.000297764 9.67327 0.10575 9.95922 0.293222 10.1701C0.480694 10.381 0.734875 10.4997 1 10.5H5.5V1.5H1C0.734875 1.50033 0.480694 1.61897 0.293222 1.82987C0.10575 2.04078 0.000297764 2.32673 0 2.625ZM2.5 3.1875C2.89769 3.18798 3.27897 3.36593 3.56019 3.68229C3.8414 3.99865 3.99957 4.4276 4 4.875V7.125C4 7.57255 3.84196 8.00178 3.56066 8.31824C3.27936 8.63471 2.89782 8.8125 2.5 8.8125C2.10218 8.8125 1.72064 8.63471 1.43934 8.31824C1.15804 8.00178 1 7.57255 1 7.125V4.875C1.00043 4.4276 1.1586 3.99865 1.43981 3.68229C1.72103 3.36593 2.10231 3.18798 2.5 3.1875Z" fill="currentColor"></path><path d="M2.5 7.6875C2.63261 7.6875 2.75979 7.62824 2.85355 7.52275C2.94732 7.41726 3 7.27418 3 7.125V4.875C3 4.72582 2.94732 4.58274 2.85355 4.47725C2.75979 4.37176 2.63261 4.3125 2.5 4.3125C2.36739 4.3125 2.24021 4.37176 2.14645 4.47725C2.05268 4.58274 2 4.72582 2 4.875V7.125C2 7.27418 2.05268 7.41726 2.14645 7.52275C2.24021 7.62824 2.36739 7.6875 2.5 7.6875Z" fill="currentColor"></path><path d="M11 1.5H6.5V10.5H11C11.2651 10.4997 11.5193 10.381 11.7068 10.1701C11.8942 9.95922 11.9997 9.67327 12 9.375V2.625C11.9997 2.32673 11.8942 2.04078 11.7068 1.82987C11.5193 1.61897 11.2651 1.50033 11 1.5ZM11 7.125C11 7.57255 10.842 8.00178 10.5607 8.31824C10.2794 8.63471 9.89782 8.8125 9.5 8.8125C9.10218 8.8125 8.72064 8.63471 8.43934 8.31824C8.15804 8.00178 8 7.57255 8 7.125V4.875C8 4.42745 8.15804 3.99822 8.43934 3.68176C8.72064 3.36529 9.10218 3.1875 9.5 3.1875C9.89782 3.1875 10.2794 3.36529 10.5607 3.68176C10.842 3.99822 11 4.42745 11 4.875V7.125Z" fill="currentColor"></path><path d="M9.5 4.3125C9.36739 4.3125 9.24021 4.37176 9.14645 4.47725C9.05268 4.58274 9 4.72582 9 4.875V7.125C9 7.27418 9.05268 7.41726 9.14645 7.52275C9.24021 7.62824 9.36739 7.6875 9.5 7.6875C9.63261 7.6875 9.75979 7.62824 9.85355 7.52275C9.94732 7.41726 10 7.27418 10 7.125V4.875C10 4.72582 9.94732 4.58274 9.85355 4.47725C9.75979 4.37176 9.63261 4.3125 9.5 4.3125Z" fill="currentColor"></path></svg>'
        },
        {
            text: 'Slot',
            link: '/'+LANG+'/casino/group/lobby',
            icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1_308)"><path d="M10.3207 5.67932C7.78198 3.14063 5.43066 1.28845 2.36572 4.35352C-0.439947 7.15918 -0.57337 13.3468 1.03991 14.9601C2.65319 16.5734 8.84082 16.4399 11.6465 13.6343C14.7115 10.5692 12.8594 8.21802 10.3207 5.67932ZM3.3601 13.3029L2.69714 12.6399L4.02294 11.3141L4.68591 11.9769L3.3601 13.3029ZM3.3601 10.6511L2.69714 9.98828L4.02294 8.66236L4.68591 9.32532L3.3601 10.6511ZM3.3601 7.99951L2.69714 7.33655L4.02294 6.01074L4.68591 6.67371L3.3601 7.99951ZM6.01171 13.3029L5.34887 12.6399L6.67468 11.3141L7.33764 11.9769L6.01171 13.3029ZM6.01171 10.6511L5.34887 9.98828L6.67468 8.66236L7.33764 9.32532L6.01171 10.6511ZM6.01171 7.99951L5.34887 7.33655L6.67468 6.01074L7.33764 6.67371L6.01171 7.99951ZM8.66345 13.3029L8.00048 12.6399L9.32629 11.3141L9.98925 11.9769L8.66345 13.3029ZM8.66345 10.6511L8.00048 9.98828L9.32629 8.66236L9.98925 9.32532L8.66345 10.6511Z" fill="currentColor"></path><path d="M11.1292 4.19006C11.3102 3.29614 11.057 2.0177 10.3653 1.32581L9.03945 0C8.41482 0.624512 8.11416 1.84705 8.20095 2.66358C9.29446 3.32886 10.2346 4.26734 10.9835 5.01636C11.7326 5.76538 12.6712 6.70545 13.3365 7.79908C14.153 7.88587 15.3574 7.56714 15.9821 6.94263L14.6563 5.61682C13.9644 4.92493 12.6858 4.67175 11.792 4.85291L11.8047 4.84021C12.9253 3.71973 14.4153 3.10242 16 3.10242V2.16504C14.1647 2.16504 12.4395 2.87964 11.1417 4.17737L11.1292 4.19006Z" fill="currentColor"></path></g><defs><clipPath id="clip0_1_308"><rect width="16" height="16" fill="white"></rect></clipPath></defs></svg>'
        },
        {
            text: 'Canlı Casino',
            link: '/'+LANG+'/live-casino',
            icon:'<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1_329)"><path d="M10.7042 8.83272C9.67609 8.84033 8.84909 9.68533 8.86047 10.7168C8.87185 11.7483 9.71023 12.5781 10.7383 12.5667C11.7588 12.5591 12.582 11.7255 12.582 10.6978C12.5782 9.6663 11.736 8.82891 10.7042 8.83272Z" fill="currentColor"></path><path d="M5.26409 3.43159C4.23603 3.43921 3.40902 4.2842 3.4204 5.31571C3.42799 6.34722 4.27017 7.17699 5.29823 7.16558C6.31871 7.15796 7.14192 6.32438 7.14192 5.29668C7.13812 4.26517 6.29595 3.42779 5.26409 3.43159Z" fill="currentColor"></path><path d="M5.03652 0.0059137C2.11925 0.142941 -0.134146 2.62846 0.00621716 5.5517C0.135199 8.28462 2.31272 10.4694 5.03652 10.5989H5.27931C8.19279 10.6027 10.56 8.23514 10.5638 5.3119C10.5638 5.30809 10.5638 5.30809 10.5638 5.30429V5.05307C10.4234 2.12983 7.95 -0.131113 5.03652 0.0059137ZM0.783903 5.68492C0.609398 5.68492 0.469035 5.54408 0.469035 5.36899C0.469035 5.1939 0.609398 5.05307 0.783903 5.05307C0.958409 5.05307 1.09877 5.1939 1.09877 5.36899C1.09877 5.54408 0.958409 5.68492 0.783903 5.68492ZM5.27931 0.424607C5.45381 0.424607 5.59418 0.56544 5.59418 0.74053C5.59418 0.91562 5.45381 1.05645 5.27931 1.05645C5.1048 1.05645 4.96444 0.91562 4.96444 0.74053C4.96444 0.569246 5.1048 0.424607 5.27931 0.424607ZM1.8537 1.89765C1.97888 1.77204 2.17615 1.77204 2.30134 1.89765C2.42653 2.02325 2.42653 2.22118 2.30134 2.34679C2.17615 2.4724 1.97888 2.4724 1.8537 2.34679C1.7323 2.22118 1.7323 2.01945 1.8537 1.89765ZM2.38101 8.80608C2.25582 8.93169 2.05855 8.93169 1.93336 8.80608C1.80817 8.68048 1.80817 8.48255 1.93336 8.35694C2.05855 8.23133 2.25582 8.23133 2.38101 8.35694C2.5024 8.47874 2.5024 8.68048 2.38101 8.80608ZM5.39312 10.1954C5.21861 10.1954 5.07825 10.0546 5.07825 9.87946C5.07825 9.70437 5.21861 9.56354 5.39312 9.56354C5.56762 9.56354 5.70799 9.70437 5.70799 9.87946C5.70799 10.0546 5.56762 10.1954 5.39312 10.1954ZM5.30207 7.9725C3.83016 7.98392 2.62759 6.80016 2.61621 5.32332C2.60483 3.84647 3.78463 2.63987 5.25655 2.62846C6.73226 2.62084 7.93862 3.81983 7.94241 5.30048C7.94241 6.76591 6.7664 7.96109 5.30207 7.9725ZM8.29142 1.81771C8.41661 1.69211 8.61388 1.69211 8.73907 1.81771C8.86425 1.94332 8.86425 2.14125 8.73907 2.26686C8.61388 2.39247 8.41661 2.39247 8.29142 2.26686C8.17003 2.14125 8.17003 1.93952 8.29142 1.81771ZM8.81873 8.72615C8.69354 8.85176 8.49628 8.85176 8.37109 8.72615C8.2459 8.60054 8.2459 8.40262 8.37109 8.27701C8.49628 8.1514 8.69354 8.1514 8.81873 8.27701C8.94013 8.39881 8.94013 8.60054 8.81873 8.72615ZM9.88852 5.57073C9.71402 5.57073 9.57366 5.42989 9.57366 5.2548C9.57366 5.07971 9.71402 4.93888 9.88852 4.93888C10.063 4.93888 10.2034 5.07971 10.2034 5.2548C10.2034 5.42989 10.063 5.57073 9.88852 5.57073Z" fill="currentColor"></path><path d="M11.148 5.41849C11.1291 6.36245 10.8901 7.25313 10.4766 8.04103C10.5486 8.03342 10.6207 8.02961 10.6966 8.02961C12.1723 8.022 13.3787 9.22098 13.3825 10.7016C13.3825 12.1671 12.2026 13.3622 10.7421 13.3737C9.2702 13.3851 8.06763 12.2013 8.05625 10.7245C8.05625 10.6445 8.06005 10.5646 8.06384 10.4847C7.28615 10.9072 6.39845 11.1584 5.45764 11.185C5.69284 13.8076 7.82484 15.8744 10.4728 16H10.7156C13.629 16.0038 15.9962 13.6363 16 10.7131C16 10.7092 16 10.7092 16 10.7054V10.4542C15.8748 7.75556 13.7618 5.62403 11.148 5.41849ZM7.82105 14.2034C7.69586 14.329 7.49859 14.329 7.37341 14.2034C7.24822 14.0778 7.24822 13.8799 7.37341 13.7543C7.49859 13.6287 7.69586 13.6287 7.82105 13.7543C7.94624 13.8799 7.94624 14.0816 7.82105 14.2034ZM10.8332 15.5965C10.6587 15.5965 10.5183 15.4557 10.5183 15.2806C10.5183 15.1055 10.6587 14.9647 10.8332 14.9647C11.0077 14.9647 11.148 15.1055 11.148 15.2806C11.148 15.4557 11.0077 15.5965 10.8332 15.5965ZM13.4052 7.26454C13.4052 7.08945 13.5456 6.94862 13.7201 6.94862C13.8946 6.94862 14.035 7.08945 14.035 7.26454C14.035 7.43963 13.8946 7.58047 13.7201 7.58047C13.5456 7.58047 13.4052 7.43963 13.4052 7.26454ZM14.2588 14.1273C14.1336 14.2529 13.9363 14.2529 13.8111 14.1273C13.6859 14.0017 13.6859 13.8038 13.8111 13.6782C13.9363 13.5526 14.1336 13.5526 14.2588 13.6782C14.384 13.8 14.384 14.0017 14.2588 14.1273ZM15.3286 10.9719C15.1541 10.9719 15.0137 10.831 15.0137 10.656C15.0137 10.4809 15.1541 10.34 15.3286 10.34C15.5031 10.34 15.6434 10.4809 15.6434 10.656C15.6472 10.831 15.5031 10.9719 15.3286 10.9719Z" fill="currentColor"></path></g><defs><clipPath id="clip0_1_329"><rect width="16" height="16" fill="white"></rect></clipPath></defs></svg>'
        },
        {
            text: 'Aviator',
            link: '/'+LANG+'/casino/games/aviator/',
            icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.27234 12.5031V13.8915C2.27234 14.5029 2.75475 15 3.3536 15H12.6469C13.2457 15 13.7281 14.5029 13.7281 13.8915V12.5031H2.27234Z" fill="currentColor"></path><path d="M15.1919 4.41822L11.5157 5.95521L8.48813 1.26997C8.25524 0.910009 7.74511 0.910009 7.51223 1.26997L4.4847 5.95521L0.808425 4.41822C0.342652 4.21824 -0.128666 4.67534 0.032137 5.16672L2.13311 11.6461H13.8667L15.9677 5.16672C16.129 4.67534 15.6577 4.21824 15.1919 4.41822ZM9.194 8.44067L8.19702 9.96337C8.10276 10.1068 7.8976 10.1068 7.80389 9.96337L6.80691 8.44067C6.75257 8.35782 6.75257 8.24983 6.80691 8.16698L7.80389 6.64428C7.89815 6.50087 8.10331 6.50087 8.19702 6.64428L9.194 8.16698C9.24778 8.24983 9.24778 8.35782 9.194 8.44067Z" fill="currentColor"></path></svg>'
        },
        {
            text: 'Promosyonlar',
            link: '/'+LANG+'/promotions/',
            icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.6494 3.02325C10.0205 1.28424 8.41265 0.15397 8.3451 0.106784C7.97805 -0.149551 7.44 0.084691 7.44 0.500521V2.6992C7.44 3.83453 5.75716 4.23408 5.1265 3.2473C4.93039 2.94125 4.45355 2.909 4.2067 3.17824C2.51196 5.02481 1 7.35307 1 9.75021C1 13.1963 4.1402 16 8 16C11.8598 16 15 13.1963 15 9.75021C15 7.29104 13.4096 4.90262 11.6494 3.02325ZM4.92 8.00027C4.92 7.44872 5.4226 7.0003 6.04 7.0003C6.6574 7.0003 7.16 7.44872 7.16 8.00027C7.16 8.55181 6.6574 9.00024 6.04 9.00024C5.4226 9.00024 4.92 8.55181 4.92 8.00027ZM6.48205 12.307C6.29319 12.5243 5.94123 12.5648 5.6963 12.3948C5.452 12.2251 5.40825 11.9111 5.59795 11.6933L9.51795 7.19342C9.708 6.9753 10.0598 6.93624 10.3037 7.10561C10.548 7.27529 10.5918 7.58934 10.4021 7.80715L6.48205 12.307ZM9.96 12.5001C9.3426 12.5001 8.84 12.0517 8.84 11.5002C8.84 10.9486 9.3426 10.5002 9.96 10.5002C10.5774 10.5002 11.08 10.9486 11.08 11.5002C11.08 12.0517 10.5774 12.5001 9.96 12.5001Z" fill="currentColor"></path></svg>'
        }
        // Add more menu items as needed
    ];

    // Add menu items to navbar
    menuItems.forEach(item => {
        navbar.appendChild(headMenuItem(item.text, item.icon, item.link));
    });
    document.querySelector("header").insertAdjacentElement('afterend', navbar);
    return navbar;
}

// DOM'un yüklenmesini bekler (dinamik çözüm)
function waitForDomLoad() {
    return new Promise((resolve) => {
        const observer = new MutationObserver(() => {
            const herobanner = document.querySelector('.homepage .section.pt-24 .container .row .col-12.col-lg-5.col-xxl-4.id-t-d');
            if (herobanner) {
                observer.disconnect();
                resolve();
            }
        });
        observer.observe(document.body, {childList: true, subtree: true});
    });
}

// Statik timeout (yedeği)
function staticTimeout() {
    return new Promise((resolve) => {
        setTimeout(resolve, 1500); // 1.5 saniyelik statik bekleme
    });
}

function homePageManipulation() {
    // Hero Banner Remover
    /*var herobanner = document.querySelector('.homepage .section.pt-24 .container .row .col-12.col-lg-5.col-xxl-4.id-t-d');
    if (herobanner) {
        herobanner.remove();
        writeToConsole('Hero banner removed.');
    } else {
        writeToConsole('Hero banner not found.');
    }*/

    // Slider full sizer
    /*var slider = document.querySelector('.homepage .container .row .col-12.col-lg-7.col-xxl-8');
    if (slider) {
        slider.classList.add('col-lg-12', 'col-xxl-12');
        slider.classList.remove('col-xxl-8', 'col-lg-7');
        writeToConsole('Slider resized to full width.');
    } else {
        writeToConsole('Slider not found.');
    }*/

    highlightBar();
    writeToConsole('Highlight added to the page.');
    bannerAfterHighlightBar();
    writeToConsole('Banner section initialization started.');
    bannerAfterFastAccess();
    writeToConsole('Fast Access initialization started.');
}

function highlightBar() {
    // Highlight Bar veri yapısı
    const highlightItems = [
        {
            borderColor: "rgb(255, 195, 0)",
            image: {
		link: "#",
                webp: "https://betrozgiris.com/img/FFC300.webp",
                jpg: "https://betrozgiris.com/img/deneme.jpg"
            },
            title: "Deneme"
        },
        {
            borderColor: "rgb(45, 246, 125)",
            image: {
		link: "#",
                webp: "https://betrozgiris.com/img/2DF67D.webp",
                jpg: "https://betrozgiris.com/img/turnuva.jpg"
            },
            title: "Turnuva"
        },
        {
            borderColor: "rgb(35, 229, 255)",
            image: {
		link: "#",
                webp: "https://betrozgiris.com/img/23E5FF.webp",
                jpg: "https://betrozgiris.com/img/extra-oran.jpg"
            },
            title: "Extra Oran"
        },
        {
            borderColor: "rgb(34, 158, 217)",
            image: {
		link: "https://t.me/betrozsosyal",
                webp: "https://betrozgiris.com/img/229ED9.webp",
                jpg: "https://betrozgiris.com/img/telegram.jpg"
            },
            title: "Telegram"
        },
        {
            borderColor: "rgb(255, 111, 249)",
            image: {
		link: "",
                webp: "https://betrozgiris.com/img/FF6FF9.webp",
                jpg: "https://betrozgiris.com/img/bonus-buy.jpg"
            },
            title: "Bonus Buy"
        }
    ];

    // HTML oluşturma fonksiyonu
    function createHighlightItemHTML(item) {
        return `
            <div class="highlightBar-item" style="border-color: ${item.borderColor};">
                <a href="${item.image.link}">
			<picture class="highlightBar-image">
                    <source srcset="${item.image.webp}">
                    <source srcset="${item.image.jpg}">
                    <img class="highlightBar-image" 
                         srcset="${item.image.jpg}" 
                         loading="lazy" 
                         alt="picture">
                </picture>
                <div class="highlightBar-title-wrapper" 
                     style="background: ${item.borderColor}; 
                            box-shadow: ${item.borderColor.replace('rgb', 'rgba').replace(')', ', 0.6)')} 0px 5px 6px 2px;">
                    <div class="highlightBar-title-text">${item.title}</div>
                </div>
		</a>
            </div>
        `;
    }

    // Ana yapıyı oluştur
    const homepageSection = document.querySelector('.homepage .section.pt-24');
    const highlightSection = document.querySelector('#highlightArea');
    if (homepageSection && !highlightSection) {
        const highlightSection = document.createElement('div');
        highlightSection.id = 'highlightArea';
        highlightSection.className = 'section highlight';

        const container = document.createElement('div');
        container.className = 'container';

        const row = document.createElement('div');
        row.className = 'row';

        const column = document.createElement('div');
        column.id = 'highlightBar';
        column.className = 'col-12 col-lg-12 col-xxl-12';

        // Highlight bar container
        const highlightBarContainer = document.createElement('div');
        highlightBarContainer.className = 'highlight-bar-container d-flex';

        // Highlight itemlerini oluştur
        highlightBarContainer.innerHTML = highlightItems.map(item => createHighlightItemHTML(item)).join('');

        // Hiyerarşiyi kur
        column.appendChild(highlightBarContainer);
        row.appendChild(column);
        container.appendChild(row);
        highlightSection.appendChild(container);

        // Homepage section'ından sonra ekle
        homepageSection.insertAdjacentElement('afterend', highlightSection);
        writeToConsole('Highlight bar added successfully');
    } else {
        writeToConsole('Homepage section not found');
    }
}

function bannerAfterHighlightBar() {
    // Banner verilerini tanımla
    const bannerItems = [
        {
            subtitle: "Hep Bağlantıda Kalın",
            title: "BetRoz Telegram!",
            bannerLink: "https://t.me/betrozsosyal",
            class: `banner-telegram`,

        },
        {
            subtitle: "Çok Kazandıran Size Özel",
            title: "Bonusları Keşfet!",
            bannerLink: '/'+LANG+"/promotions",
            bannerIcon: ``,
            class: `banner-bonus`,
        },
    ];

    // Banner HTML oluşturucu
    function createBannerItemHTML(item) {
        return `
            <div class="banner-skeleton col-6 col-lg-4 col-xxl-4 col-sm-6 col-md-6 ${item.class}">
                <div class="banner-content">
                    <div class="banner-space">
                        <div class="ellipsis banner-subtitle">${item.subtitle}</div>
                        <div class="ellipsis banner-title">${item.title}</div>
                    </div>
                    <div class="banner-wrapper">
                        <a href="${item.bannerLink}" rel="noreferrer" class="banner-link">
                            <span class="banner-link-wrapper" style="height: 8px; width: 8px; min-width: 8px;"><svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.55014 11.7822L11.7715 6.5562C12.0762 6.25127 12.0762 5.7587 11.7715 5.45612L6.55014 0.227816C6.24548 -0.0771102 5.75334 -0.0747646 5.44869 0.227816C5.14403 0.530396 5.14637 1.02531 5.44869 1.33024L9.34128 5.22625L0.778049 5.22625C0.349185 5.22625 0 5.57574 0 6.00498C0 6.43423 0.349185 6.78372 0.778049 6.78372L9.34362 6.78372L5.44869 10.6797C5.15106 10.9893 5.15809 11.4819 5.46744 11.7798C5.76975 12.073 6.24783 12.073 6.55014 11.7822Z" fill="currentColor"></path></svg></span>
                        </a>
                    </div>
                </div>
            </div>
        `;
    }

    // Banner bölümünü oluştur
    const highlightArea = document.querySelector('#highlightArea');
    const existingBannerSection = document.querySelector('#bannerSection');

    if (highlightArea && !existingBannerSection) {
        const bannerSection = document.createElement('div');
        bannerSection.id = 'bannerSection';
        bannerSection.className = 'section banner-section';

        const bannerContainer = document.createElement('div');
        bannerContainer.className = 'container';

        const bannerRow = document.createElement('div');
        bannerRow.className = 'row';

        // Banner itemlerini oluştur
        bannerRow.innerHTML = bannerItems.map(item => createBannerItemHTML(item)).join('');

        // Hiyerarşiyi kur
        bannerContainer.appendChild(bannerRow);
        bannerSection.appendChild(bannerContainer);

        // Highlight alanından sonra ekle
        highlightArea.insertAdjacentElement('afterend', bannerSection);
        writeToConsole('Banner section added successfully');
    } else {
        writeToConsole('Highlight area not found or banner section already exists');
    }
}

function bannerAfterFastAccess() {
    // Fast Access verilerini tanımla
    const fastAccessItems = [
        {
            title: "Boxes - casino",
            link: '/'+LANG+"/casino/group/lobby",
            image: "https://betrozgiris.com/img/fast-access/canli-casino.png"
        },
        {
            title: "Boxes - sportsbook",
            link: '/'+LANG+"/sportsbook",
            image: "https://betrozgiris.com/img/fast-access/spor-bahis.png"
        },
        {
            title: "Boxes - slot",
            link: '/'+LANG+"/casino/group/lobby",
            image: "https://betrozgiris.com/img/fast-access/slot-oyun.png"
        },
        {
            title: "Boxes - /games/aviator",
            link: '/'+LANG+"/games/aviator",
            image: "https://betrozgiris.com/img/fast-access/aviator.png"
        },
        {
            title: "Boxes - games/virtual",
            link: '/'+LANG+"/games/virtual",
            image: "https://betrozgiris.com/img/fast-access/sanal-spor.png"
        },
        {
            title: "Boxes - promotions",
            link: '/'+LANG+"/promotions",
            image: "https://betrozgiris.com/img/fast-access/bonus.png"
        }
    ];

    // Fast Access HTML oluşturucu
    function createFastAccessItemHTML(item) {
        return `
            <a href="${item.link}" class="">
                <img title="${item.title}" width="100%" src="${item.image}">
            </a>
        `;
    }

    // Fast Access bölümünü oluştur
    const homepageSection = document.querySelector('.homepage .section.pt-24');
    const existingFastAccess = document.querySelector('#fastAccessSection');

    if (homepageSection && !existingFastAccess) {
        const fastAccessSection = document.createElement('div');
        fastAccessSection.id = 'fastAccessSection';
        fastAccessSection.className = 'section fast-access-section';

        const fastAccessContainer = document.createElement('div');
        fastAccessContainer.className = 'container';

        const fastAccessRow = document.createElement('div');
        fastAccessRow.className = 'row';

        const fastAccessCol = document.createElement('div');
        fastAccessCol.className = 'col-12';

        const boxesDiv = document.createElement('div');
        boxesDiv.className = 'boxes';

        // Fast Access itemlerini oluştur
        boxesDiv.innerHTML = fastAccessItems.map(item => createFastAccessItemHTML(item)).join('');

        // Hiyerarşiyi kur
        fastAccessCol.appendChild(boxesDiv);
        fastAccessRow.appendChild(fastAccessCol);
        fastAccessContainer.appendChild(fastAccessRow);
        fastAccessSection.appendChild(fastAccessContainer);

        // Homepage section'dan sonra ekle
        homepageSection.insertAdjacentElement('afterend', fastAccessSection);
        writeToConsole('Fast Access section added successfully');
    } else {
        writeToConsole('Homepage section not found or Fast Access section already exists');
    }
}

// Route değişikliklerini algılamak ve yeniden path'i kontrol etmek
(function () {
    const pushState = history.pushState;
    const replaceState = history.replaceState;

    history.pushState = function () {
        pushState.apply(history, arguments);
        window.dispatchEvent(new Event('locationchange'));
    };

    history.replaceState = function () {
        replaceState.apply(history, arguments);
        window.dispatchEvent(new Event('locationchange'));
    };

    // Geri/ileri butonlarını dinleme
    window.addEventListener('popstate', () => {
        window.dispatchEvent(new Event('locationchange'));
    });

    // Route değişimlerini dinle
    window.addEventListener('locationchange', () => {
        writeToConsole(`Route changed to: ${window.location.pathname}`);
        readPath(); // Route değişiminde path'i yeniden kontrol et
    });

    // İlk yüklemede çalıştır
    writeToConsole(`Initial load path: ${window.location.pathname}`);
    readPath();
})();

// Konsol log temizleme
setInterval(() => {
    const logDiv = document.getElementById('debug-log');
    if (logDiv) logDiv.innerHTML = '';
}, 2000);

// Ekran genişliğine göre mobile class'ı ekleyen fonksiyon
// Sayfa yüklendiğinde çalışacak ana fonksiyon
const responsiveHandler = () => {
    // Mobile durumunu kontrol eden fonksiyon
    function checkMobile() {
        const body = document.querySelector('body');
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            body.classList.add('is-mobile');
            body.classList.remove('is-desktop');
        } else {
            body.classList.remove('is-mobile');
            body.classList.add('is-desktop');
        }
    }

    // İlk yüklemede kontrolleri yap
    checkMobile();

    // Resize olayını dinle
    window.onresize = checkMobile;
}

// DOMContentLoaded event'ini dinleyerek başlat
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', responsiveHandler);
} else {
    responsiveHandler();
}

function updateIndicatorPosition() {
    // Tüm olası active elementleri daha kapsamlı seç
    const activeElement = 
        document.querySelector('#tabbar nav ul li a.lowbar__btn.active') || 
        document.querySelector('#tabbar nav ul li.center button.active') ||
        document.querySelector('#tabbar nav ul li a.lowbar__btn[type="button"].active');

    const indicator = document.querySelector('#tabbar .indicator');
    
    if (activeElement && indicator) {
        const tabbarRect = document.querySelector('#tabbar').getBoundingClientRect();
        const elementRect = activeElement.getBoundingClientRect();
        
        const leftPosition = (elementRect.left - tabbarRect.left) + (elementRect.width / 2) - (indicator.offsetWidth / 2);
        
        indicator.style.transform = `translateX(${leftPosition}px)`;
        indicator.style.opacity = 1;
    }
}

document.addEventListener('DOMContentLoaded', () => {
    // Tüm tıklanabilir elementleri seç
    const allClickableElements = document.querySelectorAll('#tabbar nav ul li a.lowbar__btn, #tabbar nav ul li.center button');
    
    allClickableElements.forEach(element => {
        element.addEventListener('click', (e) => {
            // type="button" olan a elementi veya normal button ise default davranışı engelle
            if(element.getAttribute('type') === 'button' || element.tagName.toLowerCase() === 'button') {
                e.preventDefault();
            }
            
            // Tüm active classları kaldır
            document.querySelectorAll('#tabbar nav ul li a.active, #tabbar nav ul li button.active')
                .forEach(el => el.classList.remove('active'));
            
            // Yeni active class ekle
            element.classList.add('active');
            
            // Indicator pozisyonunu güncelle
            updateIndicatorPosition();
        });
    });
    
    // URL'e göre başlangıç active durumunu ayarla
    const currentPath = window.location.pathname;
    const matchingLink = document.querySelector(`#tabbar nav ul li a[href="${currentPath}"]`);
    
    if (matchingLink) {
        matchingLink.classList.add('active');
    } else {
        // Eğer URL eşleşmesi yoksa ilk menü öğesini active yap
        const firstButton = document.querySelector('#tabbar nav ul li a.lowbar__btn');
        if (firstButton) firstButton.classList.add('active');
    }
    
    // İlk yüklemede pozisyonu ayarla
    updateIndicatorPosition();
});

// Responsive için
window.addEventListener('resize', updateIndicatorPosition);

// Sayfa yönlendirmelerini takip etmek için
window.addEventListener('locationchange', updateIndicatorPosition);
