.carouselContainer {
  @apply w-[484px] h-[400px];
  @apply min-w-[378px] min-h-[314px];
}

.slide {
  @apply absolute top-0 left-0 w-full h-full bg-center bg-[length:100%_100%] bg-no-repeat;
  @apply transition-all duration-[400ms] ease-in-out;
  @apply rounded-xl;
}

.currentSlide {
  z-index: 20;
  transform: translateX(0) rotateY(0deg) scale(1);
  filter: blur(0);
  opacity: 1;
}

.slideLeft {
  z-index: 10;
  transform: rotateY(30deg) translateX(-35%) scale(0.8);
  filter: blur(4px);
}

.slideRight {
  z-index: 10;
  transform: rotateY(-30deg) translateX(35%) scale(0.8);
  filter: blur(4px);
}

.hiddenSlide {
  @apply hidden;
}
