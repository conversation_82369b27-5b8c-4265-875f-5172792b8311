<script>
  // Switch to enable/disable this script
  const custom0020active = true;

  if (custom0020active) {
    // Localization object for multiple languages
    const custom0020translations = {
      en: {
        casinoTitle: "Casino",
        casinoGames: "8,500+ GAMES",
        casinoDesc: "Enjoy BetFury Originals and other casino games from top providers.",
        casinoBtn: "Go to Casino",
        sportsTitle: "Sports",
        sportsFreeBets: "FREE BETS",
        sportsDesc: "Bet on popular sports events with high odds and other great features.",
        sportsBtn: "Go to Sports"
      },
      tr: {
        casinoTitle: "Casino",
        casinoGames: "8,500+ OYUN",
        casinoDesc: "BetFury Orijinalleri ve en iyi sağlayıcılardan diğer casino oyunlarının keyfini çıkarın.",
        casinoBtn: "Casino'ya Git",
        sportsTitle: "Spor",
        sportsFreeBets: "ÜCRETSİZ BAHİSLER",
        sportsDesc: "Yüksek oranlar ve diğer harika özelliklerle popüler spor etkinliklerine bahis yapın.",
        sportsBtn: "Spor'a Git"
      },
      de: {
        casinoTitle: "Casino",
        casinoGames: "8,500+ SPIELE",
        casinoDesc: "Genießen Sie BetFury Originals und andere Casino-Spiele von Top-Anbietern.",
        casinoBtn: "Zum Casino",
        sportsTitle: "Sport",
        sportsFreeBets: "GRATIS WETTEN",
        sportsDesc: "Setzen Sie auf beliebte Sportereignisse mit hohen Quoten und anderen großartigen Features.",
        sportsBtn: "Zum Sport"
      }
    };

    // Function to get current language
    function custom0020getCurrentLanguage() {
      return document.documentElement.lang || "en";
    }

    // Function to get translations for current language
    function custom0020getTranslations() {
      const lang = custom0020getCurrentLanguage();
      return custom0020translations[lang] || custom0020translations.en;
    }

    // Function to check if DOM is ready
    function custom0020isDomReady() {
      return document.readyState === 'complete' || document.readyState === 'interactive';
    }

    // Function to create the Betroz cards
    function custom0020createBetrozCards() {
      // Check if cards already exist to prevent duplicates
      if (document.getElementById('custom0020-betroz-cards')) {
        console.log('Betroz Cards: Cards already exist, skipping insertion');
        return;
      }

      const t = custom0020getTranslations();
      const lang = custom0020getCurrentLanguage();

      // Create the main container
      const container = document.createElement('div');
      container.id = 'custom0020-betroz-cards';

      // Add styles for the cards
      if (!document.getElementById('custom0020-betroz-styles')) {
        const style = document.createElement('style');
        style.id = 'custom0020-betroz-styles';
        style.textContent = `
          /* Main container for Betroz cards */
          #custom0020-betroz-cards {
            width: 100%;
            margin: 30px 0;
            padding: 0;
            box-sizing: border-box;
            perspective: 1000px;
          }

          /* Cards wrapper */
          .custom0020-cards-wrapper {
            display: flex;
            gap: 24px;
            width: 100%;
            box-sizing: border-box;
          }

          /* Individual card styling - Compact design without outline */
          .custom0020-card {
            flex: 1;
            min-height: 100px;
            border-radius: 13px;
            padding: 0;
            color: white;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 16px;
            box-sizing: border-box;
            background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
            transform-style: preserve-3d;
          }



          /* Content wrapper to sit above the background - Ultra Compact */
          .custom0020-card-content {
            position: relative;
            z-index: 1;
            padding: 22px;
            display: flex;
            align-items: center;
            gap: 16px;
            width: 100%;
            box-sizing: border-box;
          }

          /* Animated shimmer overlay */
          .custom0020-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent,
              rgba(255, 255, 255, 0.1),
              rgba(255, 255, 255, 0.2),
              rgba(255, 255, 255, 0.1),
              transparent
            );
            transition: left 0.6s ease;
            border-radius: 17px;
            z-index: 2;
          }

          /* Very subtle hover effects without outline */
          .custom0020-card:hover {
            transform: translateY(-4px) scale(1.005);
            background: linear-gradient(135deg, #071e2d 0%, #082030 30%, #092235 60%, #0a2438 80%, #0b253b 100%);
          }

          /* Subtle shimmer animation on hover */
          .custom0020-card:hover::after {
            left: 100%;
          }

          /* Icon container with subtle floating animation - No circle, larger */
          .custom0020-icon-container {
            width: 72px;
            height: 72px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
            animation: custom0020float 8s ease-in-out infinite;
          }

          /* Subtle floating animation for icons */
          @keyframes custom0020float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-2px); }
          }

          /* Subtle icon hover effect - No circle styling */
          .custom0020-card:hover .custom0020-icon-container {
            transform: scale(1.05);
          }

          /* Icon styling - 20% larger, no circle */
          .custom0020-icon {
            width: 43px;
            height: 43px;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
            transition: all 0.3s ease;
          }

          .custom0020-card:hover .custom0020-icon {
            filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4)) brightness(1.1);
          }

          /* Content area with enhanced typography - Compact */
          .custom0020-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
            position: relative;
            z-index: 2;
          }

          /* Clean title with solid white text - Compact */
          .custom0020-title {
            font-size: 22px;
            font-weight: 800;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #ffffff;
            letter-spacing: -0.3px;
            transition: all 0.3s ease;
          }

          .custom0020-card:hover .custom0020-title {
            transform: translateX(2px);
            color: #f8f8f8;
          }

          /* Compact badge without outline */
          .custom0020-badge {
            font-size: 9px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 9px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
            backdrop-filter: blur(10px);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            animation: custom0020pulse 3s ease-in-out infinite;
          }

          /* Subtle pulsing animation for badges */
          @keyframes custom0020pulse {
            0%, 100% { opacity: 0.9; }
            50% { opacity: 1; }
          }

          .custom0020-card:hover .custom0020-badge {
            transform: scale(1.02);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.35), rgba(255, 255, 255, 0.15));
          }

          /* Compact description with enhanced readability */
          .custom0020-description {
            font-size: 13px;
            line-height: 1.4;
            margin: 0;
            opacity: 0.9;
            font-weight: 400;
            color: #ffffff;
            transition: all 0.3s ease;
          }

          .custom0020-card:hover .custom0020-description {
            opacity: 1;
            transform: translateX(1px);
          }

          /* Button area with compact spacing */
          .custom0020-button-area {
            display: flex;
            align-items: center;
            margin-top: 8px;
          }

          /* Compact button with filling effect */
          .custom0020-button {
            font-size: 13px;
            font-weight: 700;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          /* Filling effect for button */
          .custom0020-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transition: width 0.3s ease;
            z-index: -1;
          }

          /* Knife shine effect for buttons - reverted to white */
          .custom0020-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent,
              rgba(255, 255, 255, 0.3),
              rgba(255, 255, 255, 0.5),
              rgba(255, 255, 255, 0.3),
              transparent
            );
            transition: left 0.4s ease;
            z-index: 0;
          }

          .custom0020-card:hover .custom0020-button {
            transform: translateX(2px) scale(1.005);
          }

          .custom0020-card:hover .custom0020-button::before {
            width: 100%;
          }

          .custom0020-card:hover .custom0020-button::after {
            left: 100%;
          }

          .custom0020-card:hover .custom0020-button-arrow {
            transform: translateX(2px) scale(1.05);
          }

          /* Enhanced responsive design - Compact */
          @media (max-width: 768px) {
            #custom0020-betroz-cards {
              margin: 16px 0;
              perspective: 800px;
            }

            .custom0020-cards-wrapper {
              flex-direction: column;
              gap: 16px;
            }

            .custom0020-card {
              min-height: 90px;
              border-radius: 11px;
            }

            .custom0020-card-content {
              padding: 12px;
              gap: 12px;
            }

            .custom0020-card:hover {
              transform: translateY(-3px) scale(1.003);
            }

            .custom0020-icon-container {
              width: 60px;
              height: 60px;
              animation: custom0020float 4s ease-in-out infinite;
            }

            .custom0020-icon {
              width: 36px;
              height: 36px;
            }

            .custom0020-title {
              font-size: 18px;
              gap: 10px;
            }

            .custom0020-description {
              font-size: 12px;
              line-height: 1.3;
            }

            .custom0020-button {
              font-size: 12px;
              padding: 6px 12px;
            }
          }

          @media (max-width: 480px) {
            #custom0020-betroz-cards {
              margin: 16px 0;
            }

            .custom0020-card {
              padding: 24px;
              gap: 16px;
              min-height: 120px;
              border-radius: 11px;
            }

            .custom0020-card:hover {
              transform: translateY(-4px) scale(1.01);
            }

            .custom0020-icon-container {
              width: 60px;
              height: 60px;
            }

            .custom0020-icon {
              width: 36px;
              height: 36px;
            }

            .custom0020-title {
              font-size: 20px;
              gap: 10px;
            }

            .custom0020-badge {
              font-size: 9px;
              padding: 5px 10px;
            }

            .custom0020-description {
              font-size: 13px;
            }

            .custom0020-button {
              font-size: 13px;
              padding: 10px 16px;
              gap: 6px;
            }

            .custom0020-button::after {
              font-size: 16px;
            }
          }

          /* Ultra-wide screen enhancements */
          @media (min-width: 1400px) {
            .custom0020-card-content {
              padding: 20px;
            }

            .custom0020-card {
              min-height: 160px;
            }

            .custom0020-icon-container {
              width: 90px;
              height: 90px;
            }

            .custom0020-icon {
              width: 54px;
              height: 54px;
            }

            .custom0020-title {
              font-size: 32px;
            }

            .custom0020-description {
              font-size: 16px;
            }

            .custom0020-button {
              font-size: 16px;
              padding: 14px 24px;
            }
          }

          /* Accessibility and performance optimizations */
          @media (prefers-reduced-motion: reduce) {
            .custom0020-card,
            .custom0020-icon-container,
            .custom0020-title,
            .custom0020-description,
            .custom0020-button,
            .custom0020-badge {
              animation: none !important;
              transition: none !important;
            }

            .custom0020-card:hover {
              transform: none !important;
            }
          }

          /* High contrast mode support */
          @media (prefers-contrast: high) {
            .custom0020-card {
              border: 2px solid white;
              background: #000 !important;
            }

            .custom0020-title,
            .custom0020-description {
              color: white !important;
              -webkit-text-fill-color: white !important;
            }

            .custom0020-badge {
              background: white !important;
              color: black !important;
            }
          }
        `;
        document.head.appendChild(style);
      }

      // Create the HTML structure
      container.innerHTML = `
        <div class="custom0020-cards-wrapper">
          <!-- Casino Card -->
          <a href="https://betroz.com/${lang}/casino" class="custom0020-card custom0020-casino-card">
            <div class="custom0020-card-content">
              <div class="custom0020-icon-container">
                <img src="https://i.ibb.co/ZCk0qvx/Chat-GPT-Image-May-31-2025-01-25-48-AM-1.png" alt="Casino" class="custom0020-icon" />
              </div>
              <div class="custom0020-content">
                <h3 class="custom0020-title">
                  ${t.casinoTitle}
                  <span class="custom0020-badge">${t.casinoGames}</span>
                </h3>
                <p class="custom0020-description">${t.casinoDesc}</p>
                <div class="custom0020-button-area">
                  <span class="custom0020-button">${t.casinoBtn}<span class="custom0020-button-arrow">→</span></span>
                </div>
              </div>
            </div>
          </a>

          <!-- Sports Card -->
          <a href="https://betroz.com/${lang}/sports" class="custom0020-card custom0020-sports-card">
            <div class="custom0020-card-content">
              <div class="custom0020-icon-container">
                <img src="https://i.ibb.co/Cp3jkgh7/sport3d-card.png" alt="Sports" class="custom0020-icon" />
              </div>
              <div class="custom0020-content">
                <h3 class="custom0020-title">
                  ${t.sportsTitle}
                  <span class="custom0020-badge">${t.sportsFreeBets}</span>
                </h3>
                <p class="custom0020-description">${t.sportsDesc}</p>
                <div class="custom0020-button-area">
                  <span class="custom0020-button">${t.sportsBtn}<span class="custom0020-button-arrow">→</span></span>
                </div>
              </div>
            </div>
          </a>
        </div>
      `;

      return container;
    }

    // Function to wait for target element and insert cards
    function custom0020waitForTargetElement() {
      // Check if cards already exist
      if (document.getElementById('custom0020-betroz-cards')) {
        console.log('Betroz Cards: Cards already exist, skipping insertion');
        return;
      }

      let attempts = 0;
      const maxAttempts = 25;
      let intervalId;

      function custom0020tryInsert() {
        const targetElement = document.getElementById('main-slider-swiper');

        if (targetElement) {
          clearInterval(intervalId);
          console.log('Betroz Cards: Target element found, inserting cards');

          const cardsElement = custom0020createBetrozCards();
          if (cardsElement) {
            targetElement.after(cardsElement);
            console.log('Betroz Cards: Cards inserted successfully');
          }
          return;
        }

        attempts++;
        if (attempts >= maxAttempts) {
          clearInterval(intervalId);
          console.log('Betroz Cards: Max attempts reached, target element not found');
        }
      }

      intervalId = setInterval(custom0020tryInsert, 200);
    }

    // Function to update language when it changes
    function custom0020updateElementLanguage() {
      const existingCards = document.getElementById('custom0020-betroz-cards');
      if (existingCards) {
        console.log('Betroz Cards: Language changed, updating content');
        const t = custom0020getTranslations();
        const lang = custom0020getCurrentLanguage();

        // Update casino card content
        const casinoTitle = existingCards.querySelector('.custom0020-casino-card .custom0020-title');
        const casinoDesc = existingCards.querySelector('.custom0020-casino-card .custom0020-description');
        const casinoBtn = existingCards.querySelector('.custom0020-casino-card .custom0020-button');
        const casinoLink = existingCards.querySelector('.custom0020-casino-card');

        if (casinoTitle && casinoDesc && casinoBtn && casinoLink) {
          casinoTitle.innerHTML = `${t.casinoTitle} <span class="custom0020-badge">${t.casinoGames}</span>`;
          casinoDesc.textContent = t.casinoDesc;
          casinoBtn.textContent = t.casinoBtn;
          casinoLink.href = `https://betroz.com/${lang}/casino`;
        }

        // Update sports card content
        const sportsTitle = existingCards.querySelector('.custom0020-sports-card .custom0020-title');
        const sportsDesc = existingCards.querySelector('.custom0020-sports-card .custom0020-description');
        const sportsBtn = existingCards.querySelector('.custom0020-sports-card .custom0020-button');
        const sportsLink = existingCards.querySelector('.custom0020-sports-card');

        if (sportsTitle && sportsDesc && sportsBtn && sportsLink) {
          sportsTitle.innerHTML = `${t.sportsTitle} <span class="custom0020-badge">${t.sportsFreeBets}</span>`;
          sportsDesc.textContent = t.sportsDesc;
          sportsBtn.textContent = t.sportsBtn;
          sportsLink.href = `https://betroz.com/${lang}/sports`;
        }
      }
    }

    // Main initialization function
    function custom0020init() {
      console.log('Betroz Cards: Initializing script');

      // Set up language change observer
      const languageObserver = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
            custom0020updateElementLanguage();
            break;
          }
        }
      });

      languageObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['lang']
      });

      // Set up navigation event listeners for SPA
      window.addEventListener('hashchange', () => {
        if (!document.getElementById('custom0020-betroz-cards')) {
          custom0020waitForTargetElement();
        }
      });

      window.addEventListener('popstate', () => {
        if (!document.getElementById('custom0020-betroz-cards')) {
          custom0020waitForTargetElement();
        }
      });

      // Check if target element already exists
      if (document.getElementById('main-slider-swiper')) {
        custom0020waitForTargetElement();
      } else {
        // Use MutationObserver to watch for target element
        const bodyObserver = new MutationObserver((mutations) => {
          for (const mutation of mutations) {
            if (mutation.type === 'childList' && mutation.addedNodes.length) {
              for (const node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  if (node.id === 'main-slider-swiper' || node.querySelector('#main-slider-swiper')) {
                    bodyObserver.disconnect();
                    custom0020waitForTargetElement();
                    return;
                  }
                }
              }
            }
          }
        });

        bodyObserver.observe(document.body, { childList: true, subtree: true });
      }
    }

    // Initialize when DOM is ready
    if (custom0020isDomReady()) {
      custom0020init();
    } else {
      document.addEventListener('DOMContentLoaded', custom0020init);
    }
  }
</script>
