<script>
  // Switch to enable/disable this script
  const betroz0003active = true;

  if (betroz0003active) {
    // Flag to track if styles have been applied
    let betroz0003stylesApplied = false;

    // Social media icons (using SVG data URLs)
    const betroz0003socialIcons = [
      // Facebook icon
      `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="26" height="26" class="social-icon"><path d="M12.001 2.002c-5.522 0-9.999 4.477-9.999 9.999 0 4.99 3.656 9.126 8.437 9.879v-6.988h-2.54v-2.891h2.54V9.798c0-2.508 1.493-3.891 3.776-3.891 1.094 0 2.24.195 2.24.195v2.459h-1.264c-1.24 0-1.628.772-1.628 1.563v1.875h2.771l-.443 2.891h-2.328v6.988C18.344 21.129 22 16.992 22 12.001c0-5.522-4.477-9.999-9.999-9.999z"/></svg>`,
      // Twitter/X icon
      `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22" class="social-icon"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/></svg>`,
      // Instagram icon
      `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="26" height="26" class="social-icon"><path d="M12 2c2.717 0 3.056.01 4.122.06 1.065.05 1.79.217 2.428.465.66.254 1.216.598 1.772 1.153a4.908 4.908 0 0 1 1.153 1.772c.247.637.415 1.363.465 2.428.047 1.066.06 1.405.06 4.122 0 2.717-.01 3.056-.06 4.122-.05 1.065-.218 1.79-.465 2.428a4.883 4.883 0 0 1-1.153 1.772 4.915 4.915 0 0 1-1.772 1.153c-.637.247-1.363.415-2.428.465-1.066.047-1.405.06-4.122.06-2.717 0-3.056-.01-4.122-.06-1.065-.05-1.79-.218-2.428-.465a4.89 4.89 0 0 1-1.772-1.153 4.904 4.904 0 0 1-1.153-1.772c-.248-.637-.415-1.363-.465-2.428C2.013 15.056 2 14.717 2 12c0-2.717.01-3.056.06-4.122.05-1.066.217-1.79.465-2.428a4.88 4.88 0 0 1 1.153-1.772A4.897 4.897 0 0 1 5.45 2.525c.638-.248 1.362-.415 2.428-.465C8.944 2.013 9.283 2 12 2zm0 1.802c-2.67 0-2.986.01-4.04.059-.976.045-1.505.207-1.858.344-.466.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.048 1.055-.058 1.37-.058 4.04 0 2.67.01 2.986.058 4.04.045.976.207 1.505.344 1.858.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.04.058 2.67 0 2.987-.01 4.04-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.04 0-2.67-.01-2.986-.058-4.04-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 0 0-.748-1.15 3.098 3.098 0 0 0-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.054-.048-1.37-.058-4.04-.058zm0 3.063a5.135 5.135 0 1 1 0 10.27 5.135 5.135 0 0 1 0-10.27zm0 8.468a3.333 3.333 0 1 0 0-6.666 3.333 3.333 0 0 0 0 6.666zm6.538-8.469a1.2 1.2 0 1 1-2.4 0 1.2 1.2 0 0 1 2.4 0z"/></svg>`,
      // Telegram icon
      `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="social-icon"><path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12a12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/></svg>`
    ];

    // Social media URLs - actual Betroz social media links
    const betroz0003socialUrls = [
      'https://facebook.com/betrozdotcom', // Facebook
      'https://x.com/betrozdotcom', // Twitter/X
      'https://instagram.com/betrozdotcom', // Instagram
      'https://t.me/betrozdotcom'  // Telegram
    ];

    // Function to apply enhanced active element styling
    function betroz0003applyActiveStyling() {
      // Check if styles are already applied
      if (betroz0003stylesApplied || document.getElementById('betroz0003-active-styles')) {
        return;
      }

      // Create and inject styles
      const style = document.createElement('style');
      style.id = 'betroz0003-active-styles';
      style.textContent = `
        /* Enhanced Active Navigation Styling - Betroz Design with Soft Aqua */
        .sidebar__nav li.active {
          position: relative !important;
          background: linear-gradient(135deg, var(--tf-active-opacity) 0%, rgba(71, 140, 245, 0.15) 50%, var(--tf-active-opacity) 100%) !important;
          border-radius: 0px !important;
          border: 1px solid var(--tf-border) !important;
          box-shadow:
            0 8px 32px rgba(71, 140, 245, 0.2) !important,
            0 0 0 1px rgba(71, 140, 245, 0.1) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
          transform: translateX(4px) !important;
          transition: none !important;
          overflow: hidden !important;
        }

        /* Active element glow effect */
        .sidebar__nav li.active::before {
          content: '' !important;
          position: absolute !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
          background: linear-gradient(45deg, transparent 30%, rgba(71, 140, 245, 0.1) 50%, transparent 70%) !important;
          opacity: 0 !important;
          animation: betroz0003-glow-pulse 2s ease-in-out infinite !important;
          pointer-events: none !important;
          z-index: 1 !important;
        }

        /* Reset/wipe active link styles */
        .sidebar__nav li.active > a {
          background: none !important;
          border: none !important;
          box-shadow: none !important;
          text-shadow: none !important;
          transform: none !important;
          filter: none !important;
          padding: 12px 16px !important;
          margin: 0 !important;
          border-radius: 0 !important;
          color: inherit !important;
          font-weight: inherit !important;
          text-decoration: none !important;
          outline: none !important;
          position: static !important;
          z-index: auto !important;
          transition: 0.3s !important;
        }

        /* Active SVG icon styling */
        .sidebar__nav li.active > a .svg-icon {
          filter: drop-shadow(0 0 4px rgba(71, 140, 245, 0.4)) !important;
          transition: 0.3s !important;
        }

        /* Hover enhancement for active element */
        .sidebar__nav li.active:hover {
          transform: translateX(6px) scale(1.02) !important;
          box-shadow:
            0 12px 48px rgba(71, 140, 245, 0.3) !important,
            0 0 0 1px rgba(71, 140, 245, 0.2) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
          transition: 0.3s !important;
        }

        .sidebar__nav li.active:hover::before {
          opacity: 1 !important;
        }

        /* Force 0.3s transitions for all sidebar navigation items */
        .sidebar__nav li,
        .sidebar__nav li > a,
        .sidebar__nav li::before,
        .sidebar__nav li::after {
          transition: 0.3s !important;
        }

        /* Selection animations */
        .sidebar__nav li.active:active {
          transform: translateX(2px) scale(0.98) !important;
          transition: 0.3s !important;
        }

        .sidebar__nav li.active:active::before {
          opacity: 0.8 !important;
          transition: 0.3s !important;
        }

        /* Non-active items hover and click */
        .sidebar__nav li:not(.active) {
          transition: 0.3s !important;
        }

        .sidebar__nav li:not(.active):hover {
          transform: translateX(3px) !important;
          transition: 0.3s !important;
        }

        .sidebar__nav li:not(.active):active {
          transform: translateX(1px) scale(0.98) !important;
          transition: 0.3s !important;
        }

        /* Smooth transition when becoming active */
        .sidebar__nav li.active {
          animation: betroz0003-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        @keyframes betroz0003-slide-in {
          0% {
            transform: translateX(0) scale(1) !important;
            opacity: 0.8 !important;
          }
          50% {
            transform: translateX(6px) scale(1.01) !important;
          }
          100% {
            transform: translateX(4px) scale(1) !important;
            opacity: 1 !important;
          }
        }

        /* Left border accent */
        .sidebar__nav li.active::after {
          content: '' !important;
          position: absolute !important;
          left: 0 !important;
          top: 20% !important;
          bottom: 20% !important;
          width: 3px !important;
          background: linear-gradient(to bottom, transparent, var(--tf-active), transparent) !important;
          border-radius: 0 2px 2px 0 !important;
          z-index: 3 !important;
        }

        /* Glow pulse animation */
        @keyframes betroz0003-glow-pulse {
          0%, 100% {
            opacity: 0 !important;
            transform: translateX(-100%) !important;
          }
          50% {
            opacity: 0.6 !important;
            transform: translateX(100%) !important;
          }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .sidebar__nav li.active {
            transform: translateX(2px) !important;
            border-radius: 8px !important;
          }

          .sidebar__nav li.active:hover {
            transform: translateX(3px) scale(1.01) !important;
          }


        }

        /* Dark theme adjustments - using soft aqua accent */
        [data-bs-theme="dark"] .sidebar__nav li.active {
          background: linear-gradient(135deg, var(--tf-active-opacity) 0%, rgba(71, 140, 245, 0.2) 50%, var(--tf-active-opacity) 100%) !important;
          border-color: var(--tf-borderhover) !important;
          box-shadow:
            0 8px 32px rgba(71, 140, 245, 0.2) !important,
            0 0 0 1px rgba(71, 140, 245, 0.1) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        }

        [data-bs-theme="dark"] .sidebar__nav li.active::before {
          background: linear-gradient(45deg, transparent 30%, rgba(71, 140, 245, 0.1) 50%, transparent 70%) !important;
        }

        [data-bs-theme="dark"] .sidebar__nav li.active::after {
          background: linear-gradient(to bottom, transparent, var(--tf-active), transparent) !important;
        }



        [data-bs-theme="dark"] .sidebar__nav li.active > a .svg-icon {
          filter: drop-shadow(0 0 4px rgba(71, 140, 245, 0.4)) !important;
        }

        [data-bs-theme="dark"] .sidebar__nav li.active:hover {
          box-shadow:
            0 12px 48px rgba(71, 140, 245, 0.3) !important,
            0 0 0 1px rgba(71, 140, 245, 0.2) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
        }

        /* Force 0.3s transitions - highest specificity */
        .sidebar__big .sidebar__nav li,
        .sidebar__big .sidebar__nav li > a,
        .sidebar__big .sidebar__nav li::before,
        .sidebar__big .sidebar__nav li::after {
          transition: 0.3s !important;
          border: none !important;
          animation-fill-mode: both !important;
          animation-timing-function: none !important;
        }

        /* Override any existing transitions */
        .sidebar__big .sidebar__nav li.active {
          transition: 0.3s !important;
        }

        .sidebar__big .sidebar__nav li.active:hover {
          transition: 0.3s !important;
        }

        .sidebar__big .sidebar__nav li:not(.active):hover {
          transition: 0.3s !important;
        }

        /* Sidebar background gradient - compatible approach */
        .sidebar__big {
          background-image:
            linear-gradient(
              135deg,
              rgba(15, 23, 42, 0.4) 0%,
              rgba(30, 41, 59, 0.3) 25%,
              rgba(51, 65, 85, 0.25) 50%,
              rgba(30, 41, 59, 0.3) 75%,
              rgba(15, 23, 42, 0.4) 100%
            ),
            radial-gradient(circle at 30% 30%, rgba(24, 207, 251, 0.08) 0%, transparent 60%),
            radial-gradient(circle at 70% 70%, rgba(71, 140, 245, 0.06) 0%, transparent 60%) !important;
          background-blend-mode: overlay !important;
        }

        /* Ensure gradient works with existing background */
        .sidebar__big {
          background-color: var(--tf-bg2) !important;
        }

        /* Small sidebar gradient - same as big sidebar */
        .sidebar__small {
          background-image:
            linear-gradient(
              135deg,
              rgba(15, 23, 42, 0.4) 0%,
              rgba(30, 41, 59, 0.3) 25%,
              rgba(51, 65, 85, 0.25) 50%,
              rgba(30, 41, 59, 0.3) 75%,
              rgba(15, 23, 42, 0.4) 100%
            ),
            radial-gradient(circle at 30% 30%, rgba(24, 207, 251, 0.08) 0%, transparent 60%),
            radial-gradient(circle at 70% 70%, rgba(71, 140, 245, 0.06) 0%, transparent 60%) !important;
          background-blend-mode: overlay !important;
          background-color: var(--tf-bg2) !important;
        }

        /* Dark theme sidebar gradient - compatible approach */
        [data-bs-theme="dark"] .sidebar__big {
          background-image:
            linear-gradient(
              135deg,
              rgba(2, 6, 23, 0.5) 0%,
              rgba(15, 23, 42, 0.4) 25%,
              rgba(30, 41, 59, 0.35) 50%,
              rgba(15, 23, 42, 0.4) 75%,
              rgba(2, 6, 23, 0.5) 100%
            ),
            radial-gradient(circle at 30% 30%, rgba(24, 207, 251, 0.1) 0%, transparent 60%),
            radial-gradient(circle at 70% 70%, rgba(71, 140, 245, 0.08) 0%, transparent 60%) !important;
          background-blend-mode: overlay !important;
          background-color: var(--tf-bg2) !important;
        }

        /* Dark theme small sidebar gradient - same as big sidebar */
        [data-bs-theme="dark"] .sidebar__small {
          background-image:
            linear-gradient(
              135deg,
              rgba(2, 6, 23, 0.5) 0%,
              rgba(15, 23, 42, 0.4) 25%,
              rgba(30, 41, 59, 0.35) 50%,
              rgba(15, 23, 42, 0.4) 75%,
              rgba(2, 6, 23, 0.5) 100%
            ),
            radial-gradient(circle at 30% 30%, rgba(24, 207, 251, 0.1) 0%, transparent 60%),
            radial-gradient(circle at 70% 70%, rgba(71, 140, 245, 0.08) 0%, transparent 60%) !important;
          background-blend-mode: overlay !important;
          background-color: var(--tf-bg2) !important;
        }



        /* Social media icon styling */
        .social-icon {
          fill: #18cffb;
          transition: fill 0.3s ease !important;
          z-index: 2 !important;
          position: relative !important;
        }

        /* Sidebar content padding fix */
        .sidebar__content {
          padding-bottom: 0px !important;
        }
      `;

      document.head.appendChild(style);
      betroz0003stylesApplied = true;
      console.log('Betroz Active Styling: Enhanced active element styles applied successfully');
    }

    // Add CSS for the social icon if not already added (redundant but for exact compatibility)
    function betroz0003addSocialIconStyles() {
      if (!document.getElementById('betroz0003-social-icon-style')) {
        const style = document.createElement('style');
        style.id = 'betroz0003-social-icon-style';
        style.textContent = `
          .social-icon {
            fill: #18cffb;
            transition: fill 0.3s ease;
            z-index: 2;
            position: relative;
          }
        `;
        document.head.appendChild(style);
      }
    }

    // Function to create a social media link
    function betroz0003createSocialLink(icon, url) {
      const socialLink = document.createElement('a');
      socialLink.href = url;
      socialLink.target = '_blank';
      socialLink.rel = 'noopener noreferrer';

      // Add native CSS styles with blue color scheme
      socialLink.setAttribute('style', `
        display: flex;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background-color: transparent;
        border: 2px solid #18cffb;
        transition: all 0.3s ease;
        box-shadow: none;
        position: relative;
        overflow: hidden;
      `);

      // Set the icon first
      socialLink.innerHTML = icon;

      // Create gradient overlay element
      const gradientOverlay = document.createElement('div');
      gradientOverlay.setAttribute('style', `
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0%;
        background: linear-gradient(to bottom, #18cffb, #0ac3c6);
        transition: height 0.3s ease;
        z-index: 1;
      `);
      socialLink.appendChild(gradientOverlay);

      // Add hover effect
      socialLink.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = 'none';
        this.style.borderColor = '#18cffb';
        this.querySelector('div').style.height = '100%';
        this.querySelector('.social-icon').style.fill = '#000000';
      });

      socialLink.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'none';
        this.style.borderColor = '#18cffb';
        this.querySelector('div').style.height = '0%';
        this.querySelector('.social-icon').style.fill = '#18cffb';
      });

      return socialLink;
    }

    // Function to create and insert social media links in the sidebar
    function betroz0003renderSocialMediaLinks() {
      // Check if the element already exists to avoid duplicates
      if (document.getElementById('betroz0003-social-media-links')) {
        return;
      }

      // Add the social icon styles
      betroz0003addSocialIconStyles();

      // Create the container for social media links
      const socialMediaContainer = document.createElement('div');
      socialMediaContainer.id = 'betroz0003-social-media-links';

      // Add native CSS styles based on screen size
      const isMobile = window.innerWidth <= 768;

      socialMediaContainer.setAttribute('style', `
        display: flex;
        justify-content: ${isMobile ? 'flex-start' : 'space-between'};
        padding: 0 16px;
        margin: 30px 0;
        width: 100%;
        box-sizing: border-box;
        ${isMobile ? 'gap: 10px;' : ''}
      `);

      // Add resize listener to update styles on window resize
      window.addEventListener('resize', function() {
        const isMobileNow = window.innerWidth <= 768;
        socialMediaContainer.style.justifyContent = isMobileNow ? 'flex-start' : 'space-between';
        socialMediaContainer.style.gap = isMobileNow ? '10px' : '';
      });

      // Create social media links for sidebar
      betroz0003socialIcons.forEach((icon, index) => {
        const socialLink = betroz0003createSocialLink(icon, betroz0003socialUrls[index]);
        socialMediaContainer.appendChild(socialLink);
      });

      // Find the sidebar__big element
      const sidebar = document.querySelector('.sidebar__big');
      if (sidebar) {
        // Get all children of the sidebar
        const children = Array.from(sidebar.children);

        // Insert as the second last child
        if (children.length > 0) {
          sidebar.insertBefore(socialMediaContainer, children[children.length - 1]);
        } else {
          // If there are no children, just append it
          sidebar.appendChild(socialMediaContainer);
        }
      }
    }

    // Function to check if DOM is ready
    function betroz0003isDomReady() {
      return document.readyState === 'complete' || document.readyState === 'interactive';
    }

    // Function to wait for sidebar elements and apply styling
    function betroz0003waitForSidebar() {
      let attempts = 0;
      const maxAttempts = 50;

      function checkForSidebar() {
        const sidebar = document.querySelector('.sidebar__nav');
        const activeElement = document.querySelector('.sidebar__nav li.active');

        if (sidebar) {
          betroz0003applyActiveStyling();
          betroz0003renderSocialMediaLinks();
          console.log('Betroz Active Styling: Sidebar found, active element styled, and social media links added');
          return;
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(checkForSidebar, 200);
        } else {
          console.log('Betroz Active Styling: Max attempts reached, sidebar not found');
        }
      }

      checkForSidebar();
    }

    // Initialize the script
    function betroz0003init() {
      if (betroz0003isDomReady()) {
        betroz0003waitForSidebar();
      } else {
        document.addEventListener('DOMContentLoaded', betroz0003waitForSidebar);
      }

      // Also apply styles when navigation changes (for SPAs)
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && 
              (mutation.attributeName === 'class' || mutation.attributeName === 'data-bs-theme')) {
            // Re-apply styles if theme changes or classes change
            if (!betroz0003stylesApplied) {
              betroz0003applyActiveStyling();
            }
          }
        });
      });

      // Observe changes to the document and body for theme changes
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-bs-theme', 'class']
      });

      observer.observe(document.body, {
        attributes: true,
        childList: true,
        subtree: true,
        attributeFilter: ['class']
      });

      // Also listen for any changes to the sidebar structure for social media links
      const socialObserver = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList' &&
              (mutation.target.classList.contains('sidebar__big') ||
               document.querySelector('.sidebar__big'))) {
            betroz0003renderSocialMediaLinks();
            break;
          }
        }
      });

      // Start observing the document body for sidebar changes
      socialObserver.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    // Start the script
    betroz0003init();

    console.log('Betroz Active Styling: Script loaded and ready');
  }
</script>
