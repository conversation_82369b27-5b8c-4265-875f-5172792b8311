<script>
  // ========================================
  // BETROZ HEADER ENHANCEMENT SCRIPT
  // ========================================
  // This script enhances the header by replacing SVG icons and setting
  // all border radii to 4px for wallet, notifications, profile, and chat elements.
  //
  // Author: Betroz Enhancement Team
  // Type: Modificational Script (modifies existing header elements)
  // ========================================

  // REGULATION 1: Active Switch
  const betroz0001active = true;

  // REGULATION 2: All code must be wrapped in the active check
  if (betroz0001active) {

    // ========================================
    // CUSTOM SVG ICONS
    // ========================================
    const betroz0001icons = {
      wallet: `<svg viewBox="0 0 24 24" fill="currentColor" class="svg-icon">
        <path d="M21 7.28V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-2.28c.59-.35 1-.98 1-1.72V9c0-.74-.41-1.37-1-1.72zM20 9v6h-7V9h7zM5 19V5h14v2H9c-.55 0-1 .45-1 1v8c0 .55.45 1 1 1h10v2H5z"/>
        <circle cx="16" cy="12" r="1.5"/>
      </svg>`,
      
      notifications: `<svg viewBox="0 0 24 24" fill="currentColor" class="svg-icon">
        <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
      </svg>`,

      profile: `<svg viewBox="0 0 24 24" fill="currentColor" class="svg-icon" title="Me">
        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        <circle cx="12" cy="8" r="3" fill="none" stroke="currentColor" stroke-width="1"/>
      </svg>`,
      
      chat: `<svg viewBox="0 0 24 24" fill="currentColor" class="svg-icon">
        <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
        <circle cx="8" cy="10" r="1"/>
        <circle cx="12" cy="10" r="1"/>
        <circle cx="16" cy="10" r="1"/>
      </svg>`
    };

    // ========================================
    // DOM READINESS CHECK
    // ========================================
    function betroz0001isDomReady() {
      return document.readyState === 'complete' || document.readyState === 'interactive';
    }

    // ========================================
    // ICON REPLACEMENT FUNCTIONS
    // ========================================
    function betroz0001replaceIcons() {
      console.log('Betroz Header: Starting icon replacement');

      // Replace wallet icons
      const walletIcons = document.querySelectorAll('svg use[href*="#wallet"], svg use[xlink\\:href*="#wallet"]');
      walletIcons.forEach(use => {
        const svg = use.closest('svg');
        if (svg) {
          svg.outerHTML = betroz0001icons.wallet;
        }
      });

      // Replace notification icons
      const notificationIcons = document.querySelectorAll('svg use[href*="#notifications"], svg use[xlink\\:href*="#notifications"]');
      notificationIcons.forEach(use => {
        const svg = use.closest('svg');
        if (svg) {
          svg.outerHTML = betroz0001icons.notifications;
        }
      });

      // Replace profile icons
      const profileIcons = document.querySelectorAll('svg use[href*="#profile"], svg use[xlink\\:href*="#profile"]');
      profileIcons.forEach(use => {
        const svg = use.closest('svg');
        if (svg) {
          svg.outerHTML = betroz0001icons.profile;
        }
      });

      // Replace chat icons
      const chatIcons = document.querySelectorAll('svg use[href*="#chat"], svg use[xlink\\:href*="#chat"]');
      chatIcons.forEach(use => {
        const svg = use.closest('svg');
        if (svg) {
          svg.outerHTML = betroz0001icons.chat;
        }
      });

      console.log('Betroz Header: Icon replacement completed');
    }

    // ========================================
    // BORDER RADIUS STYLING
    // ========================================
    function betroz0001applyBorderRadiusStyles() {
      // Check if styles already applied to prevent duplicates
      if (document.getElementById('betroz0001-header-styles')) {
        return;
      }

      console.log('Betroz Header: Applying border radius styles');

      const style = document.createElement('style');
      style.id = 'betroz0001-header-styles';
      style.textContent = `
        /* Betroz Header Enhancement - Border Radius 4px */

        /* CSS Variable Override */
        :root {
          --tf-btn2: #3f96ff12 !important;
        }

        /* Animation Keyframes */
        @keyframes betroz0001fadeInDown {
          0% {
            opacity: 0;
            transform: translateY(-10px);
          }
          100% {
            opacity: 1;
            transform: translateY(0px);
          }
        }

        @keyframes betroz0001fadeInDownDropdown {
          0% {
            opacity: 0;
            transform: translateY(70px);
          }
          100% {
            opacity: 1;
            transform: translateY(80px);
          }
        }

        /* Header elements */
        .header,
        .header__content,
        .header__logo,
        .header__search,
        .header__search-btn,
        .header__search-btnmob,
        .header__actions,
        .header__wallet,
        .header__wallet-dropdown,
        .header__wallet-dropdown-btn,
        .header__wallet-btn,
        .header__action,
        .header__chat {
          border-radius: 4px !important;
          transition: all 0.3s !important;
          animation: betroz0001fadeInDown 0.4s ease-out both !important;
        }

        /* Dropdown menus */
        .dropdown-menu,
        .header__wallet-menu,
        .header__notifications,
        .header__user-menu {
          border-radius: 8px !important;
          transition: all 0.3s !important;
          animation: betroz0001fadeInDownDropdown 0.4s ease-out both !important;
        }
        
        /* Search elements */
        .search__input,
        .header__wallet-search .search,
        .header__wallet-search .search__input {
          border-radius: 4px !important;
          transition: all 0.3s !important;
        }
        
        /* Currency list items */
        .header__wallet-currencies li,
        .header__wallet-currencies li span {
          border-radius: 4px !important;
          transition: all 0.3s !important;
        }
        
        /* Instrument icon wrappers */
        .instrument-icon-wrapper,
        .instrument-icon-wrapper svg {
          border-radius: 4px !important;
          transition: all 0.3s !important;
        }
        
        /* Tab navigation */
        .nav-tabs,
        .tabs-nav,
        .tabs-nav__btn,
        .nav-item {
          border-radius: 4px !important;
          transition: all 0.3s !important;
        }
        
        /* SVG icons */
        .svg-icon,
        .big-icon,
        .mini-icon {
          border-radius: 4px !important;
          transition: all 0.3s !important;
        }
        
        /* Buttons and links */
        .header__dropdown-btn,
        .header button,
        .header a,
        .dropdown-menu a,
        .dropdown-menu button {
          border-radius: 4px !important;
          transition: all 0.3s !important;
        }
        
        /* Enhanced hover effects with 4px radius */
        .header__action:hover,
        .header__wallet-btn:hover,
        .header__chat:hover,
        .header__search-btn:hover,
        .header__search-btnmob:hover {
          border-radius: 4px !important;
          transform: scale(1.05) !important;
          transition: all 0.3s !important;
        }
        
        /* Dropdown items hover */
        .dropdown-menu a:hover,
        .dropdown-menu button:hover,
        .header__wallet-currencies li:hover {
          border-radius: 4px !important;
          background-color: rgba(71, 140, 245, 0.1) !important;
          transition: all 0.3s !important;
        }
        
        /* Tab buttons hover */
        .tabs-nav__btn:hover {
          border-radius: 4px !important;
          background-color: rgba(71, 140, 245, 0.15) !important;
          transition: all 0.3s !important;
        }
        
        /* Active states */
        .tabs-nav__btn.active {
          border-radius: 4px !important;
          background-color: rgba(71, 140, 245, 0.2) !important;
          transition: all 0.3s !important;
        }

        /* Header Signup Button - Glowy Button Styling */
        .header__signup,
        button.header__signup {
          /* Wipe existing styles */
          all: unset !important;

          /* Base button styling */
          display: inline-flex !important;
          align-items: center !important;
          justify-content: center !important;
          padding: 12px 0px !important;
          border-radius: 8px !important;
          font-family: inherit !important;
          font-size: 14px !important;
          font-weight: 600 !important;
          text-decoration: none !important;
          cursor: pointer !important;
          position: relative !important;
          overflow: hidden !important;
          border: 2px solid #18cffb !important;
          background: #18cffb !important;
          color: #000000 !important;
          box-shadow:
            0 0 20px rgba(24, 207, 251, 0.4) !important,
            0 4px 15px rgba(24, 207, 251, 0.2) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
          transition: all 0.3s !important;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
          min-width: 120px !important;
          white-space: nowrap !important;
        }

        /* Knife shine effect overlay */
        .header__signup::before,
        button.header__signup::before {
          content: '' !important;
          position: absolute !important;
          top: 0 !important;
          left: -100% !important;
          width: 100% !important;
          height: 100% !important;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.4) 50%,
            transparent 100%
          ) !important;
          transition: left 0.6s ease !important;
          z-index: 1 !important;
        }

        /* Hover effects */
        .header__signup:hover,
        button.header__signup:hover {
          transform: translateY(-2px) scale(1.02) !important;
          background: #2dd4ff !important;
          box-shadow:
            0 0 30px rgba(24, 207, 251, 0.6) !important,
            0 8px 25px rgba(24, 207, 251, 0.3) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
          border-color: #2dd4ff !important;
          transition: all 0.3s !important;
        }

        /* Knife shine effect on hover */
        .header__signup:hover::before,
        button.header__signup:hover::before {
          left: 100% !important;
          transition: left 0.6s ease !important;
        }

        /* Active/pressed state */
        .header__signup:active,
        button.header__signup:active {
          transform: translateY(0) scale(0.98) !important;
          box-shadow:
            0 0 15px rgba(24, 207, 251, 0.5) !important,
            0 2px 10px rgba(24, 207, 251, 0.2) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
          transition: all 0.1s ease !important;
        }

        /* Focus state for accessibility */
        .header__signup:focus,
        button.header__signup:focus {
          outline: none !important;
          box-shadow:
            0 0 20px rgba(24, 207, 251, 0.4) !important,
            0 4px 15px rgba(24, 207, 251, 0.2) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.2) !important,
            0 0 0 3px rgba(24, 207, 251, 0.3) !important;
        }

        /* Text styling */
        .header__signup span,
        button.header__signup span,
        .header__signup *,
        button.header__signup * {
          color: #000000 !important;
          font-weight: 600 !important;
          position: relative !important;
          z-index: 2 !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .header__signup,
          button.header__signup {
            padding: 10px 20px !important;
            font-size: 13px !important;
            min-width: 100px !important;
          }
        }

        /* Dark theme adjustments */
        [data-bs-theme="dark"] .header__signup,
        [data-bs-theme="dark"] button.header__signup {
          background: #18cffb !important;
          border-color: #18cffb !important;
          color: #000000 !important;
        }

        [data-bs-theme="dark"] .header__signup:hover,
        [data-bs-theme="dark"] button.header__signup:hover {
          background: #2dd4ff !important;
          border-color: #2dd4ff !important;
        }

        [data-bs-theme="dark"] .header__signup span,
        [data-bs-theme="dark"] button.header__signup span,
        [data-bs-theme="dark"] .header__signup *,
        [data-bs-theme="dark"] button.header__signup * {
          color: #000000 !important;
        }
      `;
      document.head.appendChild(style);

      console.log('Betroz Header: Border radius styles applied');
    }

    // ========================================
    // POLLING MECHANISM FOR HEADER ELEMENT
    // ========================================
    function betroz0001waitForHeaderElement() {
      let attempts = 0;
      const maxAttempts = 25;
      let intervalId;

      function betroz0001tryEnhance() {
        // Look for the header element
        const headerElement = document.querySelector('.header');

        if (headerElement) {
          console.log('Betroz Header: Header element found, applying enhancements');

          // Clear the polling interval
          clearInterval(intervalId);

          // Apply border radius styles
          betroz0001applyBorderRadiusStyles();

          // Replace icons
          betroz0001replaceIcons();

          console.log('Betroz Header: Successfully enhanced header');
          return;
        }

        // Increment attempts counter
        attempts++;
        console.log(`Betroz Header: Polling attempt ${attempts}/${maxAttempts} - Header element not found yet`);

        // Stop polling if max attempts reached
        if (attempts >= maxAttempts) {
          console.log('Betroz Header: Max polling attempts reached, stopping');
          clearInterval(intervalId);
        }
      }

      // Start polling with 200ms interval
      const pollingInterval = 200;
      intervalId = setInterval(betroz0001tryEnhance, pollingInterval);

      console.log('Betroz Header: Started polling for header element');
    }

    // ========================================
    // DOM MUTATION OBSERVERS
    // ========================================
    function betroz0001setupObservers() {
      // Observer for header element if it doesn't exist yet
      if (!document.querySelector('.header')) {
        const bodyObserver = new MutationObserver((mutations) => {
          for (const mutation of mutations) {
            if (mutation.type === 'childList' && mutation.addedNodes.length) {
              for (const node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  // Check if the header element was added
                  if (node.classList && node.classList.contains('header') || node.querySelector('.header')) {
                    console.log('Betroz Header: Header element detected via observer');
                    bodyObserver.disconnect();
                    betroz0001waitForHeaderElement();
                    return;
                  }
                }
              }
            }
          }
        });

        // Start observing the body for header element creation
        bodyObserver.observe(document.body, {
          childList: true,
          subtree: true
        });

        console.log('Betroz Header: Set up observer for header element creation');
      }
    }

    // ========================================
    // INITIALIZATION LOGIC
    // ========================================
    function betroz0001init() {
      console.log('Betroz Header: Initializing...');

      // Set up observers for DOM changes
      betroz0001setupObservers();

      // Check if header element already exists
      if (document.querySelector('.header')) {
        console.log('Betroz Header: Header element already exists');
        betroz0001waitForHeaderElement();
      } else {
        console.log('Betroz Header: Header element not found, waiting for it to appear');
        // The observer will handle this case
      }
    }

    // ========================================
    // SCRIPT ENTRY POINT
    // ========================================
    // Check if DOM is ready and initialize accordingly
    if (betroz0001isDomReady()) {
      console.log('Betroz Header: DOM is ready, initializing immediately');
      betroz0001init();
    } else {
      console.log('Betroz Header: DOM not ready, waiting for DOMContentLoaded');
      document.addEventListener('DOMContentLoaded', betroz0001init);
    }

    // Handle page navigation and hash changes (for SPA behavior)
    window.addEventListener('hashchange', () => {
      console.log('Betroz Header: Hash change detected, re-checking header');
      betroz0001waitForHeaderElement();
    });

    // Handle browser back/forward navigation
    window.addEventListener('popstate', () => {
      console.log('Betroz Header: Popstate event detected, re-checking header');
      betroz0001waitForHeaderElement();
    });

    console.log('Betroz Header: Script loaded and ready');
  }

  // ========================================
  // END OF SCRIPT
  // ========================================
</script>
