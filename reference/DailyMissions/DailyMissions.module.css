.container {
  width: 100%;
}

.header {
  margin-bottom: 24px;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.missionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.missionCard {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.08), rgba(10, 195, 198, 0.04));
  border: 1px solid rgba(24, 207, 251, 0.2);
  border-radius: 20px;
  padding: 24px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.missionCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.02), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.missionCard:hover {
  transform: translateY(-4px);
  border-color: rgba(24, 207, 251, 0.4);
  box-shadow: 0 8px 32px rgba(24, 207, 251, 0.2);
}

.missionCard:hover::before {
  opacity: 1;
}

.missionCard.completed {
  border-color: rgba(27, 226, 100, 0.622);
  background: linear-gradient(225deg, rgba(31, 183, 87, 0.184), rgba(36, 210, 100, 0.526), rgba(31, 183, 87, 0.184));
}

.missionCard.completed:hover {
  border-color: rgba(8, 238, 92, 0.722);
  box-shadow: 0 8px 32px rgba(24, 251, 62, 0.2);
}

.missionCard.claimed {
  opacity: 0.7;
  border-color: rgba(156, 163, 175, 0.3);
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.08), rgba(156, 163, 175, 0.04));
}

.missionCard.claimed:hover {
  border-color: rgba(156, 163, 175, 0.3);
  box-shadow: none;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.titleSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.missionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  line-height: 1.2;
}

.categoryBadge {
  background: rgba(0, 0, 0, 0.3);
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
}

.rewardSection {
  display: flex;
  align-items: center;
  align-self: flex-start;
  gap: 8px;
  background: #13839f80;
  box-shadow: 0 4px 16px #0cceff3b;
  border: 1px solid #1696b6;
  padding: 8px 16px;
  border-radius: 20px;
  margin-bottom: 16px;
}

.rewardIcon {
  display: flex;
  align-items: center;
}

.rewardValue {
  font-size: 1.25rem;
  font-weight: 700;
  color: #18cffb;
}

.missionDescription {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.progressSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progressLabel {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.progressValue {
  font-size: 0.9rem;
  font-weight: 600;
  color: #18cffb;
}

.progressValue.completed {
  color: #06d06a;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: rgba(24, 207, 251, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 4px;
  transition: width 0.6s ease;
}

.missionCard.completed .progressFill {
  background: linear-gradient(90deg, #06d06a 0%, #30bf69 100%);
}

.actionSection {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: auto;
}

.claimButton {
  background: linear-gradient(135deg, #01fd86 0%, #06d06a 100%);
  color: #061d2b;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 100%;
}

.claimButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px #01fd8754;
}

.claimButton:active {
  transform: translateY(0);
}

.claimedBadge,
.inProgressBadge {
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  width: 100%;
}

.claimedBadge {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  color: #ffffff;
}

.inProgressBadge {
  background: rgba(24, 207, 251, 0.1);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(24, 207, 251, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .title {
    font-size: 1.8rem;
  }

  .missionsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .missionCard {
    padding: 20px;
    min-height: 260px;
  }

  .missionTitle {
    font-size: 1.3rem;
  }

  .missionDescription {
    font-size: 0.9rem;
  }

  .rewardValue {
    font-size: 1.1rem;
  }

  .claimButton {
    padding: 14px 28px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .missionCard {
    padding: 16px;
    min-height: 240px;
  }

  .missionTitle {
    font-size: 1.2rem;
  }

  .missionDescription {
    font-size: 0.85rem;
  }

  .progressLabel,
  .progressValue {
    font-size: 0.75rem;
  }

  .rewardValue {
    font-size: 1rem;
  }

  .claimButton {
    padding: 12px 24px;
    font-size: 0.85rem;
  }

  .categoryBadge {
    font-size: 0.7rem;
    padding: 3px 10px;
  }

  .rewardSection {
    padding: 6px 12px;
  }

  .progressBar {
    height: 10px;
  }
}
