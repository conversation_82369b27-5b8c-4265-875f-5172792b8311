import styles from "./DailyMissions.module.css";

export const i18n = {
  en: {
    dailyMissions: "Daily Missions",
    progress: "Progress",
    inProgress: "In Progress",
    reward: "Reward",
    completed: "Completed",
    claim: "Claim",
    claimed: "Claimed",
    diamonds: "Diamonds",
  },
  tr: {
    dailyMissions: "<PERSON><PERSON>n<PERSON><PERSON><PERSON> Görevler",
    progress: "<PERSON>lerlem<PERSON>",
    inProgress: "Devam Ediyor",
    reward: "<PERSON><PERSON><PERSON><PERSON>",
    completed: "Tamamlandı",
    claim: "Al",
    claimed: "Alındı",
    diamonds: "Elmas",
  },
};

const DailyMissions = ({ missions = [], i18n = {}, language = "en" }) => {
  const handleClaimReward = (missionId) => {
    // TODO: Implement claim reward API call
    console.log("Claiming reward for mission:", missionId);
  };

  if (!missions.length) {
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          {i18n.dailyMissions || "Daily Missions"}
        </h2>
      </div>

      <div className={styles.missionsGrid}>
        {missions.map((mission, index) => {
          const progressPercentage =
            mission.target > 0 ? (mission.current / mission.target) * 100 : 0;
          const isCompleted = mission.current >= mission.target;
          const isClaimed = mission.claimed;

          return (
            <div
              key={mission.id || index}
              className={`${styles.missionCard} ${
                isCompleted ? styles.completed : ""
              } ${isClaimed ? styles.claimed : ""}`}
            >
              <div className={styles.cardHeader}>
                <div className={styles.titleSection}>
                  <h3 className={styles.missionTitle}>{mission.title}</h3>
                  <div className={styles.categoryBadge}>
                    {mission.category || "SLOTS"}
                  </div>
                </div>
              </div>

              <div className={styles.rewardSection}>
                💎
                <span className={styles.rewardValue}>
                  {mission.reward.toLocaleString()}
                </span>
              </div>

              <p className={styles.missionDescription}>{mission.description}</p>

              <div className={styles.progressSection}>
                <div className={styles.progressHeader}>
                  <span className={styles.progressLabel}>
                    {i18n.progress || "PROGRESS"}
                  </span>
                  <span
                    className={`${styles.progressValue} ${
                      isCompleted ? styles.completed : ""
                    }`}
                  >
                    {mission.current.toLocaleString()}/
                    {mission.target.toLocaleString()}
                  </span>
                </div>
                <div className={styles.progressBar}>
                  <div
                    className={styles.progressFill}
                    style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                  />
                </div>
              </div>

              <div className={styles.actionSection}>
                {isCompleted && !isClaimed && (
                  <button
                    className={styles.claimButton}
                    onClick={() => handleClaimReward(mission.id)}
                  >
                    {i18n.claim || "CLAIM"}
                  </button>
                )}

                {isClaimed && (
                  <div className={styles.claimedBadge}>
                    {i18n.claimed || "CLAIMED"}
                  </div>
                )}

                {!isCompleted && (
                  <div className={styles.inProgressBadge}>
                    {i18n.inProgress || "IN PROGRESS"}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export { DailyMissions };
export default DailyMissions;
