import { createRoot } from 'react-dom/client';
import { v4 as uuid } from 'uuid';

import { RootComponent } from './RootComponent'
import { LegacyRootComponent } from './LegacyRootComponent';

const isMatchingPage = (pattern) => !pattern || pattern.test(window.location.pathname)

const onLifecycle = ({ watch, selector, page, onMount, onUnmount }) => {
  let target = null
  let watchTarget = null

  const observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type !== 'childList') {
        continue
      }

      for (const node of mutation.removedNodes) {
        if (!target && !watchTarget) {
          continue
        }

        if (isMatchingPage(page)) {
          if (node.nodeType !== Node.ELEMENT_NODE) {
            continue
          }

          if (node !== watchTarget && node.contains(watchTarget) === false) {
            continue
          }
        }

        onUnmount(target)
        target = null
        watchTarget = null
        break
      }

      if (isMatchingPage(page) === false) {
        break
      }

      for (const node of mutation.addedNodes) {
        if (target) {
          continue
        }

        if (node.nodeType !== Node.ELEMENT_NODE) {
          continue
        }

        watchTarget = document.querySelector(watch)
        if (!watchTarget) {
          continue
        }

        if (watch === selector) {
          target = watchTarget
          onMount(target, watchTarget)
          break
        } else {
          target = document.querySelector(selector)
          if (!target) continue
          onMount(target, watchTarget)
          break
        }
      }
    }
  });

  if (isMatchingPage(page)) {
    watchTarget = document.querySelector(watch)
    if (watchTarget) {
      if (watch === selector) {
        target = watchTarget
        onMount(target, watchTarget)
      } else {
        target = document.querySelector(selector)
        if (target) {
          onMount(target, watchTarget)
        }
      }
    }
  }

  observer.observe(document.body, { childList: true, subtree: true });
}

export const render = ({ watch, selector, page, Component, Stylesheet, i18n }) => {
  let root = null
  const id = `element-${uuid()}`

  onLifecycle(
    {
      watch: watch ?? selector,
      selector,
      page,
      onMount: (target) => {
        console.dir(`Mounted ${selector}`)

        root = createRoot(target);
        root.render(
          <RootComponent Component={Component} Stylesheet={Stylesheet} i18n={i18n} />
        );
      },
      onUnmount: (target) => {
        console.dir(`Unmounted ${selector}`)
        root?.unmount()
      }
    }
  )

  return id
}

export const renderAfter = ({ watch, selector, page, Component, Stylesheet, i18n }) => {
  let root = null
  const id = `element-${uuid()}`

  onLifecycle(
    {
      watch: watch ?? selector,
      selector,
      page,
      onMount: (target) => {
        console.dir(`Mounted ${selector}`)

        const wrapper = document.createElement('div');
        wrapper.id = id;
        wrapper.style.width = '100%'

        target.insertAdjacentElement('afterend', wrapper);

        root = createRoot(wrapper);
        root.render(
          <RootComponent Component={Component} Stylesheet={Stylesheet} i18n={i18n} root={wrapper} />
        );
      },
      onUnmount: (target) => {
        console.dir(`Unmounted ${selector}`)
        root?.unmount()
      }
    }
  )

  return id
}

export const renderBefore = ({ watch, selector, page, Component, Stylesheet, i18n }) => {
  let root = null
  const id = `element-${uuid()}`

  onLifecycle(
    {
      watch: watch ?? selector,
      selector,
      page,
      onMount: (target) => {
        console.dir(`Mounted ${selector}`)

        const wrapper = document.createElement('div');
        wrapper.style.width = '100%'
        wrapper.id = id;

        target.insertAdjacentElement('beforebegin', wrapper);

        root = createRoot(wrapper);
        root.render(
          <RootComponent Component={Component} Stylesheet={Stylesheet} i18n={i18n} />
        );
      },
      onUnmount: (target) => {
        console.dir(`Unmounted ${selector}`)
        root?.unmount()
      }
    }
  )

  return id
}

export const renderAtTheEnd = ({ watch, selector, page, Component, Stylesheet, i18n }) => {
  let root = null
  const id = `element-${uuid()}`

  onLifecycle(
    {
      watch: watch ?? selector,
      selector,
      page,
      onMount: (target) => {
        console.dir(`Mounted ${selector}`)

        const wrapper = document.createElement('div');
        wrapper.id = id;

        target.append(wrapper);

        root = createRoot(wrapper);
        root.render(
          <RootComponent Component={Component} Stylesheet={Stylesheet} i18n={i18n} />
        );
      },
      onUnmount: (target) => {
        console.dir(`Unmounted ${selector}`)
        root?.unmount()
      }
    }
  )

  return id
}

export const renderAtTheBeginning = ({ watch, selector, page, Component, Stylesheet, i18n }) => {
  let root = null
  const id = `element-${uuid()}`

  onLifecycle(
    {
      watch: watch ?? selector,
      selector,
      page,
      onMount: (target) => {
        console.dir(`Mounted ${selector}`)

        const wrapper = document.createElement('div');
        wrapper.id = id;

        target.prepend(wrapper);

        root = createRoot(wrapper);
        root.render(
          <RootComponent Component={Component} Stylesheet={Stylesheet} i18n={i18n} />
        );
      },
      onUnmount: (target) => {
        console.dir(`Unmounted ${selector}`)
        root?.unmount()
      }
    }
  )

  return id
}

export const renderLegacy = ({ watch, page, lifecycle }) => {
  const id = `element-${uuid()}`
  let root = null

  onLifecycle(
    {
      watch,
      selector: 'body',
      page,
      onMount: (target, watchTarget) => {
        console.dir(`Mounted ${watch}`)

        const wrapper = document.createElement('div');
        wrapper.id = id;

        target.append(wrapper)

        root = createRoot(wrapper)
        root.render(
          <RootComponent Component={(props) => <LegacyRootComponent {...props} lifecycle={lifecycle} target={watchTarget} />}  />
        );
      },
      onUnmount: (target) => {
        console.dir(`Unmounted ${watch}`)
        root?.unmount()
      }
    }
  )

  return id
}

export const loadFontStylesheet = async (googleFontsUrl) => {
  try {
    // Fetch the CSS from Google Fonts
    const response = await fetch(googleFontsUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch font CSS: ${response.status}`);
    }

    const cssText = await response.text();

    // Parse @font-face rules from the CSS
    const fontFaceRegex = /@font-face\s*\{([^}]+)\}/g;
    const matches = [...cssText.matchAll(fontFaceRegex)];

    if (matches.length === 0) {
      console.warn('No @font-face rules found in the CSS');
      return;
    }

    // Create a style element and add the parsed CSS
    const styleElement = document.createElement('style');

    // Process each @font-face rule
    let processedCSS = '';
    matches.forEach(match => {
      const fontFaceContent = match[1];

      // Extract properties
      const fontFamily = extractCSSProperty(fontFaceContent, 'font-family');
      const fontStyle = extractCSSProperty(fontFaceContent, 'font-style');
      const fontWeight = extractCSSProperty(fontFaceContent, 'font-weight');
      const fontDisplay = extractCSSProperty(fontFaceContent, 'font-display');
      const src = extractCSSProperty(fontFaceContent, 'src');
      const unicodeRange = extractCSSProperty(fontFaceContent, 'unicode-range');

      // Rebuild the @font-face rule with proper formatting
      processedCSS += `
@font-face {
  font-family: ${fontFamily};
  font-style: ${fontStyle};
  font-weight: ${fontWeight};
  font-display: ${fontDisplay};
  src: ${src};
  ${unicodeRange ? `unicode-range: ${unicodeRange};` : ''}
}
`;
    });

    // Add the CSS content to the style element
    styleElement.appendChild(document.createTextNode(processedCSS));

    // Append to document head
    document.head.appendChild(styleElement);

    console.log(`Loaded font stylesheet with ${matches.length} font definitions from ${googleFontsUrl}`);

  } catch (error) {
    console.error('Error loading font stylesheet:', error);

    // Fallback: create a link element to load the CSS directly
    const linkElement = document.createElement('link');
    linkElement.rel = 'stylesheet';
    linkElement.href = googleFontsUrl;
    document.head.appendChild(linkElement);

    console.log('Fallback: Loading font stylesheet as link element');
  }
}

// Helper function to extract CSS property values
const extractCSSProperty = (cssText, property) => {
  const regex = new RegExp(`${property}\\s*:\\s*([^;]+)`, 'i');
  const match = cssText.match(regex);
  return match ? match[1].trim() : '';
}
