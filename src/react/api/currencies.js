import { API_URL, transformResponse } from './base'

/**
 * @typedef {Object} Network
 * @property {number} id - The network ID
 * @property {string} name - The network name
 * @property {string} key - The network key
 * @property {number} is_active - Whether the network is active (1 for active, 0 for inactive)
 * @property {string} pname - The network protocol name
 */

/**
 * @typedef {Object} Currency
 * @property {number} id - The currency ID
 * @property {number} payment_provider_id - The payment provider ID
 * @property {number} network_id - The network ID
 * @property {number} currency_id - The currency ID
 * @property {string} name - The currency name
 * @property {string} alpha - The currency alpha code
 * @property {string} alias - The currency alias
 * @property {number} block_count - The number of blocks required for confirmation
 * @property {number} delay - The delay in milliseconds
 * @property {number} precision - The precision for the currency
 * @property {string} min_transfer - The minimum transfer amount as a string
 * @property {boolean} is_active - Whether the currency is active
 * @property {string} currency - The currency code
 * @property {string} paymentCurrency - The payment currency code
 * @property {string} paymentGatewayName - The payment gateway name
 * @property {Network} network - The network object
 */


export const getCurrencies = async () => {
  try {
    const response = await fetch(`${API_URL}/api/player/public/wallet/currencies`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${localStorage.getItem('bearer')}`
      }
    })
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    /** @type {Currency[]} */
    const currencies = await transformResponse(response)

    return currencies
  } catch (error) {
    console.error('Error fetching currencies:', error)
  }
}
