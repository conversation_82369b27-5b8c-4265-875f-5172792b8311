import { API_URL, transformResponse } from './base'

/**
 * @typedef User
 * @type {object}
 * @property {string} token - HTTP access token
 * @property {string} ws - WS access token
 * @property {boolean} is_streamer - Is streamer account
 * @property {string} username - Account username
 * @property {email} email - Account email
 * @property {number} id - Account id
 * @property {string | null} nickname - Account nickname
 * @property {string} rank - Account VIP rank
 * @property {string} ranki - Rank image
 * @property {string} percentage - Account VIP progress "0.00"
 * @property {string} turnover - Turnover "0.00"
 * @property {number} registration - Registered at timestamp
 */

export const getUser = async () => {
  try {
    const response = await fetch(`${API_URL}/api/player/user`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${localStorage.getItem('bearer')}`
      }
    })
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }
    
    /** @type {User} */
    const user = await transformResponse(response)

    return user
  } catch (error) {
    console.error('Error fetching user:', error)
  }
}

/**
 * @typedef UserVipState
 * @type {object}
 * @property {string} name - State name
 * @property {string} slug - State slug
 * @property {string} wager_limit - Wager limit
 * @property {string} icon - Icon url
 * @property {string} rake_percentage - Rake percentage
 */

/**
 * @typedef UserVipStateDetails
 * @type {object}
 * @property {UserVipState} current - Current state
 * @property {UserVipState} next - Next state
 * @property {string} username - Username
 * @property {string} rank - Rank
 * @property {string} ranki - Rank image
 * @property {string} percentage - Percentage "0.00"
 */

export const getUserVipState = async (nickname) => {
  try {
    const response = await fetch(`${API_URL}/api/player/public/vip/state/${nickname}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${localStorage.getItem('bearer')}`
      }
    })
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    /** @type {UserVipStateDetails} */
    const userVipState = await transformResponse(response)

    return userVipState
  } catch (error) {
    console.error('Error fetching user VIP state:', error)
  }
}

/**
 * @typedef UserStats
 * @type {object}
 * @property {number} total_bet_count - Total number of bets placed
 * @property {number} total_win_count - Total number of wins
 * @property {number} total_wager_amount - Total amount wagered
 * @property {number} total_loses_count - Total number of losses
 */

export const getUserStats = async (username) => {
  try {
    const response = await fetch(`${API_URL}/api/player/public/stats/basic/${username}/casino/all`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${localStorage.getItem('bearer')}`
      }
    })
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    /** @type {UserStats} */
    const userStats = await transformResponse(response)

    return userStats
  } catch (error) {
    console.error('Error fetching user stats:', error)
  }
}

/**
 * @typedef LastPlayedGame
 * @type {object}
 * @property {number} id - The game ID
 * @property {string} image - The game image URL
 * @property {string} name - The game name
 * @property {string} slug - The game slug
 * @property {number} is_live - Whether the game is live (0 or 1)
 * @property {number} is_hd - Whether the game is HD (0 or 1)
 * @property {number} has_freespins - Whether the game has freespins (0 or 1)
 * @property {string} payout - The game payout percentage as a string
 * @property {number} has_jackpot - Whether the game has jackpot (0 or 1)
 * @property {number} buy_bonus - Whether the game has buy bonus feature (0 or 1)
 * @property {string} blacklist - JSON string of blacklisted countries
 * @property {string} provider - The game provider name
 * @property {string} color - The game color theme
 */

export const getLastPlayedGames = async () => {
  try {
    const response = await fetch(`${API_URL}/api/player/public/games2/welcome/last-played`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${localStorage.getItem('bearer')}`
      }
    })
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    /** @type {LastPlayedGame[]} */
    const lastPlayedGames = await transformResponse(response)

    return lastPlayedGames
  } catch (error) {
    console.error('Error fetching last played games:', error)
  }
}

