import { API_URL, transformResponse } from './base'

/**
 * @typedef {Object} PromotionCard
 * @property {number} timestamp - Unix timestamp
 * @property {string} slug - URL slug for the promotion
 * @property {string} title - Promotion title
 * @property {string} description - Promotion description
 * @property {string} image - Image URL for the promotion
 */

/**
 * Fetches promotion cards from the API
 * @param {Object} options - Request options
 * @param {string} [options.language='en'] - Language code (e.g., 'en', 'tr')
 * @param {number} [options.categoryId=114] - Category ID for promotions
 * @param {string} [options.type='all'] - Type filter for promotions
 * @returns {Promise<PromotionCard[]>} Array of promotion cards
 */
export const getPromotionCards = async ({ language = 'en', categoryId = 114, type = 'all' } = {}) => {
  try {
    const bearerToken = localStorage.getItem('bearer')

    // Get current timestamp for rt parameter
    const rt = Math.floor(Date.now() / 1000)

    const requestBody = {
      lang: language,
      category_id: categoryId,
      type: type,
      rt: rt
    }

    const response = await fetch(`${API_URL}/api/player/public/promotions/category`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${bearerToken}`
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Authentication required. Please log in to view promotions.')
      }
      throw new Error('Network response was not ok')
    }

    /** @type {PromotionCard[]} */
    const promotions = await transformResponse(response)

    return promotions
  } catch (error) {
    console.error('Error fetching promotion cards:', error)
    throw error
  }
}
