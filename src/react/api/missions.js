import { API_ENDPOINTS } from '../config/environment.js';
import { isMissionInactive, getCompletedMissionMessage, sortMissionsByPriority } from '../utils/timeUtils.js';

// Mock data for development - replace with actual API calls
const mockMissionsData = {
  en: {
    headerSlides: [
      {
        id: 'slide_1',
        title: 'Daily Missions',
        subtitle: 'Complete daily missions to earn diamond rewards',
        backgroundImage: 'https://via.placeholder.com/1440x290/18cffb/ffffff?text=Daily+Missions',
        backgroundColor: 'linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%)',
        textColor: '#ffffff'
      },
      {
        id: 'slide_2',
        title: 'Weekly Challenges',
        subtitle: 'Take on bigger challenges for premium rewards',
        backgroundImage: 'https://via.placeholder.com/1440x290/fbbf24/000000?text=Weekly+Challenges',
        backgroundColor: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
        textColor: '#000000'
      },
      {
        id: 'slide_3',
        title: 'Monthly Masters',
        subtitle: 'Become a monthly master and earn ultimate rewards',
        backgroundImage: 'https://via.placeholder.com/1440x290/8b5cf6/ffffff?text=Monthly+Masters',
        backgroundColor: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        textColor: '#ffffff'
      },
      {
        id: 'slide_4',
        title: 'Missions Shop',
        subtitle: 'Spend your diamonds on exclusive rewards and bonuses',
        backgroundImage: 'https://via.placeholder.com/1440x290/22c55e/ffffff?text=Missions+Shop',
        backgroundColor: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
        textColor: '#ffffff'
      }
    ],
    stats: {
      diamonds: 1250,
      totalProgress: 75,
      completedMissions: 12,
      totalMissions: 16,
      dailyStreak: 5
    },
    dailyMissions: [
      {
        id: 'daily_1',
        type: 'bet',
        title: 'Play 370x on The Dog House — Muttley Crew',
        description: 'Min bet 0.20€, hit 370x multiplier or higher',
        current: 1,
        target: 1,
        reward: 1000,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'daily_2',
        type: 'win',
        title: 'Win 5 Games',
        description: 'Win at least 5 games in any category',
        current: 5,
        target: 5,
        reward: 75,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'daily_3',
        type: 'deposit',
        title: 'Make a Deposit',
        description: 'Make a deposit of at least $20',
        current: 1,
        target: 1,
        reward: 100,
        claimed: true,
        category: 'ACCOUNT'
      },
      {
        id: 'daily_4',
        type: 'play',
        title: 'Play for 30 Minutes',
        description: 'Play games for a total of 30 minutes',
        current: 25,
        target: 30,
        reward: 60,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'daily_5',
        type: 'bet',
        title: 'Hit Big Win',
        description: 'Win 50x or higher multiplier on any slot',
        current: 2,
        target: 3,
        reward: 150,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'daily_6',
        type: 'play',
        title: 'Try Live Casino',
        description: 'Play at least 3 rounds in live casino',
        current: 0,
        target: 3,
        reward: 200,
        claimed: false,
        category: 'LIVE'
      },
      {
        id: 'daily_7',
        type: 'win',
        title: 'Sports Bet Winner',
        description: 'Win a sports bet with odds 2.0 or higher',
        current: 0,
        target: 1,
        reward: 120,
        claimed: false,
        category: 'SPORTS'
      },
      {
        id: 'daily_8',
        type: 'play',
        title: 'Spin Master',
        description: 'Make 100 spins on any slot game',
        current: 67,
        target: 100,
        reward: 80,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'daily_9',
        type: 'bet',
        title: 'High Roller',
        description: 'Place a bet of $50 or higher',
        current: 0,
        target: 1,
        reward: 300,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'daily_10',
        type: 'win',
        title: 'Lucky Streak',
        description: 'Win 3 games in a row',
        current: 1,
        target: 3,
        reward: 180,
        claimed: false,
        category: 'CASINO'
      }
    ],
    weeklyMissions: [
      {
        id: 'weekly_1',
        type: 'bet',
        title: 'Weekly Warrior',
        description: 'Place 500 bets during the week',
        current: 234,
        target: 500,
        reward: 2500,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'weekly_2',
        type: 'win',
        title: 'Big Winner',
        description: 'Win a total of $1000 this week',
        current: 650,
        target: 1000,
        reward: 1500,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'weekly_3',
        type: 'play',
        title: 'Slot Explorer',
        description: 'Play 20 different slot games',
        current: 12,
        target: 20,
        reward: 800,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'weekly_4',
        type: 'deposit',
        title: 'VIP Depositor',
        description: 'Make 3 deposits of $100+ each',
        current: 2,
        target: 3,
        reward: 2000,
        claimed: false,
        category: 'ACCOUNT'
      }
    ],
    monthlyMissions: [
      {
        id: 'monthly_1',
        type: 'bet',
        title: 'Monthly Champion',
        description: 'Place 5000 bets during the month',
        current: 2340,
        target: 5000,
        reward: 15000,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'monthly_2',
        type: 'win',
        title: 'Mega Winner',
        description: 'Win a total of $10000 this month',
        current: 4500,
        target: 10000,
        reward: 8000,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'monthly_3',
        type: 'play',
        title: 'Game Master',
        description: 'Play 100 different games',
        current: 67,
        target: 100,
        reward: 5000,
        claimed: false,
        category: 'CASINO'
      }
    ],
    finalMissions: {
      daily: {
        id: 'final_daily',
        title: 'Daily Champion',
        description: 'Complete all daily missions to unlock this special reward',
        current: 0,
        target: 1,
        reward: 2500,
        claimed: false
      },
      weekly: {
        id: 'final_weekly',
        title: 'Weekly Legend',
        description: 'Complete all weekly missions to unlock this premium reward',
        current: 0,
        target: 1,
        reward: 10000,
        claimed: false
      },
      monthly: {
        id: 'final_monthly',
        title: 'Monthly Master',
        description: 'Complete all monthly missions to unlock this ultimate reward',
        current: 0,
        target: 1,
        reward: 50000,
        claimed: false
      }
    }
  },
  tr: {
    headerSlides: [
      {
        id: 'slide_1',
        title: 'Günlük Görevler',
        subtitle: 'Elmas ödülleri kazanmak için günlük görevleri tamamlayın',
        backgroundImage: 'https://via.placeholder.com/1440x290/18cffb/ffffff?text=Gunluk+Gorevler',
        backgroundColor: 'linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%)',
        textColor: '#ffffff'
      },
      {
        id: 'slide_2',
        title: 'Haftalık Meydan Okumalar',
        subtitle: 'Premium ödüller için daha büyük meydan okumalara katılın',
        backgroundImage: 'https://via.placeholder.com/1440x290/fbbf24/000000?text=Haftalik+Meydan+Okumalar',
        backgroundColor: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
        textColor: '#000000'
      },
      {
        id: 'slide_3',
        title: 'Aylık Ustalar',
        subtitle: 'Aylık usta olun ve nihai ödülleri kazanın',
        backgroundImage: 'https://via.placeholder.com/1440x290/8b5cf6/ffffff?text=Aylik+Ustalar',
        backgroundColor: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        textColor: '#ffffff'
      },
      {
        id: 'slide_4',
        title: 'Görev Mağazası',
        subtitle: 'Elmaslarınızı özel ödüller ve bonuslar için harcayın',
        backgroundImage: 'https://via.placeholder.com/1440x290/22c55e/ffffff?text=Gorev+Magazasi',
        backgroundColor: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
        textColor: '#ffffff'
      }
    ],
    stats: {
      diamonds: 1250,
      totalProgress: 75,
      completedMissions: 12,
      totalMissions: 16,
      dailyStreak: 5
    },
    dailyMissions: [
      {
        id: 'daily_1',
        type: 'bet',
        title: 'The Dog House — Muttley Crew\'da 370x Oyna',
        description: 'Min bahis 0.20€, 370x çarpan veya daha yüksek vur',
        current: 1,
        target: 1,
        reward: 1000,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'daily_2',
        type: 'win',
        title: '5 Oyun Kazan',
        description: 'Herhangi bir kategoride en az 5 oyun kazan',
        current: 5,
        target: 5,
        reward: 75,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'daily_3',
        type: 'deposit',
        title: 'Para Yatır',
        description: 'En az 20$ para yatır',
        current: 1,
        target: 1,
        reward: 100,
        claimed: true,
        category: 'ACCOUNT'
      },
      {
        id: 'daily_4',
        type: 'play',
        title: '30 Dakika Oyna',
        description: 'Toplam 30 dakika oyun oyna',
        current: 25,
        target: 30,
        reward: 60,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'daily_5',
        type: 'bet',
        title: 'Büyük Kazanç',
        description: 'Herhangi bir slotta 50x veya daha yüksek çarpan kazan',
        current: 2,
        target: 3,
        reward: 150,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'daily_6',
        type: 'play',
        title: 'Canlı Casino Dene',
        description: 'Canlı casinoda en az 3 tur oyna',
        current: 0,
        target: 3,
        reward: 200,
        claimed: false,
        category: 'LIVE'
      },
      {
        id: 'daily_7',
        type: 'win',
        title: 'Spor Bahis Kazananı',
        description: '2.0 veya daha yüksek oranla spor bahsi kazan',
        current: 0,
        target: 1,
        reward: 120,
        claimed: false,
        category: 'SPORTS'
      },
      {
        id: 'daily_8',
        type: 'play',
        title: 'Spin Ustası',
        description: 'Herhangi bir slot oyununda 100 spin yap',
        current: 67,
        target: 100,
        reward: 80,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'daily_9',
        type: 'bet',
        title: 'Yüksek Bahisçi',
        description: '50$ veya daha yüksek bahis yap',
        current: 0,
        target: 1,
        reward: 300,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'daily_10',
        type: 'win',
        title: 'Şanslı Seri',
        description: 'Üst üste 3 oyun kazan',
        current: 1,
        target: 3,
        reward: 180,
        claimed: false,
        category: 'CASINO'
      }
    ],
    weeklyMissions: [
      {
        id: 'weekly_1',
        type: 'bet',
        title: 'Haftalık Savaşçı',
        description: 'Hafta boyunca 500 bahis yap',
        current: 234,
        target: 500,
        reward: 2500,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'weekly_2',
        type: 'win',
        title: 'Büyük Kazanan',
        description: 'Bu hafta toplam 1000$ kazan',
        current: 650,
        target: 1000,
        reward: 1500,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'weekly_3',
        type: 'play',
        title: 'Slot Kaşifi',
        description: '20 farklı slot oyunu oyna',
        current: 12,
        target: 20,
        reward: 800,
        claimed: false,
        category: 'SLOTS'
      },
      {
        id: 'weekly_4',
        type: 'deposit',
        title: 'VIP Para Yatıran',
        description: 'Her biri 100$+ olan 3 para yatırma işlemi yap',
        current: 2,
        target: 3,
        reward: 2000,
        claimed: false,
        category: 'ACCOUNT'
      }
    ],
    monthlyMissions: [
      {
        id: 'monthly_1',
        type: 'bet',
        title: 'Aylık Şampiyon',
        description: 'Ay boyunca 5000 bahis yap',
        current: 2340,
        target: 5000,
        reward: 15000,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'monthly_2',
        type: 'win',
        title: 'Mega Kazanan',
        description: 'Bu ay toplam 10000$ kazan',
        current: 4500,
        target: 10000,
        reward: 8000,
        claimed: false,
        category: 'CASINO'
      },
      {
        id: 'monthly_3',
        type: 'play',
        title: 'Oyun Ustası',
        description: '100 farklı oyun oyna',
        current: 67,
        target: 100,
        reward: 5000,
        claimed: false,
        category: 'CASINO'
      }
    ],
    finalMissions: {
      daily: {
        id: 'final_daily',
        title: 'Günlük Şampiyon',
        description: 'Bu özel ödülün kilidini açmak için tüm günlük görevleri tamamla',
        current: 0,
        target: 1,
        reward: 2500,
        claimed: false
      },
      weekly: {
        id: 'final_weekly',
        title: 'Haftalık Efsane',
        description: 'Bu premium ödülün kilidini açmak için tüm haftalık görevleri tamamla',
        current: 0,
        target: 1,
        reward: 10000,
        claimed: false
      },
      monthly: {
        id: 'final_monthly',
        title: 'Aylık Usta',
        description: 'Bu nihai ödülün kilidini açmak için tüm aylık görevleri tamamla',
        current: 0,
        target: 1,
        reward: 50000,
        claimed: false
      }
    }
  }
}

const mockShopItems = {
  en: [
    {
      id: 'shop_1',
      name: 'Free Spins Pack',
      description: '50 free spins for slot games',
      price: 200,
      quantity: '50x',
      image: 'https://via.placeholder.com/200x120/18cffb/ffffff?text=Free+Spins',
      purchased: false
    },
    {
      id: 'shop_2',
      name: 'Bonus Balance',
      description: '$25 bonus balance for your account',
      price: 500,
      quantity: '$25',
      image: 'https://via.placeholder.com/200x120/22c55e/ffffff?text=Bonus',
      purchased: false
    },
    {
      id: 'shop_3',
      name: 'VIP Status',
      description: '7 days of VIP status with exclusive benefits',
      price: 1000,
      quantity: '7 days',
      image: 'https://via.placeholder.com/200x120/fbbf24/000000?text=VIP',
      purchased: false
    },
    {
      id: 'shop_4',
      name: 'Cashback Boost',
      description: '10% extra cashback for 24 hours',
      price: 300,
      quantity: '24h',
      image: 'https://via.placeholder.com/200x120/8b5cf6/ffffff?text=Cashback',
      purchased: false
    }
  ],
  tr: [
    {
      id: 'shop_1',
      name: 'Bedava Spin Paketi',
      description: 'Slot oyunları için 50 bedava spin',
      price: 200,
      quantity: '50x',
      image: 'https://via.placeholder.com/200x120/18cffb/ffffff?text=Bedava+Spin',
      purchased: false
    },
    {
      id: 'shop_2',
      name: 'Bonus Bakiye',
      description: 'Hesabınız için 25$ bonus bakiye',
      price: 500,
      quantity: '25$',
      image: 'https://via.placeholder.com/200x120/22c55e/ffffff?text=Bonus',
      purchased: false
    },
    {
      id: 'shop_3',
      name: 'VIP Statü',
      description: 'Özel avantajlarla 7 gün VIP statü',
      price: 1000,
      quantity: '7 gün',
      image: 'https://via.placeholder.com/200x120/fbbf24/000000?text=VIP',
      purchased: false
    },
    {
      id: 'shop_4',
      name: 'Cashback Artırımı',
      description: '24 saat boyunca %10 ekstra cashback',
      price: 300,
      quantity: '24s',
      image: 'https://via.placeholder.com/200x120/8b5cf6/ffffff?text=Cashback',
      purchased: false
    }
  ]
}

// Get bearer token from localStorage
function getBearerToken() {
  try {
    return localStorage.getItem('bearer');
  } catch (error) {
    console.warn('Missions API: Failed to get bearer token from localStorage:', error);
    return null;
  }
}

// Create extended user via custom API
async function createExtendedUser() {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    console.log('Missions API: No bearer token found, user not logged in');
    return null;
  }

  try {
    const response = await fetch(API_ENDPOINTS.EXTENDED_USERS_ME, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      console.warn(`Missions API: Extended user creation failed with status ${response.status}`);
      return null;
    }

    const data = await response.json();
    console.log('Missions API: Extended user created successfully:', data);
    return data;
  } catch (error) {
    console.error('Missions API: Failed to create extended user:', error);
    return null;
  }
}

// Fetch real missions data from backend
async function fetchRealMissionsData() {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    console.log('Missions API: No bearer token found for missions fetch');
    return null;
  }

  try {
    const response = await fetch(API_ENDPOINTS.MISSIONS, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      console.warn(`Missions API: Missions fetch failed with status ${response.status}`);
      return null;
    }

    const data = await response.json();
    console.log('Missions API: Real missions data fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('Missions API: Failed to fetch real missions data:', error);
    return null;
  }
}

// Fetch mission statistics from backend
async function fetchMissionStats() {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    console.log('Missions API: No bearer token found for stats fetch');
    return null;
  }

  try {
    const response = await fetch(API_ENDPOINTS.MISSION_STATS, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      console.warn(`Missions API: Stats fetch failed with status ${response.status}`);
      return null;
    }

    const data = await response.json();
    console.log('Missions API: Mission stats fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('Missions API: Failed to fetch mission stats:', error);
    return null;
  }
}

// Helper function to check if objective is completed based on operator
// This is used as a fallback when userAssignment.isCompleted is not available
function checkObjectiveCompletion(progress, target, operator) {
  switch (operator) {
    case 'ge': // greater than or equal
      return progress >= target;
    case 'gt': // greater than
      return progress > target;
    case 'le': // less than or equal
      return progress <= target;
    case 'lt': // less than
      return progress < target;
    case 'eq': // equal
      return progress === target;
    default:
      return progress >= target; // default to greater than or equal
  }
}

// Calculate final missions data based on completed missions and claims
function calculateFinalMissions(finalClaimsData, statsData, language, dailyMissions = [], weeklyMissions = [], monthlyMissions = []) {
  console.log('Final Mission Claims: calculateFinalMissions called with:', {
    finalClaimsData: finalClaimsData ? 'present' : 'null',
    statsData: statsData ? 'present' : 'null',
    statsDataSuccess: statsData?.success,
    statsDataData: statsData?.data,
    eligibleMissions: statsData?.data?.eligibleMissions,
    completedEligibleMissions: statsData?.data?.completedEligibleMissions,
    dailyMissionsCount: dailyMissions.length,
    weeklyMissionsCount: weeklyMissions.length,
    monthlyMissionsCount: monthlyMissions.length,
    language
  });

  // Helper function to calculate reward based on total missions count
  const calculateReward = (type, totalCount) => {
    const multipliers = { daily: 10, weekly: 30, monthly: 100 };
    return (multipliers[type] || 10) * totalCount;
  };

  // Helper function to check if all missions of a type are completed
  const areAllMissionsCompleted = (missions) => {
    if (missions.length === 0) return false;

    // Check participating missions first
    const participatingMissions = missions.filter(m => m.participating);
    if (participatingMissions.length > 0) {
      return participatingMissions.every(mission => mission.completed);
    }

    // If no participating missions, check all missions
    return missions.every(mission => mission.completed);
  };

  // Helper function to get claim status from final claims data
  const getClaimStatus = (type) => {
    if (!finalClaimsData || !finalClaimsData.success || !finalClaimsData.data) {
      return { claimed: false, claimData: null };
    }

    const claimData = finalClaimsData.data[type];
    return {
      claimed: claimData !== null,
      claimData: claimData
    };
  };

  // Helper function to get completed missions count from actual mission arrays
  const getCompletedCount = (type) => {
    let missions;
    switch (type) {
      case 'daily':
        missions = dailyMissions;
        break;
      case 'weekly':
        missions = weeklyMissions;
        break;
      case 'monthly':
        missions = monthlyMissions;
        break;
      default:
        missions = [];
    }
    const result = missions.filter(mission => mission.completed).length;
    console.log(`Final Mission Claims: getCompletedCount(${type}) = ${result} (from ${missions.length} total)`);
    return result;
  };

  // Helper function to get total missions count from actual mission arrays
  const getTotalCount = (type) => {
    let missions;
    switch (type) {
      case 'daily':
        missions = dailyMissions;
        break;
      case 'weekly':
        missions = weeklyMissions;
        break;
      case 'monthly':
        missions = monthlyMissions;
        break;
      default:
        missions = [];
    }
    const result = missions.length;
    console.log(`Final Mission Claims: getTotalCount(${type}) = ${result}`);
    return result;
  };

  // Calculate data for each mission type - USE THE SAME LOGIC AS THE WORKING STATS
  const dailyCompleted = getCompletedCount('daily');
  const dailyTotal = getTotalCount('daily');
  const dailyAllCompleted = dailyCompleted === dailyTotal && dailyTotal > 0;
  const dailyClaimStatus = getClaimStatus('daily');

  console.log('Final Mission Claims: Daily progress calculation:', {
    dailyCompleted,
    dailyTotal,
    dailyAllCompleted
  });

  const weeklyCompleted = getCompletedCount('weekly');
  const weeklyTotal = getTotalCount('weekly');
  const weeklyAllCompleted = weeklyCompleted === weeklyTotal && weeklyTotal > 0;
  const weeklyClaimStatus = getClaimStatus('weekly');

  console.log('Final Mission Claims: Weekly progress calculation:', {
    weeklyCompleted,
    weeklyTotal,
    weeklyAllCompleted
  });

  const monthlyCompleted = getCompletedCount('monthly');
  const monthlyTotal = getTotalCount('monthly');
  const monthlyAllCompleted = monthlyCompleted === monthlyTotal && monthlyTotal > 0;
  const monthlyClaimStatus = getClaimStatus('monthly');

  console.log('Final Mission Claims: Monthly progress calculation:', {
    monthlyCompleted,
    monthlyTotal,
    monthlyAllCompleted
  });

  const finalMissionsResult = {
    daily: {
      id: 'final_daily',
      title: language === 'tr' ? 'Günlük Usta' : 'Daily Master',
      description: language === 'tr'
        ? dailyTotal === 0
          ? 'Şu anda günlük görev bulunmamaktadır'
          : 'Bu özel ödülün kilidini açmak için tüm günlük görevleri tamamla'
        : dailyTotal === 0
          ? 'No daily missions available at the moment'
          : 'Complete all daily missions to unlock this special reward',
      current: dailyCompleted,
      target: dailyTotal,
      reward: calculateReward('daily', dailyTotal),
      claimed: dailyClaimStatus.claimed,
      canClaim: dailyAllCompleted && !dailyClaimStatus.claimed && dailyTotal > 0,
      completedMissions: dailyCompleted,
      totalMissions: dailyTotal,
      claimData: dailyClaimStatus.claimData,
      isEmpty: dailyTotal === 0
    },
    weekly: {
      id: 'final_weekly',
      title: language === 'tr' ? 'Haftalık Efsane' : 'Weekly Legend',
      description: language === 'tr'
        ? weeklyTotal === 0
          ? 'Şu anda haftalık görev bulunmamaktadır'
          : 'Bu premium ödülün kilidini açmak için tüm haftalık görevleri tamamla'
        : weeklyTotal === 0
          ? 'No weekly missions available at the moment'
          : 'Complete all weekly missions to unlock this premium reward',
      current: weeklyCompleted,
      target: weeklyTotal,
      reward: calculateReward('weekly', weeklyTotal),
      claimed: weeklyClaimStatus.claimed,
      canClaim: weeklyAllCompleted && !weeklyClaimStatus.claimed && weeklyTotal > 0,
      completedMissions: weeklyCompleted,
      totalMissions: weeklyTotal,
      claimData: weeklyClaimStatus.claimData,
      isEmpty: weeklyTotal === 0
    },
    monthly: {
      id: 'final_monthly',
      title: language === 'tr' ? 'Aylık Şampiyon' : 'Monthly Champion',
      description: language === 'tr'
        ? monthlyTotal === 0
          ? 'Şu anda aylık görev bulunmamaktadır'
          : 'Bu nihai ödülün kilidini açmak için tüm aylık görevleri tamamla'
        : monthlyTotal === 0
          ? 'No monthly missions available at the moment'
          : 'Complete all monthly missions to unlock this ultimate reward',
      current: monthlyCompleted,
      target: monthlyTotal,
      reward: calculateReward('monthly', monthlyTotal),
      claimed: monthlyClaimStatus.claimed,
      canClaim: monthlyAllCompleted && !monthlyClaimStatus.claimed && monthlyTotal > 0,
      completedMissions: monthlyCompleted,
      totalMissions: monthlyTotal,
      claimData: monthlyClaimStatus.claimData,
      isEmpty: monthlyTotal === 0
    }
  };

  console.log('Final Mission Claims: Final missions result:', finalMissionsResult);
  return finalMissionsResult;
}

// Transform backend missions data to frontend format
function transformMissionsData(backendData, language = 'en', finalClaimsData = null, statsData = null) {
  if (!backendData || !backendData.success || !backendData.data) {
    console.warn('Missions API: Invalid backend missions data');
    return null;
  }

  const { eligibleMissions } = backendData.data;

  // Group missions by type
  const dailyMissions = [];
  const weeklyMissions = [];
  const monthlyMissions = [];

  eligibleMissions.forEach(mission => {
    // Check if user is participating (has user assignments)
    const hasUserAssignments = mission.userAssignments && mission.userAssignments.length > 0;

    // Check if mission is inactive (completed and in cooldown)
    const isInactive = isMissionInactive(mission.latestParticipation);
    const inactiveMessage = isInactive
      ? getCompletedMissionMessage(mission.latestParticipation.nextAllowedParticipationSeconds, language)
      : null;

    // Calculate progress from user assignments
    let totalProgress = 0;
    let totalTarget = 0;
    let isCompleted = false;
    let allObjectivesCompleted = true;

    if (hasUserAssignments) {
      mission.objectives.forEach(objective => {
        const userAssignment = mission.userAssignments.find(
          assignment => assignment.missionObjectiveId === objective.id
        );

        if (userAssignment) {
          const progress = parseInt(userAssignment.progress || '0');
          const target = parseInt(objective.targetValue || '1');

          totalProgress += progress;
          totalTarget += target;

          // Use the isCompleted field from userAssignment if available, otherwise fallback to calculation
          let isObjectiveCompleted;
          if (userAssignment.hasOwnProperty('isCompleted')) {
            isObjectiveCompleted = userAssignment.isCompleted;
            console.log(`Missions API: Using isCompleted field for objective ${objective.id}: ${isObjectiveCompleted}`);
          } else {
            // Fallback to manual calculation for backward compatibility
            isObjectiveCompleted = checkObjectiveCompletion(
              progress,
              target,
              objective.operator
            );
            console.log(`Missions API: Calculated completion for objective ${objective.id}: ${isObjectiveCompleted}`);
          }

          if (!isObjectiveCompleted) {
            allObjectivesCompleted = false;
          }
        } else {
          allObjectivesCompleted = false;
        }
      });

      // Mission is completed only if ALL objectives are completed
      isCompleted = allObjectivesCompleted && mission.objectives.length > 0;
    }

    const transformedMission = {
      id: mission.id.toString(),
      title: mission.name,
      description: mission.description,
      reward: mission.reward,
      category: mission.objectives?.[0]?.objectiveType?.toUpperCase() || 'GENERAL',
      current: totalProgress,
      target: totalTarget || parseInt(mission.objectives?.[0]?.targetValue || '1'),
      claimed: false, // TODO: Implement claim status
      completed: isCompleted,
      participating: hasUserAssignments,
      startDate: mission.startDate,
      endDate: mission.endDate,
      objectives: mission.objectives || [],
      userAssignments: mission.userAssignments || [],
      // New fields for handling inactive missions
      isInactive: isInactive,
      inactiveMessage: inactiveMessage,
      latestParticipation: mission.latestParticipation || null,
      nextAllowedParticipationSeconds: mission.latestParticipation?.nextAllowedParticipationSeconds || 0
    };

    switch (mission.missionType) {
      case 'daily':
        dailyMissions.push(transformedMission);
        break;
      case 'weekly':
        weeklyMissions.push(transformedMission);
        break;
      case 'monthly':
        monthlyMissions.push(transformedMission);
        break;
      default:
        dailyMissions.push(transformedMission); // Default to daily
    }
  });

  // Sort missions by priority within each category
  const sortedDailyMissions = sortMissionsByPriority(dailyMissions);
  const sortedWeeklyMissions = sortMissionsByPriority(weeklyMissions);
  const sortedMonthlyMissions = sortMissionsByPriority(monthlyMissions);

  // Create header slides based on available missions
  const headerSlides = [];

  if (sortedDailyMissions.length > 0) {
    headerSlides.push({
      id: 'slide_daily',
      title: language === 'tr' ? 'Günlük Görevler' : 'Daily Missions',
      subtitle: language === 'tr' ? 'Günlük görevleri tamamlayarak elmas ödülleri kazanın' : 'Complete daily missions to earn diamond rewards',
      backgroundColor: 'linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%)',
      textColor: '#ffffff'
    });
  }

  if (sortedWeeklyMissions.length > 0) {
    headerSlides.push({
      id: 'slide_weekly',
      title: language === 'tr' ? 'Haftalık Meydan Okumalar' : 'Weekly Challenges',
      subtitle: language === 'tr' ? 'Daha büyük meydan okumalara katılarak premium ödüller kazanın' : 'Take on bigger challenges for premium rewards',
      backgroundColor: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
      textColor: '#000000'
    });
  }

  if (sortedMonthlyMissions.length > 0) {
    headerSlides.push({
      id: 'slide_monthly',
      title: language === 'tr' ? 'Aylık Görevler' : 'Monthly Missions',
      subtitle: language === 'tr' ? 'Aylık görevleri tamamlayarak büyük ödüller kazanın' : 'Complete monthly missions for big rewards',
      backgroundColor: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
      textColor: '#ffffff'
    });
  }

  // If no missions, add a default slide
  if (headerSlides.length === 0) {
    headerSlides.push({
      id: 'slide_empty',
      title: language === 'tr' ? 'Görevler' : 'Missions',
      subtitle: language === 'tr' ? 'Şu anda mevcut görev bulunmamaktadır' : 'No missions available at the moment',
      backgroundColor: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
      textColor: '#ffffff'
    });
  }

  // Calculate final missions data
  const finalMissions = calculateFinalMissions(
    finalClaimsData,
    statsData,
    language,
    sortedDailyMissions,
    sortedWeeklyMissions,
    sortedMonthlyMissions
  );

  return {
    headerSlides,
    dailyMissions: sortedDailyMissions,
    weeklyMissions: sortedWeeklyMissions,
    monthlyMissions: sortedMonthlyMissions,
    finalMissions: finalMissions,
    stats: {
      diamonds: 0, // TODO: Get from user data
      completedToday: sortedDailyMissions.filter(m => m.claimed).length,
      totalToday: sortedDailyMissions.length,
      completedWeekly: sortedWeeklyMissions.filter(m => m.claimed).length,
      totalWeekly: sortedWeeklyMissions.length,
      completedMonthly: sortedMonthlyMissions.filter(m => m.claimed).length,
      totalMonthly: sortedMonthlyMissions.length
    }
  };
}

/**
 * Get missions data for the user
 * @param {Object} params - Request parameters
 * @param {string} params.language - Language code (en, tr)
 * @returns {Promise<Object>} Missions data
 */
export const getMissionsData = async ({ language = 'en' }) => {
  try {
    // Step 1: Create extended user
    console.log('Missions API: Creating extended user before fetching missions...');
    const extendedUser = await createExtendedUser();

    if (!extendedUser || !extendedUser.success) {
      console.warn('Missions API: Failed to create extended user, falling back to mock data');
      return mockMissionsData[language] || mockMissionsData.en;
    }

    console.log('Missions API: Extended user created successfully, proceeding with real missions fetch');

    // Store extended user data for later use
    try {
      sessionStorage.setItem('extendedUser', JSON.stringify(extendedUser.data));
    } catch (error) {
      console.warn('Missions API: Failed to store extended user data:', error);
    }

    // Step 2: Fetch real missions data, stats, and final claims in parallel
    const [realMissionsData, statsData, finalClaimsData] = await Promise.all([
      fetchRealMissionsData(),
      fetchMissionStats(),
      getLatestFinalMissionClaims()
    ]);

    if (!realMissionsData || !realMissionsData.success) {
      console.warn('Missions API: Failed to fetch real missions data, falling back to mock data');
      return mockMissionsData[language] || mockMissionsData.en;
    }

    // Step 3: Transform backend data to frontend format
    const transformedData = transformMissionsData(realMissionsData, language, finalClaimsData, statsData);

    if (!transformedData) {
      console.warn('Missions API: Failed to transform missions data, falling back to mock data');
      return mockMissionsData[language] || mockMissionsData.en;
    }

    // Step 4: Add real stats if available
    if (statsData && statsData.success) {
      transformedData.stats = {
        diamonds: statsData.data.balance || 0,
        totalParticipations: statsData.data.totalParticipations || 0,
        completedParticipations: statsData.data.completedParticipations || 0,
        completionRate: statsData.data.totalParticipations > 0
          ? Math.round((statsData.data.completedParticipations / statsData.data.totalParticipations) * 100)
          : 0,
        eligibleMissions: statsData.data.eligibleMissions || {},
        completedEligibleMissions: statsData.data.completedEligibleMissions || {},
        // Calculate totals for display
        completedToday: statsData.data.completedEligibleMissions?.daily || 0,
        totalToday: statsData.data.eligibleMissions?.daily || 0,
        completedWeekly: statsData.data.completedEligibleMissions?.weekly || 0,
        totalWeekly: statsData.data.eligibleMissions?.weekly || 0,
        completedMonthly: statsData.data.completedEligibleMissions?.monthly || 0,
        totalMonthly: statsData.data.eligibleMissions?.monthly || 0
      };
      console.log('Missions API: Real stats integrated successfully');
      console.log('Missions API: Working stats values:', {
        completedToday: transformedData.stats.completedToday,
        totalToday: transformedData.stats.totalToday,
        completedWeekly: transformedData.stats.completedWeekly,
        totalWeekly: transformedData.stats.totalWeekly,
        completedMonthly: transformedData.stats.completedMonthly,
        totalMonthly: transformedData.stats.totalMonthly
      });
    }

    console.log('Missions API: Successfully fetched and transformed real missions data');
    return transformedData;

  } catch (error) {
    console.error('Error fetching missions data:', error);
    console.log('Missions API: Falling back to mock data due to error');
    return mockMissionsData[language] || mockMissionsData.en;
  }
}

/**
 * Get shop items
 * @deprecated Use getMarketItems from market.js instead
 * @param {Object} params - Request parameters
 * @param {string} params.language - Language code (en, tr)
 * @returns {Promise<Array>} Shop items
 */
export const getShopItems = async ({ language = 'en' }) => {
  console.warn('getShopItems is deprecated. Use getMarketItems from market.js instead.');

  try {
    // Return empty array since we now use real market items
    return [];
  } catch (error) {
    console.error('Error fetching shop items:', error)
    throw error
  }
}

/**
 * Claim mission reward
 * @param {Object} params - Request parameters
 * @param {string} params.missionId - Mission ID
 * @returns {Promise<Object>} Claim result
 */
export const claimMissionReward = async ({ missionId }) => {
  try {
    // TODO: Replace with actual API call
    // const response = await fetch(`/api/missions/${missionId}/claim`, {
    //   method: 'POST',
    //   headers: {
    //     'Authorization': `Bearer ${localStorage.getItem('bearer')}`,
    //     'Content-Type': 'application/json'
    //   }
    // })
    
    // if (!response.ok) {
    //   throw new Error(`HTTP error! status: ${response.status}`)
    // }
    
    // return await response.json()

    // Mock delay to simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return { success: true, missionId }
  } catch (error) {
    console.error('Error claiming mission reward:', error)
    throw error
  }
}

/**
 * Purchase shop item
 * @param {Object} params - Request parameters
 * @param {string} params.itemId - Item ID
 * @param {number} params.price - Item price
 * @returns {Promise<Object>} Purchase result
 */
export const purchaseShopItem = async ({ itemId, price }) => {
  try {
    // TODO: Replace with actual API call
    // const response = await fetch(`/api/missions/shop/${itemId}/purchase`, {
    //   method: 'POST',
    //   headers: {
    //     'Authorization': `Bearer ${localStorage.getItem('bearer')}`,
    //     'Content-Type': 'application/json'
    //   },
    //   body: JSON.stringify({ price })
    // })
    
    // if (!response.ok) {
    //   throw new Error(`HTTP error! status: ${response.status}`)
    // }
    
    // return await response.json()

    // Mock delay to simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return { success: true, itemId, price }
  } catch (error) {
    console.error('Error purchasing shop item:', error)
    throw error
  }
}

/**
 * Participate in a mission
 * @param {Object} params - Request parameters
 * @param {string} params.missionId - Mission ID
 * @returns {Promise<Object>} Participation result
 */
export const participateInMission = async ({ missionId }) => {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    throw new Error('No bearer token found, user not logged in');
  }

  try {
    const response = await fetch(API_ENDPOINTS.MISSION_PARTICIPATE(missionId), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Missions API: Mission participation successful:', data);
    return data;
  } catch (error) {
    console.error('Error participating in mission:', error);
    throw error;
  }
};

/**
 * Complete/claim a mission
 * @param {Object} params - Request parameters
 * @param {string} params.missionId - Mission ID
 * @returns {Promise<Object>} Completion result
 */
export const completeMission = async ({ missionId }) => {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    throw new Error('No bearer token found, user not logged in');
  }

  try {
    const response = await fetch(API_ENDPOINTS.MISSION_COMPLETE(missionId), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle specific error cases
      if (response.status === 409) {
        throw new Error(data.message || 'Mission is already completed');
      } else if (response.status === 400) {
        throw new Error(data.message || 'Not all mission objectives are completed yet');
      } else {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }
    }

    console.log('Missions API: Mission completion successful:', data);
    return data;
  } catch (error) {
    console.error('Error completing mission:', error);
    throw error;
  }
};

/**
 * Create extended user via custom API
 * @returns {Promise<Object|null>} Extended user data or null if failed
 */
export const createExtendedUserAPI = createExtendedUser

/**
 * Fetch mission statistics
 * @returns {Promise<Object|null>} Mission stats or null if failed
 */
export const fetchMissionStatsAPI = fetchMissionStats

/**
 * Fetch mission reset times
 * @returns {Promise<Object|null>} Reset times data or null if failed
 */
async function fetchMissionResetTimes() {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    console.warn('Missions API: No bearer token found for reset times fetch');
    return null;
  }

  try {
    const response = await fetch(API_ENDPOINTS.MISSION_RESET_TIMES, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      console.warn(`Missions API: Reset times fetch failed with status ${response.status}`);
      return null;
    }

    const data = await response.json();
    console.log('Missions API: Reset times fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('Missions API: Failed to fetch reset times:', error);
    return null;
  }
}

/**
 * Get mission reset times
 * @returns {Promise<Object|null>} Reset times data or null if failed
 */
export const getMissionResetTimes = async () => {
  return await fetchMissionResetTimes();
};

/**
 * Claim final mission reward
 * @param {Object} params - Request parameters
 * @param {string} params.claimType - Claim type ('daily', 'weekly', or 'monthly')
 * @returns {Promise<Object>} Claim result
 */
export const claimFinalMissionReward = async ({ claimType }) => {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    throw new Error('No bearer token found, user not logged in');
  }

  // Validate claim type
  if (!['daily', 'weekly', 'monthly'].includes(claimType)) {
    throw new Error('Invalid claim type. Must be daily, weekly, or monthly');
  }

  try {
    const response = await fetch(API_ENDPOINTS.FINAL_MISSION_CLAIMS, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({ claimType })
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle specific error cases based on status codes
      switch (response.status) {
        case 400:
          if (data.message?.includes('Invalid claim type')) {
            throw new Error('Invalid claim type. Must be daily, weekly, or monthly');
          } else if (data.message?.includes('No active')) {
            throw new Error(data.message || `No active ${claimType} missions found`);
          } else if (data.message?.includes('not completed all')) {
            throw new Error(data.message || `You have not completed all ${claimType} missions yet`);
          } else {
            throw new Error(data.message || 'Invalid request');
          }
        case 409:
          throw new Error(data.message || `You have already claimed the ${claimType} final mission reward for this period`);
        case 401:
          throw new Error('Authorization required. Please log in again');
        case 404:
          throw new Error('User not found. Please contact support');
        case 500:
          throw new Error(data.message || 'Server error occurred while claiming reward');
        default:
          throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }
    }

    console.log('Final Mission Claims API: Claim successful:', data);
    return data;
  } catch (error) {
    console.error('Error claiming final mission reward:', error);
    throw error;
  }
};

/**
 * Get latest final mission claims for the user
 * @returns {Promise<Object>} Latest claims data
 */
export const getLatestFinalMissionClaims = async () => {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    console.warn('Final Mission Claims API: No bearer token found for claims fetch');
    return null;
  }

  try {
    const response = await fetch(API_ENDPOINTS.FINAL_MISSION_CLAIMS_ME, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      // Handle specific error cases
      switch (response.status) {
        case 401:
          console.warn('Final Mission Claims API: Authorization required');
          return null;
        case 404:
          console.warn('Final Mission Claims API: User not found');
          return null;
        case 500:
          console.error('Final Mission Claims API: Server error occurred');
          return null;
        default:
          console.warn(`Final Mission Claims API: Claims fetch failed with status ${response.status}`);
          return null;
      }
    }

    const data = await response.json();
    console.log('Final Mission Claims API: Latest claims fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('Final Mission Claims API: Failed to fetch latest claims:', error);
    return null;
  }
};

/**
 * Get bearer token from localStorage
 * @returns {string|null} Bearer token or null if not found
 */
export const getBearerTokenAPI = getBearerToken
