import { transformResponse } from './base'

const API_URL = 'https://sportsbet.esportings.com/proxy2'
const TREE_API_URL = 'https://sportsbet.esportings.com/proxy3'

/**
 * @typedef {Object} SportsbookToken
 * @property {string} accessToken - JWT access token for API authentication. Contains clientId, siteId, authToken, userExternalId, userId, language, countryCode, bookmakerId, currencyCode, iat (issued at), and exp (expiration) claims.
 * @property {string} streamingToken - JWT streaming token for real-time data connections. Contains userId, clientId, siteId, language, iat (issued at), and exp (expiration) claims.
 */

/**
 * @typedef {Object} SportsbookOutcome
 * @property {number} id - Outcome ID
 * @property {string} external_id - External outcome ID
 * @property {number} market_id - Market ID this outcome belongs to
 * @property {number} odds - Odds value
 * @property {boolean} active - Whether the outcome is active
 * @property {number} updated_at - Last update timestamp
 * @property {string} [name] - Outcome name (optional)
 * @property {null} team - Team information (always null)
 */

/**
 * @typedef {Object} SportsbookMarket
 * @property {number} id - Market ID
 * @property {number} game_id - Game ID this market belongs to
 * @property {number} season_id - Season ID
 * @property {number} tournament_id - Tournament ID
 * @property {string} external_id - External market ID
 * @property {string} status - Market status
 * @property {string} specifiers - Market specifiers
 * @property {number} updated_at - Last update timestamp
 * @property {string} [name] - Market name (optional)
 * @property {null} fast - Fast market indicator (always null)
 */

/**
 * @typedef {Object} SportsbookGameInfo
 * @property {number} match_status_code - Match status code
 * @property {string} match_status - Match status description
 * @property {Object} clock - Match clock information
 * @property {string} clock.match_time - Current match time
 * @property {boolean} clock.stopped - Whether the clock is stopped
 * @property {string} current_server - Current server information
 * @property {number} [home_score] - Home team score (if available)
 * @property {number} [away_score] - Away team score (if available)
 */

/**
 * @typedef {Object} SportsbookPeriodScore
 * @property {number} number - Period number
 * @property {number} away_score - Away team score for this period
 * @property {number} home_score - Home team score for this period
 * @property {number} match_status_code - Match status code for this period
 */

/**
 * @typedef {Object} SportsbookGame
 * @property {number} id - Game ID
 * @property {string} external_id - External game ID
 * @property {string} status - Game status
 * @property {string} start_time - Game start time in ISO format
 * @property {SportsbookGameInfo} info - Game information and scores
 * @property {number} sport_id - Sport ID
 * @property {number} category_id - Category ID
 * @property {number} tournament_id - Tournament ID
 * @property {boolean} live_odds - Whether live odds are available
 * @property {number} updated_at - Last update timestamp
 * @property {number} team_home_id - Home team ID
 * @property {number} team_away_id - Away team ID
 * @property {string} team_home_name - Home team name
 * @property {string} team_away_name - Away team name
 * @property {string} team_home_abbr - Home team abbreviation
 * @property {string} team_away_abbr - Away team abbreviation
 * @property {boolean} hosted_statistics - Whether hosted statistics are available
 * @property {string} team_home_external_id - Home team external ID
 * @property {string} team_away_external_id - Away team external ID
 * @property {SportsbookPeriodScore[]} [period_scores] - Period scores array
 * @property {number} markets_count - Number of markets
 * @property {number} fast_markets_count - Number of fast markets
 * @property {number} sort - Sort order
 * @property {Object} broadcasts - Broadcast information
 * @property {boolean} neutral_ground - Whether played on neutral ground
 * @property {number} super_tiebreak - Super tiebreak indicator
 * @property {number} no_advantage_scoring - No advantage scoring indicator
 * @property {string} team_home_source - Home team data source
 * @property {string} team_away_source - Away team data source
 * @property {boolean} has_tips - Whether tips are available
 * @property {boolean} is_in_hosted_statistics - Whether in hosted statistics
 * @property {boolean} is_in_live_match_tracker - Whether in live match tracker
 */

/**
 * @typedef {Object} SportsbookSeason
 * @property {number} id - Season ID
 * @property {string} external_id - External season ID
 * @property {number} sport_id - Sport ID
 * @property {number} category_id - Category ID
 * @property {number} tournament_id - Tournament ID
 * @property {string} start_date - Season start date
 * @property {string} end_date - Season end date
 * @property {string} name - Season name
 * @property {number} year - Season year
 * @property {number} markets_count - Number of markets
 * @property {number} sort - Sort order
 * @property {string[]} tags - Season tags
 */

/**
 * @typedef {Object} SportsbookTournament
 * @property {number} id - Tournament ID
 * @property {number} sport_id - Sport ID
 * @property {number} category_id - Category ID
 * @property {string} external_id - External tournament ID
 * @property {string} name - Tournament name
 * @property {string} alias - Tournament alias
 * @property {number} sort - Sort order
 * @property {string[]} tags - Tournament tags
 * @property {number} live_games_count - Number of live games
 * @property {number} games_count - Total number of games
 * @property {number} seasons_count - Number of seasons
 * @property {number} markets_count - Number of markets
 * @property {string} start_date - Tournament start date
 * @property {string} end_date - Tournament end date
 * @property {boolean} scheduled - Whether tournament is scheduled
 * @property {boolean} scheduled_end - Whether tournament end is scheduled
 * @property {Object} broadcasts - Broadcast information
 */

/**
 * @typedef {Object} SportsbookCategory
 * @property {number} id - Category ID
 * @property {number} sport_id - Sport ID
 * @property {string} external_id - External category ID
 * @property {string} name - Category name
 * @property {string} country_code - Country code
 * @property {string} alias - Category alias
 * @property {number} sort - Sort order
 * @property {string[]} tags - Category tags
 * @property {number} live_games_count - Number of live games
 * @property {number} games_count - Total number of games
 * @property {number} seasons_count - Number of seasons
 * @property {number} tournaments_count - Number of tournaments
 * @property {Object} broadcasts - Broadcast information
 */

/**
 * @typedef {Object} SportsbookSport
 * @property {number} id - Sport ID
 * @property {string} external_id - External sport ID
 * @property {string} name - Sport name
 * @property {string} alias - Sport alias
 * @property {number} sort - Sort order
 * @property {string[]} tags - Sport tags
 * @property {number} live_games_count - Number of live games
 * @property {number} games_count - Total number of games
 * @property {number} seasons_count - Number of seasons
 * @property {number} tournaments_count - Number of tournaments
 */

/**
 * @typedef {Object} SportsbookTreeData
 * @property {number} ts - Timestamp
 * @property {SportsbookSport[]} sports - Array of sports
 * @property {SportsbookCategory[]} categories - Array of categories
 * @property {SportsbookTournament[]} tournaments - Array of tournaments
 * @property {SportsbookSeason[]} seasons - Array of seasons
 * @property {SportsbookGame[]} games - Array of games
 * @property {SportsbookMarket[]} markets - Array of markets
 * @property {SportsbookOutcome[]} [outcomes] - Array of outcomes (optional)
 */

export const getToken = async () => {
  try {
    const response = await fetch(`${API_URL}/api/v1/app/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
          "authToken": "anonymous",
          "host": "https://sportsbet.esportings.com",
          "language": "en",
          "parents": [],
          "referrer": "",
          "siteId": 909,
          "userId": "2069051739"
      })
    })
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    /** @type {SportsbookToken} */
    const tokens = await response.json()

    return tokens
  } catch (error) {
    console.error('Error fetching token:', error)
  }
}

/**
 * Decodes raw sportsbook tree data arrays into structured objects.
 * Transforms array-based data format into properly typed objects based on the data type.
 *
 * @param {Object} rawData - Raw data object containing arrays for different entity types
 * @returns {SportsbookTreeData} Decoded tree data with structured objects
 */
export const decodeTreeData = (rawData) => {
  const result = {
    ts: rawData.ts || 0,
    sports: [],
    categories: [],
    tournaments: [],
    seasons: [],
    games: [],
    markets: [],
    outcomes: []
  }

  // Helper function to get status mapping (simplified version)
  const getGameStatus = (statusCode) => {
    const statusMap = {
      0: 'not_started',
      1: 'live',
      2: 'ended',
      3: 'postponed',
      4: 'cancelled'
    }
    return statusMap[statusCode] || 'not_started'
  }

  // Helper function to get team source mapping
  const getTeamSource = (sourceCode) => {
    const sourceMap = {
      0: 'betradar',
      1: 'other'
    }
    return sourceMap[sourceCode] || 'betradar'
  }

  // Process each data type
  Object.keys(rawData).forEach(dataType => {
    if (!Array.isArray(rawData[dataType])) return

    rawData[dataType].forEach(itemArray => {
      switch (dataType) {
        case "outcomes":
          result[dataType].push({
            id: itemArray[0],
            external_id: itemArray[1],
            market_id: itemArray[2],
            odds: itemArray[3],
            active: !!itemArray[4],
            updated_at: itemArray[5],
            name: itemArray[6] || undefined,
            team: null
          })
          break

        case "markets":
          result[dataType].push({
            id: itemArray[0],
            game_id: itemArray[1],
            season_id: itemArray[2],
            tournament_id: itemArray[3],
            external_id: itemArray[4],
            status: getGameStatus(itemArray[5]),
            specifiers: itemArray[6],
            updated_at: itemArray[7],
            name: itemArray[8] || undefined,
            fast: null
          })
          break

        case "games":
          const gameInfo = {
            match_status_code: itemArray[31],
            match_status: itemArray[32],
            clock: {
              match_time: itemArray[33],
              stopped: itemArray[34] === 1
            },
            current_server: itemArray[35]
          }

          if (itemArray[28] === 1) {
            gameInfo.home_score = itemArray[29]
            gameInfo.away_score = itemArray[30]
          }

          result[dataType].push({
            id: itemArray[0],
            external_id: itemArray[1],
            status: getGameStatus(itemArray[2]),
            start_time: new Date(itemArray[3]).toISOString(),
            info: gameInfo,
            sport_id: itemArray[4],
            category_id: itemArray[5],
            tournament_id: itemArray[6],
            live_odds: itemArray[7] === 1,
            updated_at: itemArray[8],
            team_home_id: itemArray[9],
            team_away_id: itemArray[10],
            team_home_name: itemArray[11],
            team_away_name: itemArray[12],
            team_home_abbr: itemArray[13],
            team_away_abbr: itemArray[14],
            hosted_statistics: itemArray[15],
            team_home_external_id: itemArray[16],
            team_away_external_id: itemArray[17],
            period_scores: itemArray[18]?.map(periodArray => ({
              number: periodArray[0],
              away_score: periodArray[1],
              home_score: periodArray[2],
              match_status_code: periodArray[3]
            })),
            markets_count: itemArray[19],
            fast_markets_count: itemArray[20],
            sort: itemArray[21],
            broadcasts: itemArray[22],
            neutral_ground: itemArray[23] === 1,
            super_tiebreak: itemArray[24],
            no_advantage_scoring: itemArray[25],
            team_home_source: getTeamSource(itemArray[26]),
            team_away_source: getTeamSource(itemArray[27]),
            has_tips: itemArray[36] === 1,
            is_in_hosted_statistics: itemArray[37],
            is_in_live_match_tracker: itemArray[38]
          })
          break

        case "seasons":
          result[dataType].push({
            id: itemArray[0],
            external_id: itemArray[1],
            sport_id: itemArray[2],
            category_id: itemArray[3],
            tournament_id: itemArray[4],
            start_date: itemArray[5],
            end_date: itemArray[6],
            name: itemArray[7],
            year: itemArray[8],
            markets_count: itemArray[9],
            sort: itemArray[10],
            tags: itemArray[11]
          })
          break

        case "tournaments":
          result[dataType].push({
            id: itemArray[0],
            sport_id: itemArray[1],
            category_id: itemArray[2],
            external_id: itemArray[3],
            name: itemArray[4],
            alias: itemArray[5],
            sort: itemArray[6],
            tags: itemArray[7],
            live_games_count: itemArray[8],
            games_count: itemArray[9],
            seasons_count: itemArray[10],
            markets_count: itemArray[11],
            start_date: itemArray[12],
            end_date: itemArray[13],
            scheduled: itemArray[14],
            scheduled_end: itemArray[15],
            broadcasts: itemArray[16]
          })
          break

        case "categories":
          result[dataType].push({
            id: itemArray[0],
            sport_id: itemArray[1],
            external_id: itemArray[2],
            name: itemArray[3],
            country_code: itemArray[4],
            alias: itemArray[5],
            sort: itemArray[6],
            tags: itemArray[7],
            live_games_count: itemArray[8],
            games_count: itemArray[9],
            seasons_count: itemArray[10],
            tournaments_count: itemArray[11],
            broadcasts: itemArray[12]
          })
          break

        case "sports":
          result[dataType].push({
            id: itemArray[0],
            external_id: itemArray[1],
            name: itemArray[2],
            alias: itemArray[3],
            sort: itemArray[4],
            tags: itemArray[5],
            live_games_count: itemArray[6],
            games_count: itemArray[7],
            seasons_count: itemArray[8],
            tournaments_count: itemArray[9]
          })
          break
      }
    })
  })

  return result
}

/**
 * Retrieves sportsbook tree data including sports, categories, tournaments, seasons, games, and markets.
 * Makes a POST request to the sportsbook tree API using the access token from getToken().
 *
 * @async
 * @function getTree
 * @returns {Promise<SportsbookTreeData|undefined>} A promise that resolves to decoded tree data, or undefined if an error occurs.
 * @throws {Error} Throws an error if the network response is not ok, if no access token is available, or if the API request fails.
 *
 * @example
 * // Get sportsbook tree data
 * const treeData = await getTree();
 * if (treeData) {
 *   console.log('Sports:', treeData.sports);
 *   console.log('Games:', treeData.games);
 *   console.log('Markets:', treeData.markets);
 * }
 */
export const getTree = async (tokens) => {
  try {
    const response = await fetch(`${TREE_API_URL}/api/v1.1/data/tree`, {
      headers: {
        'Authorization': `Bearer ${tokens.accessToken}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const rawData = await response.json()

    // Decode the raw array data into structured objects
    const decodedData = decodeTreeData(rawData)

    return decodedData
  } catch (error) {
    console.error('Error fetching tree data:', error)
  }
}

/**
 * Retrieves sportsbook markets data using GET method.
 * Makes a GET request to the sportsbook markets API using provided tokens.
 *
 * @async
 * @function getMarkets
 * @param {SportsbookToken} tokens - The tokens object containing accessToken and streamingToken
 * @returns {Promise<SportsbookTreeData|undefined>} A promise that resolves to decoded markets data, or undefined if an error occurs.
 * @throws {Error} Throws an error if the network response is not ok, if no access token is available, or if the API request fails.
 *
 * @example
 * // Get sportsbook markets data with existing tokens
 * const tokens = await getToken();
 * const marketsData = await getMarkets(tokens);
 * if (marketsData) {
 *   console.log('Sports:', marketsData.sports);
 *   console.log('Games:', marketsData.games);
 *   console.log('Markets:', marketsData.markets);
 * }
 */
export const getMarkets = async (tokens, { entityIds = [], group = "main", names = false }) => {
  try {
    if (!tokens?.accessToken) {
      throw new Error('No access token provided')
    }

    const response = await fetch(`${TREE_API_URL}/api/v1.1/data/markets`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${tokens.accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        entityIds,
        group,
        names
      })
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const rawData = await response.json()

    // Decode the raw array data into structured objects
    const decodedData = decodeTreeData(rawData)

    return decodedData
  } catch (error) {
    console.error('Error fetching markets data:', error)
  }
}
