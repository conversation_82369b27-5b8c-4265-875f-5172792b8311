import { API_URL, transformResponse } from './base'

// Helper function to calculate browser fingerprint
const calculateFingerprint = async () => {
  const fingerprintString = "".concat(`${window.navigator.appCodeName}/${window.navigator.appVersion}`, "|").concat(window.innerWidth.toString().concat(window.innerHeight.toString(), "|").concat(window.navigator.language));

  // Hash the fingerprint string with SHA-256
  const encoder = new TextEncoder();
  const data = encoder.encode(fingerprintString);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

  return hashHex;
};

// Helper function to calculate HMAC-SHA256 signature
const calculateSignature = async (fingerprint, rt, d) => {
  const message = JSON.stringify({ fingerprint, rt, d });
  const key = 'EBTLB_BASE_VAR'; // Using the same key as x-api-auth

  const encoder = new TextEncoder();
  const keyData = encoder.encode(key);
  const messageData = encoder.encode(message);

  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
  const signatureArray = Array.from(new Uint8Array(signature));
  const signatureHex = signatureArray.map(b => b.toString(16).padStart(2, '0')).join('');

  return signatureHex;
};

// Helper function to format timestamp as YYYY-MM-DDHH:mm
const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day}${hours}:${minutes}`;
};

/**
 * @typedef {Object} GameProvider
 * @property {number} id - Provider ID
 * @property {string} identifier - Provider identifier
 * @property {string} name - Provider name
 * @property {string} image - Provider image URL
 * @property {string[]} currencies - Supported currencies
 * @property {string[]} restrictions - Country restrictions
 * @property {number} system_id - System ID
 */

/**
 * @typedef {Object} GameInfo
 * @property {string} currencies - JSON string of supported currencies
 * @property {string} image - Game image URL
 * @property {string} color - Game color
 * @property {string} identifier - Game identifier
 * @property {number} id - Game ID
 * @property {string} slug - Game slug
 * @property {number} merchant_id - Merchant ID
 * @property {number} is_live - Whether game is live
 * @property {string} name - Game name
 * @property {string} payout - Game payout percentage
 * @property {string} description - Game description
 * @property {number} has_demo - Whether game has demo mode
 * @property {string[]} blacklist - Blacklisted countries
 * @property {GameProvider} game_provider - Game provider information
 * @property {string} provider - Provider name
 * @property {any} seo - SEO information
 */

/**
 * @typedef {Object} GameInfoResponse
 * @property {GameInfo} game - Game information
 * @property {boolean} favorited - Whether game is favorited
 */

/**
 * @typedef {Object} GameUrlResponse
 * @property {string} url - JSON string containing game configuration
 */

/**
 * Fetches game information by slug
 * @param {string} slug - Game slug
 * @param {string} [language='en'] - Language code
 * @param {number} [rt] - Request timestamp
 * @returns {Promise<GameInfoResponse>} Game information
 */
export const getGameInfo = async (slug, language = 'en', rt = Math.floor(Date.now() / 1000)) => {
  try {
    const response = await fetch(`${API_URL}/api/player/public/games2/play-game/${slug}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('bearer')}`
      },
      body: JSON.stringify({
        slug,
        language,
        rt
      })
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch game information')
    }

    return data.data
  } catch (error) {
    console.error('Error fetching game info:', error)
    throw error
  }
}

/**
 * Fetches game URL for playing
 * @param {number} gameId - Game ID
 * @param {string} currency - Currency code
 * @param {string} type - Game type ('demo' or 'real')
 * @param {string} [language='en'] - Language code
 * @param {number} [rt] - Request timestamp
 * @returns {Promise<GameUrlResponse>} Game URL information
 */
export const getGameUrl = async (gameId, currency, type, language = 'en', rt = Math.floor(Date.now() / 1000)) => {
  try {
    const token = localStorage.getItem('bearer')
    let headers = {
      'Content-Type': 'application/json',
    }
    if (token && type === 'real') {
      const fingerprint = await calculateFingerprint();
      const rt = Date.now();
      const d = formatTimestamp(rt);
      const signature = await calculateSignature(fingerprint, rt, d);

      headers = {
        ...headers,
        Authorization: `Bearer ${localStorage.getItem('bearer')}`,
        'x-api-auth': 'EBTLB_BASE_VAR',
        'x-fingerprint': fingerprint,
        'x-signature': signature
      }
    }

    const response = await fetch(`${API_URL}/api/player/public/games2/get-url`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({
        id: gameId,
        code: type === 'real' ? (localStorage.getItem('wallet') ?? 'TRY') : undefined,
        currency,
        type,
        language,
        device: 'desktop',
        rt
      })
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch game URL')
    }

    return data.data
  } catch (error) {
    console.error('Error fetching game URL:', error)
    throw error
  }
}

/**
 * @typedef {Object} Customer
 * @property {number} id - Customer ID
 * @property {string} username - Customer username
 * @property {string} masked_username - Masked username
 * @property {any} last_action - Last action timestamp
 */

/**
 * @typedef {Object} GameBet
 * @property {number} id - Bet ID
 * @property {number} merchant_id - Merchant ID
 * @property {number} website_id - Website ID
 * @property {number} customer_id - Customer ID
 * @property {number} player_id - Player ID
 * @property {number} wallet_id - Wallet ID
 * @property {number} game_id - Game ID
 * @property {number} debit_id - Debit ID
 * @property {string} amount - Bet amount
 * @property {string} income - Win amount
 * @property {string} multiplier - Win multiplier
 * @property {string} usd_amount - USD bet amount
 * @property {string} usd_income - USD win amount
 * @property {number} timestamp - Bet timestamp
 * @property {string} currency - Currency code
 * @property {string} wallet_currency - Wallet currency code
 * @property {Customer} customer - Customer information
 */

/**
 * Fetches game bets data (big wins, lucky wins, challenges)
 * @param {number} gameId - Game ID
 * @param {string} type - Bet type ('big-wins', 'lucky-wins', 'challenges')
 * @param {number} [limit=10] - Number of results to fetch
 * @param {number} [rt] - Request timestamp
 * @returns {Promise<GameBet[]>} Array of game bets
 */
export const getGameBets = async (gameId, type, limit = 10, rt = Math.floor(Date.now() / 1000)) => {
  try {
    const response = await fetch(`${API_URL}/api/player/public/debits/casino/game/${gameId}/${limit}/${type}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('bearer')}`
      },
      body: JSON.stringify({
        type,
        limit: limit,
        game_id: gameId,
        rt
      })
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch game bets')
    }

    return data.data
  } catch (error) {
    console.error('Error fetching game bets:', error)
    throw error
  }
}

/**
 * @typedef {Object} GameInfo
 * @property {number} id - Game ID
 * @property {string} name - Game name
 * @property {string} slug - Game slug
 * @property {string} provider - Game provider
 * @property {string[]} blacklist - Blacklisted countries
 * @property {Object} game_website - Game website information
 * @property {Object} game_provider - Game provider information
 */

/**
 * @typedef {Object} AllGameBet
 * @property {number} id - Bet ID
 * @property {number} merchant_id - Merchant ID
 * @property {number} website_id - Website ID
 * @property {number} customer_id - Customer ID
 * @property {number} player_id - Player ID
 * @property {number} wallet_id - Wallet ID
 * @property {number} game_id - Game ID
 * @property {number} debit_id - Debit ID
 * @property {string} amount - Bet amount
 * @property {string} income - Win amount
 * @property {string} currency - Currency code
 * @property {string} multiplier - Win multiplier
 * @property {number} timestamp - Bet timestamp
 * @property {string} wallet_currency - Wallet currency code
 * @property {Customer} customer - Customer information
 * @property {GameInfo} game - Game information
 */

/**
 * Fetches all casino bets data (all bets, high rollers, high winners)
 * @param {string} type - Bet type ('all-bets', 'high-rollers', 'high-winners')
 * @param {number} [limit=10] - Number of results to fetch
 * @param {number} [rt] - Request timestamp
 * @returns {Promise<AllGameBet[]>} Array of all game bets
 */
export const getAllCasinoBets = async (type, limit = 10, rt = Math.floor(Date.now() / 1000)) => {
  try {
    const response = await fetch(`${API_URL}/api/player/public/debits/casino/${limit}/${type}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('bearer')}`
      },
      body: JSON.stringify({
        type,
        limit,
        rt
      })
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch casino bets')
    }

    return data.data
  } catch (error) {
    console.error('Error fetching casino bets:', error)
    throw error
  }
}
