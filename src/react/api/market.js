// Market API with real API integration

import { ENVIRONMENT_HOST } from "../config/environment";

// Helper function to get bearer token
const getBearerToken = () => {
  return localStorage.getItem("bearer");
};

async function createExtendedUser() {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    console.log('Missions API: No bearer token found, user not logged in');
    return null;
  }

  try {
    const response = await fetch(`${ENVIRONMENT_HOST}/api/makroz/extended-users/me`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      console.warn(`Missions API: Extended user creation failed with status ${response.status}`);
      return null;
    }

    const data = await response.json();
    console.log('Missions API: Extended user created successfully:', data);
    return data;
  } catch (error) {
    console.error('Missions API: Failed to create extended user:', error);
    return null;
  }
}

/**
 * Get market items
 * @param {Object} [params] - Request parameters
 * @returns {Promise<Object>} Market data with items and user stats
 */
export const getMarketItems = async (params = {}) => {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    throw new Error('User unathorized.');
  }

  try {
    // Step 1: Create extended user
    console.log('Market API: Creating extended user before fetching market items...');
    const extendedUser = await createExtendedUser();

    if (!extendedUser || !extendedUser.success) {
      throw new Error("Something went wrong...");
    }

    // Store extended user data for later use
    try {
      sessionStorage.setItem('extendedUser', JSON.stringify(extendedUser.data));
    } catch (error) {
      console.warn('Missions API: Failed to store extended user data:', error);
    }

    console.log('Market API: Fetching market products from real API...');

    const response = await fetch(`${ENVIRONMENT_HOST}/api/v1/makroz/public/market-products`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = (await response.json())["data"];

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error("Error fetching market items:", error);
    throw error;
  }
};

/**
 * Purchase market item
 * @param {Object} params - Request parameters
 * @param {number} params.productId - Product ID
 * @param {string} [params.currency] - Currency (optional)
 * @param {Object} [params.providers] - Providers object with provider IDs as keys and slot arrays as values (optional)
 * @returns {Promise<Object>} Purchase result
 */
export const purchaseMarketItem = async ({
  productId,
  currency,
  providers,
}) => {
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    throw new Error("User unathorized.");
  }

  try {
    // Step 1: Create extended user
    console.log('Market API: Creating extended user before purchasing market item...');
    const extendedUser = await createExtendedUser();

    if (!extendedUser || !extendedUser.success) {
      throw new Error("Something went wrong...");
    }

    // Store extended user data for later use
    try {
      sessionStorage.setItem('extendedUser', JSON.stringify(extendedUser.data));
    } catch (error) {
      console.warn('Market API: Failed to store extended user data:', error);
    }

    console.log('Market API: Purchasing market item from real API...');

    // Prepare request body
    const requestBody = {
      productId
    };

    // Add optional parameters if provided
    if (currency) {
      requestBody.currency = currency;
    }

    if (providers) {
      requestBody.providers = providers;
    }

    const response = await fetch(`${ENVIRONMENT_HOST}/api/v1/makroz/public/market-product-requests`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error("Error purchasing market item:", error);
    throw error;
  }
};

/**
 * Get purchase history
 * @param {Object} [params] - Request parameters
 * @param {number} [params.page] - Page number (optional, default: 1)
 * @param {number} [params.limit] - Items per page (optional, default: 10)
 * @returns {Promise<Object>} Purchase history data
 */
export const getPurchaseHistory = async (params = {}) => {
  const { page = 1, limit = 100 } = params;
  const bearerToken = getBearerToken();

  if (!bearerToken) {
    throw new Error("No bearer token found, user not logged in");
  }

  try {
    // Get user data from session storage to get the user ID
    const userData = JSON.parse(sessionStorage.getItem("extendedUser"));
    if (!userData || !userData.externalId) {
      throw new Error("User data not found. Please refresh the page and try again.");
    }

    console.log('Market API: Fetching purchase history from real API...');

    const response = await fetch(`${ENVIRONMENT_HOST}/api/v1/makroz/public/market-product-requests?userId=${userData.externalId}&page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error("Error fetching purchase history:", error);
    throw error;
  }
};
