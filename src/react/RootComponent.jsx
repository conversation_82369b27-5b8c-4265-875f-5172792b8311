import { useEffect, useState } from 'react'
import ReactShadowRoot from 'react-shadow-root'

export const RootComponent = ({ Component, Stylesheet, ...props }) => {
  const [language, setLanguage] = useState(() => document.documentElement.lang || 'en')
  const [accessToken, setAccessToken] = useState(null)
  const i18n = (props.i18n ?? {})[language] ?? {}

  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
          setLanguage(document.documentElement.lang || "en")
          break;
        }
      }
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['lang'],
    });

    return () => {
      observer.disconnect();
    };
  }, [])

  // @todo improve
  useEffect(() => {
    let timeout = null
    const keys = ['bearer']

    const onValueChange = (key, newValue) => {
      if (key === 'bearer') {
        setAccessToken(newValue)
      }
    }

    const onStorageUpdate = () => {
      for (const key of keys) {
        const newValue = localStorage.getItem(key)
        onValueChange(key, newValue)
      }

      timeout = setTimeout(onStorageUpdate, 1000)
    }

    onStorageUpdate()

    return () => {
      if (timeout) clearTimeout(timeout)
    }
  }, [])

  return (
    <ReactShadowRoot>
      <Component
        location={location}
        language={language}
        i18n={i18n}
        accessToken={accessToken}
        isAuthenticated={accessToken !== null}
        root={props.root}
      />
      {Stylesheet && <style dangerouslySetInnerHTML={{ __html: Stylesheet }}></style>}
    </ReactShadowRoot>
  )
}
