import { useState, useEffect } from 'react';
import { getMissionResetTimes } from '../api/missions.js';
import { formatCountdown } from '../utils/timeUtils.js';

/**
 * Custom hook for managing mission reset times and countdown timers
 * @returns {Object} Object containing reset times and countdown states
 */
export const useMissionResetTimes = () => {
  const [resetTimes, setResetTimes] = useState({
    daily: { secondsUntilReset: 0, resetTime: null },
    weekly: { secondsUntilReset: 0, resetTime: null },
    monthly: { secondsUntilReset: 0, resetTime: null }
  });
  
  const [countdowns, setCountdowns] = useState({
    daily: { days: 0, hours: 0, minutes: 0, seconds: 0 },
    weekly: { days: 0, hours: 0, minutes: 0, seconds: 0 },
    monthly: { days: 0, hours: 0, minutes: 0, seconds: 0 }
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch reset times from API
  const fetchResetTimes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await getMissionResetTimes();
      
      if (data && data.success && data.data) {
        setResetTimes(data.data);
        
        // Initialize countdowns
        const newCountdowns = {};
        Object.keys(data.data).forEach(type => {
          const seconds = data.data[type].secondsUntilReset;
          newCountdowns[type] = formatCountdown(seconds);
        });
        setCountdowns(newCountdowns);
        
        console.log('Mission reset times loaded:', data.data);
      } else {
        throw new Error('Invalid reset times data received');
      }
    } catch (err) {
      console.error('Failed to fetch mission reset times:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchResetTimes();
  }, []);

  // Update countdowns every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdowns(prevCountdowns => {
        const newCountdowns = { ...prevCountdowns };
        let hasChanges = false;

        Object.keys(newCountdowns).forEach(type => {
          const countdown = newCountdowns[type];
          let { days, hours, minutes, seconds } = countdown;

          // Decrement time
          if (seconds > 0) {
            seconds--;
            hasChanges = true;
          } else if (minutes > 0) {
            minutes--;
            seconds = 59;
            hasChanges = true;
          } else if (hours > 0) {
            hours--;
            minutes = 59;
            seconds = 59;
            hasChanges = true;
          } else if (days > 0) {
            days--;
            hours = 23;
            minutes = 59;
            seconds = 59;
            hasChanges = true;
          }

          newCountdowns[type] = { days, hours, minutes, seconds };

          // If countdown reached zero, refetch reset times
          if (days === 0 && hours === 0 && minutes === 0 && seconds === 0) {
            console.log(`${type} missions reset time reached, refetching...`);
            setTimeout(fetchResetTimes, 1000); // Refetch after 1 second
          }
        });

        return hasChanges ? newCountdowns : prevCountdowns;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Refresh function for manual updates
  const refresh = () => {
    fetchResetTimes();
  };

  return {
    resetTimes,
    countdowns,
    loading,
    error,
    refresh
  };
};
