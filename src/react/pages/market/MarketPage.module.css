/* Market Page Styles */
.container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 40px 20px;
  box-sizing: border-box;
  min-height: 100vh;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 48px;
  padding: 60px 20px;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 50%, #1e40af 100%);
  border-radius: 32px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(24, 207, 251, 0.3);
}



.historyButton {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.15) 0%, rgba(10, 195, 198, 0.1) 100%);
  border: 2px solid rgba(24, 207, 251, 0.3);
  color: rgba(255, 255, 255, 0.9);
  padding: 10px 16px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
  white-space: nowrap;
}

.historyButton:hover {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.25) 0%, rgba(10, 195, 198, 0.2) 100%);
  border-color: rgba(24, 207, 251, 0.5);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(24, 207, 251, 0.3);
}

.historyButton svg {
  font-size: 1.1rem;
}

.headerBackground {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  animation: backgroundShift 20s ease-in-out infinite;
}

.floatingElement {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.floatingElement:nth-child(1) {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floatingElement:nth-child(2) {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floatingElement:nth-child(3) {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 70%;
  animation-delay: 4s;
}

.headerContent {
  position: relative;
  z-index: 2;
}

.titleSection {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
  animation: slideInFromTop 1s ease-out;
}

.titleIcon {
  font-size: 3rem;
  animation: bounce 2s ease-in-out infinite;
}

.title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0;
  background: linear-gradient(45deg, #ffffff, #e0f2fe, #ffffff);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer 3s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
}

.titleAccent {
  width: 4px;
  height: 60px;
  background: linear-gradient(to bottom, transparent, #ffffff, transparent);
  border-radius: 2px;
  animation: pulse 2s ease-in-out infinite;
}

.subtitle {
  font-size: 1.3rem;
  margin: 0 0 40px 0;
  opacity: 0.95;
  animation: fadeInUp 1s ease-out 0.3s both;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.userStats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.pointsDisplay {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.15);
  padding: 20px 32px;
  border-radius: 20px;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.pointsDisplay:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(24, 207, 251, 0.4);
  border-color: rgba(255, 255, 255, 0.4);
}

.pointsIcon {
  width: 32px;
  height: 32px;
}

.pointsIcon svg {
  width: 100%;
  height: 100%;
}

.pointsInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.pointsValue {
  font-size: 1.8rem;
  font-weight: 800;
  line-height: 1;
  animation: countUp 1s ease-out 0.8s both;
}

.pointsLabel {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.pointsPulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  border-radius: 20px;
  transform: translate(-50%, -50%) scale(0);
  animation: pulseEffect 3s ease-in-out infinite;
}

.statsGrid {
  display: flex;
  gap: 20px;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 16px 20px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.statItem:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #ffffff;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.8;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
}

/* Filters */
.filtersSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 32px;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filterLabel {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 1rem;
  flex-shrink: 0;
}

.filterSelect {
  min-width: 200px;
}

/* Market Grid */
.marketGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(24, 207, 251, 0.2);
  border-top: 4px solid #18cffb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Header Animations */
@keyframes backgroundShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.5;
    transform: scaleY(0.8);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes sparkle {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    filter: brightness(1.3);
  }
}

@keyframes countUp {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseEffect {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.loadingText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0;
}

/* Error State */
.errorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.errorText {
  color: #ff6b6b;
  font-size: 1.1rem;
  text-align: center;
  margin: 0;
  padding: 24px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(12px);
}

/* Empty State */
.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.emptyText {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.1rem;
  text-align: center;
  margin: 0;
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(12px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .marketGrid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 20px 16px;
  }

  .header {
    margin-bottom: 32px;
    padding: 40px 16px;
  }



  .titleSection {
    gap: 12px;
    margin-bottom: 16px;
  }

  .titleIcon {
    font-size: 2.5rem;
  }

  .title {
    font-size: 2.8rem;
  }

  .titleAccent {
    height: 50px;
  }

  .subtitle {
    font-size: 1.1rem;
    margin-bottom: 32px;
  }

  .userStats {
    gap: 24px;
  }

  .pointsDisplay {
    padding: 16px 24px;
    gap: 12px;
  }

  .pointsValue {
    font-size: 1.5rem;
  }

  .statsGrid {
    gap: 16px;
  }

  .statItem {
    padding: 12px 16px;
  }

  .statValue {
    font-size: 1.3rem;
  }

  .filtersSection {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filterGroup {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filterSelect {
    min-width: auto;
    width: 100%;
  }

  .historyButton {
    align-self: center;
    padding: 10px 16px;
    font-size: 0.85rem;
  }

  .marketGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .loadingContainer,
  .errorContainer,
  .emptyContainer {
    padding: 60px 16px;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border-width: 3px;
  }

  .loadingText,
  .errorText,
  .emptyText {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 16px 12px;
  }

  .header {
    padding: 32px 12px;
    margin-bottom: 24px;
  }

  .historyButton {
    padding: 8px 12px;
    font-size: 0.8rem;
    gap: 6px;
  }

  .titleSection {
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
  }

  .titleIcon {
    font-size: 2rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .titleAccent {
    width: 60px;
    height: 4px;
  }

  .subtitle {
    font-size: 1rem;
    margin-bottom: 24px;
  }

  .userStats {
    flex-direction: column;
    gap: 16px;
  }

  .pointsDisplay {
    padding: 14px 20px;
    gap: 10px;
  }

  .pointsValue {
    font-size: 1.4rem;
  }

  .statsGrid {
    gap: 12px;
  }

  .statItem {
    padding: 10px 14px;
  }

  .statValue {
    font-size: 1.2rem;
  }

  .floatingElement {
    display: none;
  }

  .filtersSection {
    padding: 12px;
  }

  .loadingContainer,
  .errorContainer,
  .emptyContainer {
    padding: 40px 12px;
  }

  .errorText,
  .emptyText {
    padding: 20px;
    font-size: 0.9rem;
  }
}
