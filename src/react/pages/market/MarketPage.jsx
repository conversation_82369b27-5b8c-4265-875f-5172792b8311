import { useState, useEffect } from "react";
import { MarketItemCard } from "../../components/MarketItemCard/MarketItemCard";
import { MarketItemModal } from "./components/MarketItemModal";
import { PurchaseHistoryModal } from "./components/PurchaseHistoryModal";
import { CustomSelect } from "../../components/CustomSelect/CustomSelect.jsx";
import { getMarketItems, purchaseMarketItem } from "../../api/market";
import styles from "./MarketPage.module.css";
import { IoDiamondOutline, IoTimeOutline } from "react-icons/io5";

export const MARKET_PRODUCT_CATEGORIES = {
  FreeSpins: "free_spins",
  Cash: "cash",
  Reload: "reload",
  Scatter: "scatter",
};

export const i18n = {
  en: {
    title: "Market",
    subtitle: "Exchange your points for amazing rewards",
    points: "Points",
    filterByType: "Filter by Type",
    allTypes: "All Types",
    free_spins: "Free Spins",
    cash: "Cash",
    reload: "Reload",
    scatter: "Scatter",
    vip: "VIP",
    cashback: "Cashback",
    loading: "Loading market items...",
    error: "Failed to load market items. Please try again later.",
    authError: "Please log in to access the market.",
    noItems: "No items available at the moment.",
    purchase: "Purchase",
    purchased: "Purchased",
    notEnoughPoints: "Not enough points",
    selectProvider: "Select Provider",
    selectSlot: "Select Slot",
    confirmPurchase: "Confirm Purchase",
    cancel: "Cancel",
    purchaseSuccess: "Purchase successful!",
    purchaseError: "Purchase failed. Please try again.",
    oneTimeOnly: "One time only",
    limitedOffer: "Limited Offer",
    available: "Available",
    unavailable: "Unavailable",
    // History related
    history: "History",
    purchaseHistory: "Purchase History",
    filterByStatus: "Filter by Status",
    allStatuses: "All",
    pending: "Pending",
    accepted: "Accepted",
    rejected: "Rejected",
    loadingHistory: "Loading purchase history...",
    historyError: "Failed to load purchase history. Please try again.",
    noPurchases: "No purchases found.",
    noFilteredPurchases: "No purchases found for the selected status.",
    price: "Price",
    purchaseDate: "Date",
    provider: "Provider",
    slot: "Slot",
    expiryDate: "Expires",
    reason: "Reason",
    previous: "Previous",
    next: "Next",
    pageOf: "Page",
    of: "of",
  },
  tr: {
    title: "Market",
    subtitle: "Puanlarınızı harika ödüllerle değiştirin",
    points: "Puan",
    filterByType: "Türe Göre Filtrele",
    allTypes: "Tüm Türler",
    free_spins: "Bedava Spin",
    cash: "Nakit",
    reload: "Yeniden Yükle",
    scatter: "Dağıt",
    vip: "VIP",
    cashback: "Cashback",
    loading: "Market öğeleri yükleniyor...",
    error: "Market öğeleri yüklenemedi. Lütfen tekrar deneyin.",
    authError: "Markete erişmek için lütfen giriş yapın.",
    noItems: "Şu anda mevcut öğe yok.",
    purchase: "Satın Al",
    purchased: "Satın Alındı",
    notEnoughPoints: "Yeterli puan yok",
    selectProvider: "Sağlayıcı Seç",
    selectSlot: "Slot Seç",
    confirmPurchase: "Satın Almayı Onayla",
    cancel: "İptal",
    purchaseSuccess: "Satın alma başarılı!",
    purchaseError: "Satın alma başarısız. Lütfen tekrar deneyin.",
    oneTimeOnly: "Sadece bir kez",
    limitedOffer: "Sınırlı Teklif",
    available: "Mevcut",
    unavailable: "Mevcut Değil",
    // History related
    history: "Geçmiş",
    purchaseHistory: "Satın Alma Geçmişi",
    filterByStatus: "Duruma Göre Filtrele",
    allStatuses: "Tümü",
    pending: "Beklemede",
    accepted: "Kabul Edildi",
    rejected: "Reddedildi",
    loadingHistory: "Satın alma geçmişi yükleniyor...",
    historyError: "Satın alma geçmişi yüklenemedi. Lütfen tekrar deneyin.",
    noPurchases: "Satın alma bulunamadı.",
    noFilteredPurchases: "Seçilen durum için satın alma bulunamadı.",
    price: "Fiyat",
    purchaseDate: "Tarih",
    provider: "Sağlayıcı",
    slot: "Slot",
    expiryDate: "Bitiş",
    reason: "Sebep",
    previous: "Önceki",
    next: "Sonraki",
    pageOf: "Sayfa",
    of: "/",
  },
};

const MarketPage = ({
  i18n = {},
  language = "en",
  isAuthenticated = false,
}) => {
  const [marketData, setMarketData] = useState(null);
  const [userData, setUserData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedBonusType, setSelectedBonusType] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);

  const handleFilterChange = (bonusType) => {
    setSelectedBonusType(bonusType);
  };

  // Filter items based on selected bonus type
  const filteredItems =
    marketData?.filter((item) => {
      if (!selectedBonusType) return true;
      return item.category === selectedBonusType;
    }) || [];

  const handleItemClick = (item) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const handlePurchase = async (purchaseData) => {
    try {
      setError(null);

      const result = await purchaseMarketItem(purchaseData);

      if (result.success) {
        const marketResponse = await getMarketItems();
        setMarketData(marketResponse.data);
        setUserData(JSON.parse(sessionStorage.getItem("extendedUser")));
      } else {
        throw new Error(result.message || "Purchase failed");
      }
    } catch (error) {
      setError(error.message);
    }
  };

  useEffect(() => {
    const container = document.querySelector(".p-not-found")?.parentElement;
    if (!container) return;

    container.className = "";
    container.style.display = "none";
  }, []);

  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!isAuthenticated) {
          setError("Authentication required");
          return;
        }

        console.log("MarketPage: Starting market data fetch...");

        const marketResponse = await getMarketItems();

        setMarketData(marketResponse.data);
        setUserData(JSON.parse(sessionStorage.getItem("extendedUser")));

        console.log("MarketPage: Market data loaded successfully");
      } catch (err) {
        console.error("Failed to fetch market data:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMarketData();
  }, [isAuthenticated]);

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>{i18n.title}</h1>
          <p className={styles.subtitle}>{i18n.subtitle}</p>
        </div>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p className={styles.loadingText}>{i18n.loading}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>{i18n.title}</h1>
        </div>
        <div className={styles.errorContainer}>
          <p className={styles.errorText}>
            {error.includes("Authentication required")
              ? i18n.authError
              : i18n.error}
          </p>
        </div>
      </div>
    );
  }

  if (!marketData || !marketData.length) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>{i18n.title}</h1>
          <p className={styles.subtitle}>{i18n.subtitle}</p>
          {userData && (
            <div className={styles.userStats}>
              <div className={styles.pointsDisplay}>
                <div className={styles.pointsIcon}>
                  <IoDiamondOutline />
                </div>
                <span className={styles.pointsValue}>{userData.points}</span>
                <span className={styles.pointsLabel}>{i18n.points}</span>
              </div>
            </div>
          )}
        </div>

        {/* History Button Section */}
        <div className={styles.filtersSection}>
          <div className={styles.filterGroup}>
            {/* Empty space to maintain layout */}
          </div>
          <button
            className={styles.historyButton}
            onClick={() => setIsHistoryModalOpen(true)}
            title={i18n.purchaseHistory}
          >
            <IoTimeOutline />
            <span>{i18n.history}</span>
          </button>
        </div>

        <div className={styles.emptyContainer}>
          <p className={styles.emptyText}>{i18n.noItems}</p>
        </div>

        {/* Purchase History Modal */}
        <PurchaseHistoryModal
          isOpen={isHistoryModalOpen}
          onClose={() => setIsHistoryModalOpen(false)}
          language={language}
          i18n={i18n}
        />
      </div>
    );
  }

  // Show message if no items match the filter
  if (filteredItems.length === 0 && selectedBonusType) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>{i18n.title}</h1>
          <p className={styles.subtitle}>{i18n.subtitle}</p>
          <div className={styles.userStats}>
            <div className={styles.pointsDisplay}>
              <div className={styles.pointsIcon}>
                <IoDiamondOutline />
              </div>
              <span className={styles.pointsValue}>{userData.points}</span>
              <span className={styles.pointsLabel}>{i18n.points}</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className={styles.filtersSection}>
          <div className={styles.filterGroup}>
            <label className={styles.filterLabel}>{i18n.filterByType}:</label>
            <CustomSelect
              options={[
                { label: i18n.allTypes, value: "" },
                ...Object.values(MARKET_PRODUCT_CATEGORIES).map((c) => ({
                  label: i18n[c],
                  value: c,
                })),
              ]}
              value={selectedBonusType}
              onChange={handleFilterChange}
              placeholder={i18n.filterByType}
              className={styles.filterSelect}
            />
          </div>
          <button
            className={styles.historyButton}
            onClick={() => setIsHistoryModalOpen(true)}
            title={i18n.purchaseHistory}
          >
            <IoTimeOutline />
            <span>{i18n.history}</span>
          </button>
        </div>

        <div className={styles.emptyContainer}>
          <p className={styles.emptyText}>
            No items found for the selected filter.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1 className={styles.title}>{i18n.title}</h1>
        <p className={styles.subtitle}>{i18n.subtitle}</p>
        <div className={styles.userStats}>
          <div className={styles.pointsDisplay}>
            <div className={styles.pointsIcon}>
              <IoDiamondOutline />
            </div>
            <span className={styles.pointsValue}>{userData.points}</span>
            <span className={styles.pointsLabel}>{i18n.points}</span>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className={styles.filtersSection}>
        <div className={styles.filterGroup}>
          <label className={styles.filterLabel}>{i18n.filterByType}:</label>
          <CustomSelect
            options={[
              { label: i18n.allTypes, value: "" },
              ...Object.values(MARKET_PRODUCT_CATEGORIES).map((c) => ({
                label: i18n[c],
                value: c,
              })),
            ]}
            value={selectedBonusType}
            onChange={handleFilterChange}
            placeholder={i18n.filterByType}
            className={styles.filterSelect}
          />
        </div>
        <button
          className={styles.historyButton}
          onClick={() => setIsHistoryModalOpen(true)}
          title={i18n.purchaseHistory}
        >
          <IoTimeOutline />
          <span>{i18n.history}</span>
        </button>
      </div>

      {/* Market Items Grid */}
      <div className={styles.marketGrid}>
        {filteredItems.map((item) => (
          <MarketItemCard
            key={item.id}
            item={item}
            userPoints={userData.points}
            language={language}
            i18n={i18n}
            onClick={() => handleItemClick(item)}
          />
        ))}
      </div>

      {/* Purchase Modal */}
      {isModalOpen && selectedItem && (
        <MarketItemModal
          item={selectedItem}
          userPoints={userData.points}
          language={language}
          i18n={i18n}
          onPurchase={handlePurchase}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedItem(null);
          }}
        />
      )}

      {/* Purchase History Modal */}
      <PurchaseHistoryModal
        isOpen={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        language={language}
        i18n={i18n}
      />
    </div>
  );
};

export { MarketPage };
export default MarketPage;
