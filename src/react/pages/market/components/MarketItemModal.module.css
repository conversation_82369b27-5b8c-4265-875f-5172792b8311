/* Market Item Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  padding: 20px;
  backdrop-filter: blur(4px);
  font-family: 'Rajdhani-SemiBold', serif;
}

.modal {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  height: 90vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

/* Custom Scrollbar */
.modal::-webkit-scrollbar {
  width: 8px;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #20d4ff 0%, #0ed1d4 100%);
}

/* Firefox scrollbar */
.modal {
  scrollbar-width: thin;
  scrollbar-color: #18cffb rgba(255, 255, 255, 0.05);
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg,
      rgba(24, 207, 251, 0.05) 0%,
      rgba(10, 195, 198, 0.02) 100%);
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.closeButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Item Details */
.itemDetails {
  display: flex;
  gap: 24px;
  padding: 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.itemImage {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  border-radius: 16px;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholderImage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.2) 0%, rgba(10, 195, 198, 0.2) 100%);
}

.placeholderIcon {
  font-size: 2.5rem;
}

.itemInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.itemName {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.3;
}

.itemDescription {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.5;
}

.itemValue {
  display: flex;
  align-items: center;
  gap: 8px;
}

.valueLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
}

.valueText {
  font-size: 1.1rem;
  color: #18cffb;
  font-weight: 700;
}

/* Selection Section */
.selectionSection {
  padding: 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg,
      rgba(24, 207, 251, 0.05) 0%,
      rgba(10, 195, 198, 0.02) 100%);
}

.selectionTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
  margin: 0 0 20px 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.selectGroup {
  margin-bottom: 20px;
}

.selectGroup:last-child {
  margin-bottom: 0;
}

.selectLabel {
  display: block;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  margin-bottom: 8px;
}

.select {
  width: 100%;
}

.customSelect {
  width: 100%;
}



/* Purchase Summary */
.purchaseSummary {
  padding: 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg,
      rgba(24, 207, 251, 0.03) 0%,
      rgba(10, 195, 198, 0.01) 100%);
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.summaryRow:last-child {
  margin-bottom: 0;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 700;
}

.summaryLabel {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.summaryValue {
  font-size: 1rem;
  color: white;
  font-weight: 600;
}

.summaryValue.positive {
  color: #22c55e;
}

.summaryValue.negative {
  color: #ef4444;
}

/* Error Messages */
.errorMessage {
  margin: 16px 32px;
  padding: 16px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  color: #ef4444;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  gap: 16px;
  padding: 32px;
}

.cancelButton {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 14px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.cancelButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.purchaseButton {
  flex: 1;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.purchaseButton:hover {
  background: linear-gradient(135deg, #0ac3c6 0%, #18cffb 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 207, 251, 0.3);
}

.purchaseButton.disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.purchaseButton.disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 16px;
  }

  .modal {
    border-radius: 0;
    top: 0;
    position: absolute;
    height: calc(100vh - 60px);
    margin: 0;
  }

  .header {
    padding: 20px 24px;
  }

  .title {
    font-size: 1.3rem;
  }

  .itemDetails {
    flex-direction: column;
    padding: 24px;
    gap: 16px;
  }

  .itemImage {
    width: 100px;
    height: 100px;
    align-self: center;
  }

  .itemName {
    font-size: 1.2rem;
  }

  .selectionSection,
  .purchaseSummary {
    padding: 24px;
  }

  .actionButtons {
    padding: 24px;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .modalOverlay {
    padding: 12px;
  }

  .header {
    padding: 16px 20px;
  }

  .title {
    font-size: 1.2rem;
  }

  .itemDetails {
    padding: 20px;
  }

  .itemImage {
    width: 80px;
    height: 80px;
  }

  .itemName {
    font-size: 1.1rem;
  }

  .selectionSection,
  .purchaseSummary {
    padding: 20px;
  }

  .actionButtons {
    flex-direction: column;
    padding: 20px;
  }

  .cancelButton,
  .purchaseButton {
    padding: 12px 20px;
  }
}

/* Processing Overlay */
.processingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(6, 29, 43, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  z-index: 10;
  animation: fadeIn 0.3s ease-out;
}

.processingContent {
  text-align: center;
  animation: scaleIn 0.5s ease-out;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(24, 207, 251, 0.2);
  border-top: 4px solid #18cffb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.processingTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #18cffb;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 8px rgba(24, 207, 251, 0.3);
}

.processingMessage {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Success Animation Overlay */
.successOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(6, 29, 43, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  z-index: 10;
  animation: fadeIn 0.3s ease-out;
}

.successContent {
  text-align: center;
  animation: scaleIn 0.5s ease-out 0.2s both;
}

.successIcon {
  margin-bottom: 20px;
  animation: bounceIn 0.6s ease-out 0.4s both;
}

.successCircle {
  stroke-dasharray: 220;
  stroke-dashoffset: 220;
  animation: drawCircle 0.8s ease-out 0.6s forwards;
}

.successCheck {
  stroke-dasharray: 50;
  stroke-dashoffset: 50;
  animation: drawCheck 0.5s ease-out 1.2s forwards;
}

.successTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #22c55e;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
  animation: slideUp 0.4s ease-out 1.4s both;
}

.successMessage {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  animation: slideUp 0.4s ease-out 1.6s both;
}

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes drawCircle {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes drawCheck {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Game Gallery Styles */
.searchBox {
  margin-bottom: 16px;
}

.searchInput {
  box-sizing: border-box;
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(12px);
}

.searchInput:focus {
  outline: none;
  border-color: #18cffb;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(24, 207, 251, 0.1);
}

.searchInput::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.gamesGallery {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
}

.gamesGallery::-webkit-scrollbar {
  width: 6px;
}

.gamesGallery::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.gamesGallery::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 3px;
}

.providerGames {
  margin-bottom: 24px;
}

.providerGames:last-child {
  margin-bottom: 0;
}

.providerName {
  color: #18cffb;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(24, 207, 251, 0.2);
}

.gamesList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.gameCard {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.gameCard:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(24, 207, 251, 0.5);
  transform: translateY(-2px);
}

.gameCard.selected {
  background: rgba(24, 207, 251, 0.2);
  border-color: #18cffb;
  box-shadow: 0 0 20px rgba(24, 207, 251, 0.3);
}

.gameImage {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 8px;
}

.gameName {
  color: white;
  font-size: 0.85rem;
  font-weight: 500;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.gamesLoading,
.noGames {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  padding: 20px;
  font-style: italic;
}

.gamesLoading {
  color: #18cffb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gamesList {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }

  .gameImage {
    height: 60px;
  }

  .gameName {
    font-size: 0.8rem;
  }

  .gamesGallery {
    max-height: 300px;
    padding: 12px;
  }
}
