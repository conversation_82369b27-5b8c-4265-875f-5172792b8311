import styles from "./MarketItemCard.module.css";

export const i18n = {
  en: {
    shop: "Missions Shop",
    price: "Price",
    buy: "Buy",
    bought: "Bought",
    notEnoughDiamonds: "Not enough diamonds",
    diamonds: "Diamonds",
    quantity: "Quantity",
    description: "Description",
    viewMore: "View More",
  },
  tr: {
    shop: "Görev Mağazası",
    price: "Fiyat",
    buy: "Satın Al",
    bought: "Satın Alındı",
    notEnoughDiamonds: "Yeterli elmas yok",
    diamonds: "Elmas",
    quantity: "Miktar",
    description: "Açıklama",
    viewMore: "Daha Fazla Gör",
  },
};

const MarketItemCard = ({
  item,
  userPoints = 0,
  i18n = {},
  language = "en",
  onClick,
}) => {
  const canAfford = userPoints >= item.price;
  const isAvailable = item.available && item.amount > 0;
  const isOneTime = item.oneTime;
  const isLimitedOffer = item.startDate || item.endDate;

  const getBonusTypeLabel = (bonusType) => {
    switch (bonusType) {
      case "free_spins":
        return i18n.freeSpins || "Free Spins";
      case "cash":
        return i18n.cash || "Cash";
      case "vip":
        return i18n.vip || "VIP";
      case "cashback":
        return i18n.cashback || "Cashback";
      default:
        return bonusType;
    }
  };

  const getBonusTypeIcon = (bonusType) => {
    switch (bonusType) {
      case "free_spins":
        return "🎰";
      case "cash":
        return "💰";
      case "vip":
        return "👑";
      case "cashback":
        return "💸";
      default:
        return "🎁";
    }
  };

  const handleClick = () => {
    if (isAvailable && onClick) {
      onClick(item);
    }
  };

  return (
    <div
      key={item.id || index}
      className={`${styles.shopItem} ${!canAfford ? styles.unaffordable : ""}`}
    >
      <div className={styles.itemImage}>
        {item.image ? (
          <img
            src={item.image}
            alt={item.name}
            className={styles.image}
            loading="lazy"
            onError={(e) => {
              e.target.style.display = "none";
            }}
          />
        ) : (
          <div className={styles.placeholderImage}>
            <svg
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="3"
                y="3"
                width="18"
                height="18"
                rx="2"
                ry="2"
                stroke="currentColor"
                strokeWidth="2"
              />
              <circle
                cx="8.5"
                cy="8.5"
                r="1.5"
                stroke="currentColor"
                strokeWidth="2"
              />
              <polyline
                points="21,15 16,10 5,21"
                stroke="currentColor"
                strokeWidth="2"
              />
            </svg>
          </div>
        )}
      </div>

      <div className={styles.itemContent}>
        <h3 className={styles.itemName}>{item.name}</h3>

        {item.description && (
          <p className={styles.itemDescription}>{item.description}</p>
        )}

        {item.quantity && (
          <div className={styles.itemQuantity}>
            <span className={styles.quantityLabel}>
              {i18n.quantity || "Quantity"}:
            </span>
            <span className={styles.quantityValue}>{item.quantity}</span>
          </div>
        )}

        <div className={styles.actionSection}>
          <button
            className={`${styles.buyButton} ${
              !canAfford ? styles.disabled : ""
            }`}
            // onClick={() => handlePurchase(item.id, item.price)}
            disabled={!canAfford}
            title={
              !canAfford ? i18n.notEnoughDiamonds || "Not enough diamonds" : ""
            }
          >
            💎 {item.price.toLocaleString()}
          </button>
        </div>
      </div>
    </div>
  );
};

export { MarketItemCard };
export default MarketItemCard;
