/* Purchase History Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  padding: 20px;
  padding-top: 40px;
  padding-bottom: 120px;
  box-sizing: border-box;
  font-family: 'Rajdhani-SemiBold', serif;
  overflow-y: auto;
}

.modal {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: calc(100vh - 180px);
  overflow: hidden;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  margin: 20px 0;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1) 0%, rgba(10, 195, 198, 0.05) 100%);
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.closeButton {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

/* Filter Section */
.filterSection {
  padding: 20px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.filterLabel {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 12px;
  display: block;
}

.filterButtons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filterButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filterButton:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
}

.filterButton.active {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border-color: #18cffb;
  color: #ffffff;
}

/* Content */
.content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Custom Scrollbar */
.content::-webkit-scrollbar {
  width: 8px;
}

.content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #20d4ff 0%, #0ed1d4 100%);
}

/* Firefox scrollbar */
.content {
  scrollbar-width: thin;
  scrollbar-color: #18cffb rgba(255, 255, 255, 0.05);
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 32px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(24, 207, 251, 0.2);
  border-top: 3px solid #18cffb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loadingText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin: 0;
}

/* Error State */
.errorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 32px;
}

.errorText {
  color: #ff6b6b;
  font-size: 1rem;
  text-align: center;
  margin: 0;
  padding: 20px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 12px;
}

/* Empty State */
.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 32px;
}

.emptyText {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1rem;
  text-align: center;
  margin: 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

/* Purchase List */
.purchaseList {
  padding: 24px 32px;
}

.purchaseItem {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.purchaseItem:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(24, 207, 251, 0.3);
}

.purchaseHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.purchaseInfo {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.bonusIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.purchaseDetails {
  flex: 1;
}

.purchaseName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
}

.purchaseDescription {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
}

.purchaseStatus {
  flex-shrink: 0;
}

.statusBadge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusAccepted {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.statusPending {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.statusRejected {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.statusDefault {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

.purchaseMeta {}

.metaItem {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 8px 0;
}

.metaLabel {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.metaValue {
  font-size: 0.85rem;
  color: #ffffff;
  font-weight: 600;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 24px 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.pageButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pageButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
}

.pageButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modalOverlay {
    /* padding: 0 0 8vh 0; */
    padding: 0;
    display: block;
  }

  .modal {
    margin: 10px 0;
    max-height: 100%;
  }

  .header {
    padding: 20px 24px;
  }

  .title {
    font-size: 1.3rem;
  }

  .filterSection {
    padding: 16px 24px;
  }

  .purchaseList {
    padding: 20px 24px;
  }

  .purchaseItem {
    padding: 16px;
  }

  .purchaseHeader {
    flex-direction: column;
    gap: 12px;
  }

  .purchaseMeta {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .pagination {
    padding: 20px 24px;
    flex-direction: column;
    gap: 12px;
  }

  .filterButtons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modalOverlay {
    /* padding: 0 0 7vh 0; */
  }

  .modal {
    border-radius: 0;
    margin: 0;
    height: 100%;
    max-height: calc(100vh - 60px)
  }

  .header {
    padding: 16px 20px;
  }

  .title {
    font-size: 1.2rem;
  }

  .filterSection {
    padding: 12px 20px;
  }

  .purchaseList {
    padding: 16px 20px;
  }

  .purchaseItem {
    padding: 12px;
  }

  .pagination {
    padding: 16px 20px;
  }
}
