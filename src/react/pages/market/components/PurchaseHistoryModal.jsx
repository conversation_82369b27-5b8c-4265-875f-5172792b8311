import { useState, useEffect } from "react";
import { getPurchaseHistory } from "../../../api/market";
import styles from "./PurchaseHistoryModal.module.css";

const PurchaseHistoryModal = ({
  isOpen,
  onClose,
  language = "en",
  i18n = {}
}) => {
  const [purchases, setPurchases] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(null);
  const [statusFilter, setStatusFilter] = useState("");

  // ESC key handling
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // Fetch purchase history
  useEffect(() => {
    if (!isOpen) return;

    const fetchHistory = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await getPurchaseHistory({
          page: currentPage,
          limit: 10,
        });

        if (response.success) {
          const purchases = response.data || [];

          setPurchases(purchases);
          // Since we don't have pagination from API, create simple pagination
          setPagination({
            currentPage: currentPage,
            totalPages: 1,
            totalItems: purchases.length,
            itemsPerPage: 10,
            hasNextPage: false,
            hasPreviousPage: false
          });
        } else {
          throw new Error(response.message || "Failed to fetch purchase history");
        }
      } catch (err) {
        console.error("Error fetching purchase history:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchHistory();
  }, [isOpen, currentPage]);

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleStatusFilterChange = (status) => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'tr' ? 'tr-TR' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'accepted':
        return styles.statusAccepted;
      case 'pending':
        return styles.statusPending;
      case 'rejected':
        return styles.statusRejected;
      default:
        return styles.statusDefault;
    }
  };

  const getBonusTypeIcon = (bonusType) => {
    switch (bonusType) {
      case 'free_spins':
        return '🎰';
      case 'cash':
        return '💰';
      case 'vip':
        return '👑';
      case 'cashback':
        return '💸';
      default:
        return '🎁';
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay} onClick={handleBackdropClick}>
      <div className={styles.modal}>
        {/* Modal Header */}
        <div className={styles.header}>
          <h2 className={styles.title}>
            {i18n.purchaseHistory || 'Purchase History'}
          </h2>
          <button className={styles.closeButton} onClick={onClose}>
            ✕
          </button>
        </div>

        {/* Status Filter */}
        {/* <div className={styles.filterSection}>
          <label className={styles.filterLabel}>
            {i18n.filterByStatus || 'Filter by Status'}:
          </label>
          <div className={styles.filterButtons}>
            <button
              className={`${styles.filterButton} ${statusFilter === "" ? styles.active : ""}`}
              onClick={() => handleStatusFilterChange("")}
            >
              {i18n.allStatuses || 'All'}
            </button>
            <button
              className={`${styles.filterButton} ${statusFilter === "pending" ? styles.active : ""}`}
              onClick={() => handleStatusFilterChange("pending")}
            >
              {i18n.pending || 'Pending'}
            </button>
            <button
              className={`${styles.filterButton} ${statusFilter === "accepted" ? styles.active : ""}`}
              onClick={() => handleStatusFilterChange("accepted")}
            >
              {i18n.accepted || 'Accepted'}
            </button>
            <button
              className={`${styles.filterButton} ${statusFilter === "rejected" ? styles.active : ""}`}
              onClick={() => handleStatusFilterChange("rejected")}
            >
              {i18n.rejected || 'Rejected'}
            </button>
          </div>
        </div> */}

        {/* Content */}
        <div className={styles.content}>
          {isLoading ? (
            <div className={styles.loadingContainer}>
              <div className={styles.spinner}></div>
              <p className={styles.loadingText}>
                {i18n.loadingHistory || 'Loading purchase history...'}
              </p>
            </div>
          ) : error ? (
            <div className={styles.errorContainer}>
              <p className={styles.errorText}>
                {i18n.historyError || 'Failed to load purchase history. Please try again.'}
              </p>
            </div>
          ) : purchases.length === 0 ? (
            <div className={styles.emptyContainer}>
              <p className={styles.emptyText}>
                {i18n.noPurchases || 'No purchases found.'}
              </p>
            </div>
          ) : (
            <>
              {/* Purchase List */}
              <div className={styles.purchaseList}>
                {purchases.map((purchase) => (
                  <div key={purchase.id} className={styles.purchaseItem}>
                    <div className={styles.purchaseHeader}>
                      <div className={styles.purchaseInfo}>
                        <span className={styles.bonusIcon}>
                          {getBonusTypeIcon(purchase.product?.category)}
                        </span>
                        <div className={styles.purchaseDetails}>
                          <h4 className={styles.purchaseName}>
                            {purchase.product?.name_i18n?.[language] || purchase.product?.name || 'Unknown Product'}
                          </h4>
                          <p className={styles.purchaseDescription}>
                            {purchase.product?.description_i18n?.[language] || purchase.product?.description_i18n?.en || ''}
                          </p>
                        </div>
                      </div>
                      <div className={styles.purchaseStatus}>
                        <span className={`${styles.statusBadge} ${getStatusColor(purchase.status)}`}>
                          {i18n[purchase.status] || purchase.status}
                        </span>
                      </div>
                    </div>

                    <div className={styles.purchaseMeta}>
                      <div className={styles.metaItem}>
                        <span className={styles.metaLabel}>{i18n.price || 'Price'}:</span>
                        <span className={styles.metaValue}>💎 {purchase.product?.price?.toLocaleString() || 0}</span>
                      </div>
                      <div className={styles.metaItem}>
                        <span className={styles.metaLabel}>{i18n.purchaseDate || 'Date'}:</span>
                        <span className={styles.metaValue}>{formatDate(purchase.createdAt)}</span>
                      </div>
                      {purchase.currency && (
                        <div className={styles.metaItem}>
                          <span className={styles.metaLabel}>Currency:</span>
                          <span className={styles.metaValue}>{purchase.currency}</span>
                        </div>
                      )}
                      {purchase.providers && Object.keys(purchase.providers).length > 0 && (
                        <div className={styles.metaItem}>
                          <span className={styles.metaLabel}>{i18n.provider || 'Providers'}:</span>
                          <span className={styles.metaValue}>
                            {Object.keys(purchase.providers).join(', ')}
                          </span>
                        </div>
                      )}
                      {purchase.rejectReason && (
                        <div className={styles.metaItem}>
                          <span className={styles.metaLabel}>{i18n.reason || 'Reason'}:</span>
                          <span className={styles.metaValue}>{purchase.rejectReason}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className={styles.pagination}>
                  <button
                    className={styles.pageButton}
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={!pagination.hasPreviousPage}
                  >
                    {i18n.previous || 'Previous'}
                  </button>
                  
                  <span className={styles.pageInfo}>
                    {i18n.pageOf || 'Page'} {pagination.currentPage} {i18n.of || 'of'} {pagination.totalPages}
                  </span>
                  
                  <button
                    className={styles.pageButton}
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={!pagination.hasNextPage}
                  >
                    {i18n.next || 'Next'}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export { PurchaseHistoryModal };
export default PurchaseHistoryModal;
