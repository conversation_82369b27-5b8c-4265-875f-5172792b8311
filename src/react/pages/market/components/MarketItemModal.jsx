import { useState, useEffect } from "react";
import { MultiSelect } from "../../../components/MultiSelect/MultiSelect";
import styles from "./MarketItemModal.module.css";
import { MARKET_PRODUCT_CATEGORIES } from "../MarketPage";
import CustomSelect from "../../../components/CustomSelect/CustomSelect";
import { ENVIRONMENT_HOST } from "../../../config/environment";

export const i18n = {
  en: {
    confirmPurchase: "Confirm Purchase",
    selectProvider: "Select Providers and Slots",
    selectProviders: "Select Providers:",
    selectSlots: "Select Slots:",
    selectCurrency: "Select currency:",
    selectProvidersPlaceholder: "Select providers...",
    selectSlotsPlaceholder: "Select slots...",
    selectCurrencyPlaceholder: "Select currency...",
    searchGames: "Search games...",
    value: "Value:",
    price: "Price:",
    yourBalance: "Your Balance:",
    afterPurchase: "After Purchase:",
    points: "Points",
    cancel: "Cancel",
    processing: "Processing...",
    processingTitle: "Processing Purchase...",
    processingMessage: "Please wait while we process your order.",
    successTitle: "Purchase Successfully Requested!",
    successMessage:
      "Your item is being requested and will become available soon.",
    loadingGames: "Loading games...",
    noGamesFound: "No games found",
  },
  tr: {
    confirmPurchase: "Satın Almayı Onayla",
    selectProvider: "Sağlayıcı ve Slot Seçin",
    selectProviders: "Sağlayıcı Seçin:",
    selectSlots: "Slot Seçin:",
    selectCurrency: "Para birimi seçin:",
    selectProvidersPlaceholder: "Sağlayıcı seçin...",
    selectSlotsPlaceholder: "Slot seçin...",
    selectCurrencyPlaceholder: "Para birimi seçin...",
    searchGames: "Oyun ara...",
    value: "Değer:",
    price: "Fiyat:",
    yourBalance: "Bakiyeniz:",
    afterPurchase: "Satın Alma Sonrası:",
    points: "Puan",
    cancel: "İptal",
    processing: "İşleniyor...",
    processingTitle: "Satın Alma İşleniyor...",
    processingMessage: "Siparişinizi işlerken lütfen bekleyin.",
    successTitle: "Satın Alma Başarıyla Talep Edildi!",
    successMessage: "Ürününüz talep ediliyor ve yakında kullanılabilir olacak.",
    loadingGames: "Oyunlar yükleniyor...",
    noGamesFound: "Oyun bulunamadı",
  },
};

const MarketItemModal = ({
  item,
  userPoints = 0,
  language = "en",
  i18n = {},
  onPurchase,
  onClose,
}) => {
  const providers = item.providers || []
  const [selectedProviders, setSelectedProviders] = useState([]);
  const [selectedSlots, setSelectedSlots] = useState([]);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isInSuccessFlow, setIsInSuccessFlow] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [providerGames, setProviderGames] = useState({});
  const [loadingGames, setLoadingGames] = useState({});

  // Fetch games for a provider when needed
  const fetchProviderGames = async (providerId, searchName = "") => {
    if (loadingGames[providerId]) return;

    setLoadingGames(prev => ({ ...prev, [providerId]: true }));

    try {
      const url = new URL(`${ENVIRONMENT_HOST}/api/v1/ebetlab/public/games`);
      url.searchParams.set('merchantId', providerId.toString());
      url.searchParams.set('page', '1');
      url.searchParams.set('limit', '100');
      if (searchName) {
        url.searchParams.set('name', searchName);
      }

      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error('Failed to fetch games');
      }

      const data = await response.json();
      if (data.success) {
        setProviderGames(prev => ({
          ...prev,
          [providerId]: data.data.data || []
        }));
      }
    } catch (error) {
      console.error('Error fetching provider games:', error);
      setProviderGames(prev => ({
        ...prev,
        [providerId]: []
      }));
    } finally {
      setLoadingGames(prev => ({ ...prev, [providerId]: false }));
    }
  };

  const canAfford = userPoints >= item.price;
  const isAvailable = item.availableAmount > 0;
  const needsProviderSelection = providers.length > 0;
  const needsCurrencySelection =
    item.currencies && item.currencies.length !== 0;
  const hasValidCurrencySelection =
    needsCurrencySelection && selectedCurrency !== null;

  // Check if at least one provider OR one slot is selected for free spins
  const hasValidSelection =
    !needsProviderSelection ||
    selectedProviders.length > 0 ||
    selectedSlots.length > 0;

  // Safe close function that respects success flow
  const handleSafeClose = () => {
    if (!isInSuccessFlow) {
      onClose();
    }
  };

  // ESC key handling
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (
        event.key === "Escape" &&
        !isProcessing &&
        !showSuccess &&
        !isInSuccessFlow
      ) {
        handleSafeClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isProcessing, showSuccess, isInSuccessFlow]);

  // Handle search term changes - refetch games with search filter
  useEffect(() => {
    if (searchTerm && selectedProviders.length > 0) {
      selectedProviders.forEach(providerId => {
        const provider = providers.find(p => p.provider.id === providerId);
        if (provider && provider.games.length === 0) {
          // Debounce the search to avoid too many API calls
          const timeoutId = setTimeout(() => {
            fetchProviderGames(providerId, searchTerm);
          }, 300);
          return () => clearTimeout(timeoutId);
        }
      });
    }
  }, [searchTerm, selectedProviders]);

  // Prevent background scroll when modal is open
  useEffect(() => {
    const originalOverflow = document.body.style.overflow;
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = originalOverflow;
    };
  }, []);

  const handlePurchase = async () => {
    if (
      !canAfford ||
      !isAvailable ||
      !hasValidSelection ||
      !hasValidCurrencySelection
    )
      return;

    console.dir("🛒 Starting purchase...");
    setIsProcessing(true);

    // Create a minimum processing time to ensure user sees the processing state
    const minProcessingTime = new Promise((resolve) =>
      setTimeout(resolve, 1500)
    );

    try {
      const purchaseData = {
        productId: item.id,
        ...(needsCurrencySelection && {
          currency: selectedCurrency,
        }),
        ...(needsProviderSelection && {
          providers: selectedProviders.reduce((acc, providerId) => {
            const provider = providers.find(p => p.provider.id === providerId);
            if (provider) {
              // Get selected games for this provider
              const providerGamesData = provider.games.length > 0
                ? provider.games
                : providerGames[providerId] || [];

              const selectedGamesForProvider = providerGamesData.filter(game =>
                selectedSlots.includes(game.id)
              );

              acc[provider.provider.name] = selectedGamesForProvider.map(game => game.name);
            }
            return acc;
          }, {}),
        }),
      };

      console.dir("💳 Processing purchase...");

      // Wait for both the purchase to complete AND minimum processing time
      await Promise.all([onPurchase(purchaseData), minProcessingTime]);

      console.dir("✅ Purchase completed, showing success...");

      // Set success flow flag to prevent external closing
      setIsInSuccessFlow(true);

      // Immediately transition to success state
      setIsProcessing(false);
      setShowSuccess(true);

      console.dir("🎉 Success animation should be visible!");

      // Close modal after success animation
      setTimeout(() => {
        console.dir("🚪 Closing modal");
        setIsInSuccessFlow(false);
        onClose();
      }, 4000);
    } catch (error) {
      console.error("❌ Purchase failed:", error);
      setIsProcessing(false);
      // You could add error handling here with a nice error animation too
    }
  };

  const handleBackdropClick = (e) => {
    if (
      e.target === e.currentTarget &&
      !isProcessing &&
      !showSuccess &&
      !isInSuccessFlow
    ) {
      handleSafeClose();
    }
  };

  return (
    <div className={styles.modalOverlay} onClick={handleBackdropClick}>
      <div
        className={styles.modal}
        style={{ overflowY: isProcessing || showSuccess ? "clip" : "auto" }}
      >
        {/* Modal Header */}
        <div className={styles.header}>
          <h2 className={styles.title}>
            {i18n.confirmPurchase || "Confirm Purchase"}
          </h2>
          <button className={styles.closeButton} onClick={handleSafeClose}>
            ✕
          </button>
        </div>
        {/* Item Details */}
        <div className={styles.itemDetails}>
          <div className={styles.itemImage}>
            {item.photoUrl ? (
              <img
                src={item.photoUrl}
                alt={item.name_i18n[language]}
                className={styles.image}
              />
            ) : (
              <div className={styles.placeholderImage}>
                {/* Icon removed as requested */}
              </div>
            )}
          </div>

          <div className={styles.itemInfo}>
            <h3 className={styles.itemName}>{item.name_i18n[language]}</h3>
            <p className={styles.itemDescription}>
              {item.description_i18n[language]}
            </p>
            {/* <div className={styles.itemValue}>
              <span className={styles.valueLabel}>{i18n.value || 'Value:'}</span>
              <span className={styles.valueText}>{item.value}</span>
            </div> */}
          </div>
        </div>
        {/* Provider and Slot Selection for Free Spins */}
        {needsProviderSelection && providers.length > 0 && (
          <div className={styles.selectionSection}>
            <h4 className={styles.selectionTitle}>
              {i18n.selectProvider || "Select Providers and Slots"}
            </h4>

            {/* Provider Selection */}
            <div className={styles.selectGroup}>
              <label className={styles.selectLabel}>
                {i18n.selectProviders || "Select Providers:"}
              </label>
              <MultiSelect
                options={providers.map((p) => ({
                  value: p.provider.id,
                  label: p.provider.name,
                }))}
                value={selectedProviders}
                onChange={setSelectedProviders}
                placeholder={
                  i18n.selectProvidersPlaceholder || "Select providers..."
                }
                className={styles.customSelect}
              />
            </div>

            {/* Slot Gallery Selection */}
            {selectedProviders.length > 0 && (
              <div className={styles.selectGroup}>
                <label className={styles.selectLabel}>
                  {i18n.selectSlots || "Select Slots:"}
                </label>

                {/* Search box */}
                <div className={styles.searchBox}>
                  <input
                    type="text"
                    placeholder={i18n.searchGames || "Search games..."}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={styles.searchInput}
                  />
                </div>

                {/* Games Gallery */}
                <div className={styles.gamesGallery}>
                  {selectedProviders.map((providerId) => {
                    const provider = providers.find(p => p.provider.id === providerId);
                    if (!provider) return null;

                    // Use games from provider data if available, otherwise fetch from API
                    const games = provider.games.length > 0
                      ? provider.games
                      : providerGames[providerId] || [];

                    // Filter games by search term
                    const filteredGames = games.filter(game =>
                      game.name.toLowerCase().includes(searchTerm.toLowerCase())
                    );

                    // If no games in provider data and not loaded yet, fetch them
                    if (provider.games.length === 0 && !providerGames[providerId] && !loadingGames[providerId]) {
                      fetchProviderGames(providerId, searchTerm);
                    }

                    return (
                      <div key={providerId} className={styles.providerGames}>
                        <h5 className={styles.providerName}>{provider.provider.name}</h5>

                        {loadingGames[providerId] ? (
                          <div className={styles.gamesLoading}>{i18n.loadingGames || "Loading games..."}</div>
                        ) : filteredGames.length > 0 ? (
                          <div className={styles.gamesList}>
                            {filteredGames.map((game) => (
                              <div
                                key={game.id}
                                className={`${styles.gameCard} ${
                                  selectedSlots.includes(game.id) ? styles.selected : ''
                                }`}
                                onClick={() => {
                                  const newSelection = selectedSlots.includes(game.id)
                                    ? selectedSlots.filter(id => id !== game.id)
                                    : [...selectedSlots, game.id];
                                  setSelectedSlots(newSelection);
                                }}
                              >
                                <img
                                  src={game.image}
                                  alt={game.name}
                                  className={styles.gameImage}
                                />
                                <div className={styles.gameName}>{game.name}</div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className={styles.noGames}>{i18n.noGamesFound || "No games found"}</div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Currency Selection */}
        {item.currencies && item.currencies.length > 0 && (
          <div className={styles.selectionSection}>
            <div className={styles.selectGroup}>
              <label className={styles.selectLabel}>
                {i18n.selectCurrency || "Select currency:"}
              </label>
              <CustomSelect
                options={item.currencies.map((c) => ({ value: c, label: c }))}
                value={selectedCurrency}
                onChange={setSelectedCurrency}
                placeholder={
                  i18n.selectCurrencyPlaceholder || "Select currency..."
                }
                className={styles.customSelect}
              />
            </div>
          </div>
        )}

        {/* Purchase Summary */}
        <div className={styles.purchaseSummary}>
          <div className={styles.summaryRow}>
            <span className={styles.summaryLabel}>
              {i18n.price || "Price:"}
            </span>
            <span className={styles.summaryValue}>
              💎 {item.price.toLocaleString()} {i18n.points || "Points"}
            </span>
          </div>
          <div className={styles.summaryRow}>
            <span className={styles.summaryLabel}>
              {i18n.yourBalance || "Your Balance:"}
            </span>
            <span className={styles.summaryValue}>
              💎 {userPoints.toLocaleString()} {i18n.points || "Points"}
            </span>
          </div>
          <div className={styles.summaryRow}>
            <span className={styles.summaryLabel}>
              {i18n.afterPurchase || "After Purchase:"}
            </span>
            <span
              className={`${styles.summaryValue} ${
                canAfford ? styles.positive : styles.negative
              }`}
            >
              💎 {(userPoints - item.price).toLocaleString()}{" "}
              {i18n.points || "Points"}
            </span>
          </div>
        </div>
        {/* Action Buttons */}
        <div className={styles.actionButtons}>
          <button
            className={styles.cancelButton}
            onClick={handleSafeClose}
            disabled={isProcessing}
          >
            {i18n.cancel || "Cancel"}
          </button>
          <button
            className={`${styles.purchaseButton} ${
              !canAfford ||
              !isAvailable ||
              isProcessing ||
              !hasValidSelection ||
              !hasValidCurrencySelection
                ? styles.disabled
                : ""
            }`}
            onClick={handlePurchase}
            disabled={
              !canAfford ||
              !isAvailable ||
              isProcessing ||
              !hasValidSelection ||
              !hasValidCurrencySelection
            }
          >
            {isProcessing
              ? i18n.processing || "Processing..."
              : i18n.confirmPurchase || "Confirm Purchase"}
          </button>
        </div>
        {/* Processing Overlay */}
        {isProcessing && (
          <div className={styles.processingOverlay}>
            <div className={styles.processingContent}>
              <div className={styles.spinner}></div>
              <h3 className={styles.processingTitle}>
                {i18n.processingTitle || "Processing Purchase..."}
              </h3>
              <p className={styles.processingMessage}>
                {i18n.processingMessage ||
                  "Please wait while we process your order."}
              </p>
            </div>
          </div>
        )}
        {/* Success Animation Overlay */}
        {showSuccess && (
          <div className={styles.successOverlay}>
            <div className={styles.successContent}>
              <div className={styles.successIcon}>
                <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
                  <circle
                    cx="40"
                    cy="40"
                    r="35"
                    stroke="#22c55e"
                    strokeWidth="4"
                    fill="none"
                    className={styles.successCircle}
                  />
                  <path
                    d="M25 40L35 50L55 30"
                    stroke="#22c55e"
                    strokeWidth="4"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className={styles.successCheck}
                  />
                </svg>
              </div>
              <h3 className={styles.successTitle}>
                {i18n.successTitle || "Purchase Successfully Requested!"}
              </h3>
              <p className={styles.successMessage}>
                {i18n.successMessage ||
                  "Your item is being requested and will become available soon."}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export { MarketItemModal };
export default MarketItemModal;
