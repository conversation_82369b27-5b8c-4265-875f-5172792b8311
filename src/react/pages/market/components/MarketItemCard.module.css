
.shopItem {
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.12) 0%,
      rgba(124, 58, 237, 0.08) 50%,
      rgba(139, 92, 246, 0.06) 100%);
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 24px;
  padding: 24px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.1);
}

.shopItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.05) 0%,
      rgba(168, 85, 247, 0.03) 50%,
      transparent 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.shopItem::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.4) 0%,
      rgba(168, 85, 247, 0.3) 50%,
      rgba(124, 58, 237, 0.4) 100%);
  border-radius: 26px;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.shopItem:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(139, 92, 246, 0.6);
  box-shadow: 0 12px 40px rgba(139, 92, 246, 0.25),
    0 4px 16px rgba(139, 92, 246, 0.15);
}

.shopItem:hover::before {
  opacity: 1;
}

.shopItem:hover::after {
  opacity: 1;
}

.shopItem.unaffordable {
  opacity: 0.6;
  border-color: rgba(239, 68, 68, 0.3);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08), rgba(239, 68, 68, 0.04));
}

.shopItem.purchased {
  opacity: 0.7;
  border-color: rgba(34, 197, 94, 0.3);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.08), rgba(34, 197, 94, 0.04));
}

.itemImage {
  width: 100%;
  aspect-ratio: 1;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.15) 0%,
      rgba(168, 85, 247, 0.1) 50%,
      rgba(124, 58, 237, 0.08) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 2px solid rgba(139, 92, 246, 0.2);
  transition: all 0.3s ease;
}

.itemImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.shopItem:hover .itemImage {
  border-color: rgba(139, 92, 246, 0.4);
  transform: scale(1.05);
}

.shopItem:hover .itemImage::before {
  opacity: 1;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.placeholderImage {
  color: rgba(255, 255, 255, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.itemContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.itemName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  line-height: 1.3;
}

.itemDescription {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.itemQuantity {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantityLabel {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.quantityValue {
  font-size: 0.9rem;
  font-weight: 600;
  color: #8b5cf6;
}

.actionSection {
  margin-top: 16px;
}

.buyButton {
  width: 100%;
  padding: 16px 20px;
  background: linear-gradient(135deg,
      #8b5cf6 0%,
      #a855f7 50%,
      #7c3aed 100%);
  color: #ffffff;
  border: none;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1.1rem;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.buyButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.2) 50%,
      transparent 100%);
  transition: left 0.6s ease;
}

.buyButton:hover::before {
  left: 100%;
}

.buyButton:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 28px rgba(139, 92, 246, 0.5),
    0 4px 12px rgba(139, 92, 246, 0.3);
  background: linear-gradient(135deg,
      #9333ea 0%,
      #a855f7 50%,
      #8b5cf6 100%);
}

.buyButton:active {
  transform: translateY(-1px) scale(1.01);
}

.buyButton.disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: not-allowed;
  opacity: 0.6;
}

.buyButton.disabled:hover {
  transform: none;
  box-shadow: none;
}

@media (max-width: 480px) {
  .shopItem {
    padding: 14px;
  }

  .itemImage {
    margin-bottom: 10px;
  }

  .itemName {
    font-size: 1rem;
  }

  .itemDescription {
    font-size: 0.8rem;
  }

  .buyButton {
    padding: 10px 16px;
  }

  .buttonText {
    font-size: 0.8rem;
  }

  .buttonPrice {
    font-size: 0.75rem;
  }
}
