import { useEffect, useState, useRef } from "react";
import { CustomSelect } from "../../components/CustomSelect/CustomSelect.jsx";
import { getGameInfo, getGameUrl, getGameBets, getAllCasinoBets } from "../../api/games";
import styles from './OriginalsPage.module.css';

import adaCurrency from '../../assets/currency/ada.svg'
import avaxCurrency from '../../assets/currency/avax.svg'
import aznCurrency from '../../assets/currency/azn.svg'
import bnbCurrency from '../../assets/currency/bnb.svg'
import dogeCurrency from '../../assets/currency/doge.svg'
import ethCurrency from '../../assets/currency/eth.svg'
import linkCurrency from '../../assets/currency/link.svg'
import ltcCurrency from '../../assets/currency/ltc.svg'
import shibCurrency from '../../assets/currency/shib.svg'
import trxCurrency from '../../assets/currency/trx.svg'
import tryCurrency from '../../assets/currency/try.svg'
import usdcCurrency from '../../assets/currency/usdc.svg'
import usdtCurrency from '../../assets/currency/usdt.svg'
import xrpCurrency from '../../assets/currency/xrp.svg'

import rank1 from '../../assets/rank/1.svg'
import rank2 from '../../assets/rank/2.svg'
import rank3 from '../../assets/rank/3.svg'
import { SvgIcon } from "../../components/SvgIcon";

const CURRENCY_ICON = {
  ada: adaCurrency,
  avax: avaxCurrency,
  azn: aznCurrency,
  bnb: bnbCurrency,
  doge: dogeCurrency,
  eth: ethCurrency,
  link: linkCurrency,
  ltc: ltcCurrency,
  shib: shibCurrency,
  trx: trxCurrency,
  try: tryCurrency,
  usdc: usdcCurrency,
  usdt: usdtCurrency,
  xrp: xrpCurrency
}

const RANK_ICON = {
  1: rank1,
  2: rank2,
  3: rank3
}

// Internationalization
export const i18n = {
  en: {
    loading: 'Loading...',
    loading_game: 'Loading game data...',
    loading_game_url: 'Starting game...',
    error: 'Error',
    retry: 'Retry',
    invalid_game_url: 'Invalid game URL',
    game_render_error: 'Failed to render game',
    currency: 'Currency',
    mode: 'Mode',
    demo: 'Demo',
    real: 'Real Money',
    demo_play: 'Demo Play',
    real_play: 'Real Play',
    sign_up: 'Sign Up',
    play_now: 'Play Now',
    select_currency: 'Select Currency',
    select_mode: 'Select Mode',
    ready_to_play: 'Ready to Play?',
    choose_settings: 'Choose your settings and start playing!',
    game_info: 'Game Information',
    provider: 'Provider',
    rtp: 'RTP',
    big_wins: 'Big Wins',
    lucky_wins: 'Lucky Wins',
    challenges: 'Challenges',
    description: 'Description',
    username: 'Username',
    date: 'Date',
    bet: 'Bet',
    multiplier: 'Multiplier',
    profit: 'Profit',
    rank: 'Rank',
    loading_bets: 'Loading bets...',
    no_bets: 'No bets found',
    error_loading_bets: 'Error loading bets',
    all_bets: 'All Bets',
    high_rollers: 'High Rollers',
    high_winners: 'High Winners',
    game: 'Game'
  },
  tr: {
    loading: 'Yükleniyor...',
    loading_game: 'Oyun verileri yükleniyor...',
    loading_game_url: 'Oyun başlatılıyor...',
    error: 'Hata',
    retry: 'Tekrar Dene',
    invalid_game_url: 'Geçersiz oyun URL\'si',
    game_render_error: 'Oyun yüklenemedi',
    currency: 'Para Birimi',
    mode: 'Mod',
    demo: 'Demo',
    real: 'Gerçek Para',
    demo_play: 'Demo Oyna',
    real_play: 'Gerçek Para Oyna',
    sign_up: 'Kayıt Ol',
    play_now: 'Şimdi Oyna',
    select_currency: 'Para Birimi Seç',
    select_mode: 'Mod Seç',
    ready_to_play: 'Oynamaya Hazır mısın?',
    choose_settings: 'Ayarlarını seç ve oynamaya başla!',
    game_info: 'Oyun Bilgileri',
    provider: 'Sağlayıcı',
    rtp: 'RTP',
    big_wins: 'Büyük Kazançlar',
    lucky_wins: 'Şanslı Kazançlar',
    challenges: 'Meydan Okumalar',
    description: 'Açıklama',
    username: 'Kullanıcı Adı',
    date: 'Tarih',
    bet: 'Bahis',
    multiplier: 'Çarpan',
    profit: 'Kazanç',
    rank: 'Sıra',
    loading_bets: 'Bahisler yükleniyor...',
    no_bets: 'Bahis bulunamadı',
    error_loading_bets: 'Bahisler yüklenirken hata',
    all_bets: 'Tüm Bahisler',
    high_rollers: 'Yüksek Bahisçiler',
    high_winners: 'Büyük Kazananlar',
    game: 'Oyun'
  }
};

const OriginalsPage = ({ language = 'en', i18n: translations = {}, root, isAuthenticated }) => {
  const [gameData, setGameData] = useState(null);
  const [gameConfig, setGameConfig] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingGame, setIsLoadingGame] = useState(false);
  const [error, setError] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState('BRL');
  const [gameType, setGameType] = useState('demo');
  const [availableCurrencies, setAvailableCurrencies] = useState([]);
  const [gameStarted, setGameStarted] = useState(false);
  const [activeTab, setActiveTab] = useState('big-wins');
  const [betsData, setBetsData] = useState({});
  const [betsLoading, setBetsLoading] = useState(false);
  const [betsError, setBetsError] = useState(null);
  const [gameBetsCollapsed, setGameBetsCollapsed] = useState(false);
  const [allBetsActiveTab, setAllBetsActiveTab] = useState('all-bets');
  const [allBetsData, setAllBetsData] = useState({});
  const [allBetsLoading, setAllBetsLoading] = useState(false);
  const [allBetsError, setAllBetsError] = useState(null);
  const gameContainerRef = useRef(null);

  // @todo improve appearance
  useEffect(() => {
    const container = document.querySelector('.p-not-found')?.parentElement
    if (!container) return

    container.className = ''
    container.style.display = 'none'
  }, [])

  // Extract slug from URL
  const getSlugFromUrl = () => {
    const pathSegments = window.location.pathname.split('/').filter(Boolean);
    return 'ebetlab-' + pathSegments[pathSegments.length - 1] + '-originals';
  };

  // Load game data
  useEffect(() => {
    const loadGameData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const slug = getSlugFromUrl();
        if (!slug) {
          throw new Error(translations.invalid_game_url || 'Invalid game URL');
        }

        // First, get game information
        const gameInfo = await getGameInfo(slug, language);
        setGameData(gameInfo);

        // Parse available currencies
        if (gameInfo.game.currencies) {
          const currencies = JSON.parse(gameInfo.game.currencies);
          setAvailableCurrencies(currencies);

          // Set default currency if current selection is not available
          if (!currencies.includes(selectedCurrency)) {
            setSelectedCurrency(currencies[0] || 'BRL');
          }
        }

      } catch (err) {
        console.error('Error loading game data:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    loadGameData();
  }, [language]);

  // Function to start the game with specific mode
  const startGame = async (mode) => {
    if (!gameData?.game?.id) return;

    try {
      setIsLoadingGame(true);
      setError(null);
      setGameType(mode);

      const gameUrlData = await getGameUrl(
        gameData.game.id,
        selectedCurrency,
        mode,
        language
      );

      setGameConfig(gameUrlData);
      setGameStarted(true);
    } catch (err) {
      console.error('Error loading game URL:', err);
      setError(err.message);
    } finally {
      setIsLoadingGame(false);
    }
  };

  // Function to handle sign up button click
  const handleSignUp = () => {
    const signUpButton = document.querySelector('.header__signup');
    if (signUpButton) {
      signUpButton.click();
    }
  };

  // Function to load bets data for a specific tab
  const loadBetsData = async (type) => {
    if (!gameData?.game?.id) return;

    try {
      setBetsLoading(true);
      setBetsError(null);

      const bets = await getGameBets(gameData.game.id, type, 3); // Only get top 3
      setBetsData(prev => ({ ...prev, [type]: bets.slice(0, 3) }));
    } catch (err) {
      console.error('Error loading bets:', err);
      setBetsError(err.message);
    } finally {
      setBetsLoading(false);
    }
  };

  // Load bets data when game data is available
  useEffect(() => {
    if (gameData?.game?.id) {
      loadBetsData('big-wins');
    }
  }, [gameData]);

  // Load bets data when active tab changes
  useEffect(() => {
    if (gameData?.game?.id && !betsData[activeTab]) {
      loadBetsData(activeTab);
    }
  }, [activeTab, gameData, betsData]);

  // Function to load all casino bets data for a specific tab
  const loadAllBetsData = async (type) => {
    try {
      setAllBetsLoading(true);
      setAllBetsError(null);

      const bets = await getAllCasinoBets(type, 10);
      setAllBetsData(prev => ({ ...prev, [type]: bets }));
    } catch (err) {
      console.error('Error loading all bets:', err);
      setAllBetsError(err.message);
    } finally {
      setAllBetsLoading(false);
    }
  };

  // Load all bets data on component mount
  useEffect(() => {
    loadAllBetsData('all-bets');
  }, []);

  // Load all bets data when active tab changes
  useEffect(() => {
    if (!allBetsData[allBetsActiveTab]) {
      loadAllBetsData(allBetsActiveTab);
    }
  }, [allBetsActiveTab, allBetsData]);

  // Helper function to format date
  const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString(language === 'tr' ? 'tr-TR' : 'en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper function to format currency
  const formatCurrency = (amount, currency) => {
    return `${parseFloat(amount).toFixed(2)} ${currency}`;
  };

  // Render game when config is available and game is started
  useEffect(() => {
    if (!gameConfig?.url || !gameContainerRef.current || !gameStarted) return;

    try {
      // Parse the game configuration
      const config = JSON.parse(gameConfig.url);
      const { gameHtml, gameScript } = config;

      // Clear previous content (including pre-game UI)
      // gameContainerRef.current.innerHTML = '';

      // Insert game HTML
      // if (gameHtml) {
      //   gameContainerRef.current.innerHTML = gameHtml;
      // }

      // Execute game script
      if (gameScript) {
        // modify and inject script
        const modifiedScript = gameScript.replaceAll(
          'document.getElementById(\'egamings_container\')',
          `document.getElementById(\'${root.id}\').shadowRoot.getElementById('egamings_container')`
        )

        console.dir(modifiedScript)

        const script = document.createElement('script');
        script.textContent = modifiedScript;

        document.head.appendChild(script);

        // Clean up script after execution
        return () => {
          if (script.parentNode) {
            script.parentNode.removeChild(script);
          }
        };
      }
    } catch (err) {
      console.error('Error rendering game:', err);
      setError(translations.game_render_error || 'Failed to render game');
    }
  }, [gameConfig, gameStarted, translations]);

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loadingContainer}>
          <h2 className={styles.loadingTitle}>{translations.loading || 'Loading...'}</h2>
          <p className={styles.loadingText}>{translations.loading_game || 'Loading game data...'}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.errorContainer}>
          <h2 className={styles.errorTitle}>{translations.error || 'Error'}</h2>
          <p className={styles.errorText}>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className={styles.retryButton}
          >
            {translations.retry || 'Retry'}
          </button>
        </div>
      </div>
    );
  }
  console.dir([allBetsActiveTab, allBetsData[allBetsActiveTab]])
  return (
    <div className={styles.container}>
      {gameData && (
            <div className={styles.gameHeader}>
              <h1 className={styles.gameTitle}>{gameData.game.name}</h1>
            </div>
      )}

      {/* Game Container */}
      <div
        ref={gameContainerRef}
        className={styles.gameContainer}
      >
        {/* Game Preloader - positioned absolute behind */}
        <div className={styles.gamePreloader}></div>

        {/* Egamings Container - positioned on top */}
        <div id="egamings_container" className={styles.egamingsContainer}></div>

        {!gameStarted && gameData && (
          <div className={styles.preGameContainer}>
            <div className={styles.preGameBackground}>
              <img
                src={gameData.game.image}
                alt={gameData.game.name}
                className={styles.gameImage}
              />
              <div className={styles.preGameOverlay} />
            </div>

            <div className={styles.preGameContent}>
              <div className={styles.preGameCard}>
                <h2 className={styles.preGameTitle}>
                  {translations.ready_to_play || 'Ready to Play?'}
                </h2>
                <p className={styles.preGameSubtitle}>
                  {translations.choose_settings || 'Choose your settings and start playing!'}
                </p>

                <div className={styles.preGameControls}>
                  {/* Currency Selection */}
                  <div className={styles.preGameControlGroup}>
                    <label className={styles.preGameLabel}>
                      {translations.select_currency || 'Select Currency'}
                    </label>
                    <CustomSelect
                      options={availableCurrencies.map(currency => ({
                        value: currency,
                        label: currency
                      }))}
                      value={selectedCurrency}
                      onChange={setSelectedCurrency}
                      placeholder={translations.select_currency || 'Select Currency'}
                      disabled={isLoadingGame}
                      className={styles.preGameSelect}
                    />
                  </div>

                  {/* Game Mode Buttons */}
                  <div className={styles.preGameControlGroup}>
                    <label className={styles.preGameLabel}>
                      {gameData.game.has_demo === 1
                        ? (translations.select_mode || 'Select Mode')
                        : (translations.ready_to_play || 'Ready to Play?')
                      }
                    </label>
                    <div className={styles.gameModeButtons}>
                      {isAuthenticated ? (
                        <button
                          className={`${styles.gameModeButton} ${styles.realPlay}`}
                          onClick={() => startGame('real')}
                          disabled={isLoadingGame}
                        >
                          {isLoadingGame && gameType === 'real'
                            ? (translations.loading_game_url || 'Starting game...')
                            : (translations.real_play || 'Real Play')
                          }
                        </button>
                      ) : (
                        <button
                          className={`${styles.gameModeButton} ${styles.signUp}`}
                          onClick={handleSignUp}
                          disabled={isLoadingGame}
                        >
                          {translations.sign_up || 'Sign Up'}
                        </button>
                      )}
                      {gameData.game.has_demo === 1 && (
                        <button
                          className={`${styles.gameModeButton} ${styles.demoPlay}`}
                          onClick={() => startGame('demo')}
                          disabled={isLoadingGame}
                        >
                          {isLoadingGame && gameType === 'demo'
                            ? (translations.loading_game_url || 'Starting game...')
                            : (translations.demo_play || 'Demo Play')
                          }
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Game Bets Section */}
      {gameData && (
        <div className={styles.gameBetsSection}>
          <div className={styles.tableWrap}>
            <div
              className={styles.gameBetsHeader}
              onClick={() => setGameBetsCollapsed(!gameBetsCollapsed)}
            >
              <h2 className={styles.gameBetsTitle}>{gameData.game.name}</h2>
              <span className={`${styles.collapseIcon} ${gameBetsCollapsed ? styles.collapsed : ''}`}>
                ▼
              </span>
            </div>

            <div className={`${styles.gameBetsContent} ${gameBetsCollapsed ? styles.collapsed : styles.expanded}`}>
              <div className={styles.navTabs}>
                <button
                  className={`${styles.tabsNavBtn} ${activeTab === 'big-wins' ? styles.active : ''}`}
                  onClick={() => setActiveTab('big-wins')}
                >
                  {translations.big_wins || 'Big Wins'}
                </button>
                <button
                  className={`${styles.tabsNavBtn} ${activeTab === 'lucky-wins' ? styles.active : ''}`}
                  onClick={() => setActiveTab('lucky-wins')}
                >
                  {translations.lucky_wins || 'Lucky Wins'}
                </button>
                <button
                  className={`${styles.tabsNavBtn} ${activeTab === 'challenges' ? styles.active : ''}`}
                  onClick={() => setActiveTab('challenges')}
                >
                  {translations.challenges || 'Challenges'}
                </button>
                <button
                  className={`${styles.tabsNavBtn} ${activeTab === 'description' ? styles.active : ''}`}
                  onClick={() => setActiveTab('description')}
                >
                  {translations.description || 'Description'}
                </button>
              </div>

              {activeTab === 'description' ? (
                <div className={styles.descriptionContent}>
                  <div className={styles.descriptionHeader}>
                    <img
                      src={gameData.game.game_provider.image}
                      alt={gameData.game.game_provider.name}
                      className={styles.descriptionProviderImage}
                    />
                    <div className={styles.descriptionGameInfo}>
                      <h3 className={styles.descriptionGameName}>{gameData.game.name}</h3>
                      <p className={styles.descriptionProvider}>
                        {translations.provider || 'Provider'}: {gameData.game.game_provider.name}
                      </p>
                    </div>
                  </div>

                  <div className={styles.descriptionStats}>
                    <div className={styles.descriptionStat}>
                      <span className={styles.descriptionStatLabel}>
                        {translations.rtp || 'RTP'}
                      </span>
                      <span className={styles.descriptionStatValue}>
                        {gameData.game.payout}%
                      </span>
                    </div>
                  </div>
                </div>
              ) : betsLoading ? (
                <div className={styles.betsLoading}>
                  {translations.loading_bets || 'Loading bets...'}
                </div>
              ) : betsError ? (
                <div className={styles.betsError}>
                  {translations.error_loading_bets || 'Error loading bets'}
                </div>
              ) : betsData[activeTab]?.length > 0 ? (
                <table className={styles.xtable}>
                  <thead>
                    <tr>
                      <th>{translations.rank || 'Rank'}</th>
                      <th>{translations.username || 'Username'}</th>
                      <th>{translations.date || 'Date'}</th>
                      <th>{translations.bet || 'Bet'}</th>
                      <th>{translations.multiplier || 'Multiplier'}</th>
                      <th>{translations.profit || 'Profit'}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {betsData[activeTab].map((bet, index) => {
                      return (
                        <tr key={bet.id}>
                          <td>
                            {RANK_ICON[index + 1] && (
                              <SvgIcon icon={RANK_ICON[index + 1]} className={styles.rankIcon} />
                            )}
                          </td>
                          <td>
                            <span className={styles.xtableText}>{bet.customer.masked_username}</span>
                          </td>
                          <td>
                            <span className={styles.xtableText}>{formatDate(bet.timestamp)}</span>
                          </td>
                          <td>
                            <div className={styles.xtableCoin}>
                              <div className={styles.instrumentIconWrapper}>
                                {CURRENCY_ICON[bet.wallet_currency.toLowerCase()] && (
                                  <SvgIcon icon={CURRENCY_ICON[bet.wallet_currency.toLowerCase()]} />
                                )}
                              </div>
                              <span>{parseFloat(bet.amount).toFixed(2)}</span>
                            </div>
                          </td>
                          <td>
                            <span className={styles.xtableText}>{bet.multiplier}x</span>
                          </td>
                          <td>
                            <div className={styles.xtableCoin}>
                              <div className={styles.instrumentIconWrapper}>
                                {CURRENCY_ICON[bet.wallet_currency.toLowerCase()] && (
                                  <SvgIcon icon={CURRENCY_ICON[bet.wallet_currency.toLowerCase()]} />
                                )}
                              </div>
                              <span>{parseFloat(bet.income).toFixed(2)}</span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              ) : (
                <div className={styles.betsEmpty}>
                  {translations.no_bets || 'No bets found'}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* All Casino Bets Section */}
      <div className={styles.allBetsSection}>
        <div className={styles.tableWrap}>
          <div className={styles.navTabs}>
            <button
              className={`${styles.tabsNavBtn} ${allBetsActiveTab === 'all-bets' ? styles.active : ''}`}
              onClick={() => setAllBetsActiveTab('all-bets')}
            >
              {translations.all_bets || 'All Bets'}
            </button>
            <button
              className={`${styles.tabsNavBtn} ${allBetsActiveTab === 'high-rollers' ? styles.active : ''}`}
              onClick={() => setAllBetsActiveTab('high-rollers')}
            >
              {translations.high_rollers || 'High Rollers'}
            </button>
            <button
              className={`${styles.tabsNavBtn} ${allBetsActiveTab === 'high-winners' ? styles.active : ''}`}
              onClick={() => setAllBetsActiveTab('high-winners')}
            >
              {translations.high_winners || 'High Winners'}
            </button>
          </div>

          {allBetsLoading ? (
            <div className={styles.betsLoading}>
              {translations.loading_bets || 'Loading bets...'}.
            </div>
          ) : allBetsError ? (
            <div className={styles.betsError}>
              {translations.error_loading_bets || 'Error loading bets'}
            </div>
          ) : allBetsData[allBetsActiveTab]?.length > 0 ? (
            <table className={styles.xtable}>
              <thead>
                <tr>
                  <th>{translations.game || 'Game'}</th>
                  <th>{translations.username || 'Username'}</th>
                  <th>{translations.date || 'Date'}</th>
                  <th>{translations.bet || 'Bet'}</th>
                  <th>{translations.multiplier || 'Multiplier'}</th>
                  <th>{translations.profit || 'Profit'}</th>
                </tr>
              </thead>
              <tbody>
                {allBetsData[allBetsActiveTab].map((bet) => {
                  const walletAmount = parseFloat(bet.amount);
                  const multiplier = parseFloat(bet.multiplier);
                  const profit = walletAmount * multiplier - walletAmount;
                  const isProfitPositive = multiplier >= 1;

                  return (
                    <tr key={bet.id}>
                      <td>
                        <div className={styles.gameCell}>
                          {allBetsActiveTab !== 'all-bets' && (
                            <img
                              src={bet.game.game_website?.image || ''}
                              alt={bet.game.name}
                              className={styles.gameImage}
                            />
                          )}
                          <span className={`${styles.xtableText} ${styles.gameNameHighlight}`}>
                            {bet.game_name || bet.game.game_website?.name || bet.game.name}
                          </span>
                        </div>
                      </td>
                      <td>
                        <span className={styles.xtableText}>{bet.customer.masked_username}</span>
                      </td>
                      <td>
                        <span className={styles.xtableText}>{formatDate(bet.timestamp)}</span>
                      </td>
                      <td>
                        <div className={styles.xtableCoin}>
                          <div className={styles.instrumentIconWrapper}>
                            {CURRENCY_ICON[bet.wallet_currency.toLowerCase()] && (
                              <SvgIcon icon={CURRENCY_ICON[bet.wallet_currency.toLowerCase()]} />
                            )}
                          </div>
                          <span>{parseFloat(bet.amount).toFixed(2)}</span>
                        </div>
                      </td>
                      <td>
                        <span className={styles.xtableText}>{bet.multiplier}x</span>
                      </td>
                      <td>
                        <div className={`${styles.xtableCoin} ${isProfitPositive ? styles.profitPositive : styles.profitNegative}`}>
                          <div className={styles.instrumentIconWrapper}>
                            {CURRENCY_ICON[bet.wallet_currency.toLowerCase()] && (
                              <SvgIcon icon={CURRENCY_ICON[bet.wallet_currency.toLowerCase()]} />
                            )}
                          </div>
                          <span>{profit.toFixed(2)}</span>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          ) : (
            <div className={styles.betsEmpty}>
              {translations.no_bets || 'No bets found'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OriginalsPage;
