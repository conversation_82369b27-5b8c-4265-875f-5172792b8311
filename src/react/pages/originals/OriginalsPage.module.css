/* OriginalsPage Component Styles - CSS Module Version */

.container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  color: white;
  background: #151a22;
  min-height: 100vh;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loadingTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #18cffb;
  margin: 0 0 12px 0;
  text-shadow: 0 0 8px rgba(24, 207, 251, 0.4);
}

.loadingText {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.errorTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ff6b6b;
  margin: 0 0 12px 0;
}

.errorText {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 20px 0;
}

.retryButton {
  background: linear-gradient(135deg, #feca57 0%, #ff6b6b 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 100px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.retryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(254, 202, 87, 0.3);
}

.gameHeader {
  margin-bottom: 12px;
}

.gameTitle {
  font-size: 2rem;
  margin: 0;
  font-weight: 700;
  color: #ffffff;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(24, 207, 251, 0.3);
}

.gameControls {
  display: flex;
  gap: 24px;
  align-items: center;
  flex-wrap: wrap;
}

.controlGroup {
  display: flex;
  align-items: center;
  gap: 10px;
}

.controlLabel {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.select {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid rgba(24, 207, 251, 0.3);
  border-radius: 6px;
  color: white;
  font-size: 14px;
  padding: 8px 12px;
  min-width: 80px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select:hover {
  border-color: rgba(24, 207, 251, 0.5);
  box-shadow: 0 0 8px rgba(24, 207, 251, 0.2);
}

.select:focus {
  outline: none;
  border-color: #18cffb;
  box-shadow: 0 0 12px rgba(24, 207, 251, 0.4);
}

.select option {
  background: #0a2438;
  color: white;
  padding: 8px;
}

.gameContainer {
  width: 100%;
  aspect-ratio: 919 / 516;
  border: 1px solid #323b45;
  border-radius: 16px;
  overflow: hidden;
  /* box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); */
  position: relative;
}

.egamingsContainer {
  width: 100%;
  height: 100%;
}

.gameContainer iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Game Preloader Styles */
.gamePreloader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.gamePreloader::before {
  content: '';
  width: 60px;
  height: 60px;
  border: 4px solid rgba(24, 207, 251, 0.2);
  border-top: 4px solid #18cffb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.egamingsContainer {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* Pre-game UI Styles */
.preGameContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.preGameBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.gameImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preGameOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(6, 29, 43, 0.9) 0%,
      rgba(7, 30, 45, 0.8) 30%,
      rgba(8, 32, 48, 0.8) 60%,
      rgba(9, 34, 53, 0.9) 80%,
      rgba(10, 36, 56, 0.9) 100%);
  backdrop-filter: blur(4px);
}

.preGameContent {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 40px;
  box-sizing: border-box;
}

.preGameCard {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1), rgba(10, 195, 198, 0.05));
  border: 1px solid rgba(24, 207, 251, 0.3);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  backdrop-filter: blur(20px);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.4);
  max-width: 400px;
  width: 100%;
  animation: cardFadeIn 0.6s ease-out;
  margin: 0 20px;
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.preGameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(24, 207, 251, 0.3);
}

.preGameSubtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.preGameControls {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 0;
}

.gameModeButtons {
  display: flex;
  gap: 12px;
  width: 100%;
  justify-content: center;
}

.gameModeButtons:has(> :only-child) {
  justify-content: center;
}

.gameModeButtons> :only-child {
  max-width: 200px;
}

.gameModeButton {
  flex: 1;
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid rgba(24, 207, 251, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.gameModeButton:hover:not(:disabled) {
  border-color: rgba(24, 207, 251, 0.5);
  box-shadow: 0 0 12px rgba(24, 207, 251, 0.2);
  transform: translateY(-1px);
}

.gameModeButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.gameModeButton.selected {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border-color: #18cffb;
  color: #0a2438;
  box-shadow: 0 0 16px rgba(24, 207, 251, 0.4);
}

.gameModeButton.realPlay {
  background: linear-gradient(135deg, rgba(46, 213, 115, 0.15) 0%, rgba(0, 184, 148, 0.15) 100%);
  border-color: rgba(46, 213, 115, 0.4);
  color: #2ed573;
}

.gameModeButton.realPlay:hover:not(:disabled) {
  border-color: rgba(46, 213, 115, 0.6);
  box-shadow: 0 0 12px rgba(46, 213, 115, 0.3);
  background: linear-gradient(135deg, rgba(46, 213, 115, 0.2) 0%, rgba(0, 184, 148, 0.2) 100%);
}

.gameModeButton.demoPlay {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15) 0%, rgba(238, 82, 83, 0.15) 100%);
  border-color: rgba(255, 107, 107, 0.4);
  color: #ff6b6b;
}

.gameModeButton.demoPlay:hover:not(:disabled) {
  border-color: rgba(255, 107, 107, 0.6);
  box-shadow: 0 0 12px rgba(255, 107, 107, 0.3);
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2) 0%, rgba(238, 82, 83, 0.2) 100%);
}

.gameModeButton.signUp {
  background: linear-gradient(135deg, #feca57 0%, #ff6b6b 100%);
  border-color: #feca57;
  color: white;
}

.gameModeButton.signUp:hover:not(:disabled) {
  border-color: #ff6b6b;
  box-shadow: 0 0 12px rgba(254, 202, 87, 0.3);
}

.preGameControlGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: left;
}

.preGameLabel {
  font-size: 14px;
  font-weight: 600;
  color: #18cffb;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.preGameSelect {
  width: 100%;
}

.playButton {
  background: linear-gradient(135deg, #feca57 0%, #ff6b6b 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 700;
  font-size: 16px;
  padding: 16px 32px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  min-width: 160px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(254, 202, 87, 0.3);
  position: relative;
  overflow: hidden;
}

.playButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(254, 202, 87, 0.4);
}

.playButton:active:not(:disabled) {
  transform: translateY(0);
}

.playButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.playButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.playButton:hover:not(:disabled)::before {
  left: 100%;
}

/* Enhanced Table Styling - Based on table_styling.js */
.tableWrap {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border-radius: 16px;
  padding: 24px;
  margin: 20px 0;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(24, 207, 251, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  isolation: isolate;
}

/* Particle background effect */
.tableWrap::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 50%, rgba(24, 207, 251, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(10, 195, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(24, 207, 251, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Tab navigation styling */
.navTabs {
  background: rgba(24, 207, 251, 0.05);
  border-radius: 12px;
  padding: 4px;
  margin: 0 0 20px 0;
  border: 1px solid rgba(24, 207, 251, 0.2);
  position: relative;
  z-index: 2;
  display: flex;
  gap: 4px;
}

.tabsNavBtn {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  flex: 1;
  text-align: center;
}

.tabsNavBtn.active {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  color: #061d2b;
  box-shadow: 0 4px 12px rgba(24, 207, 251, 0.3);
}

.tabsNavBtn:hover:not(.active) {
  background: rgba(24, 207, 251, 0.1);
  color: #ffffff;
}

/* Enhanced table styling */
.xtable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: transparent;
  position: relative;
  z-index: 2;
}

.xtable thead th {
  background: #1b384d;
  color: #ffffff;
  font-weight: 700;
  padding: 12px 12px;
  text-align: left;
  border: none;
  font-size: 14px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  position: relative;
}

.xtable thead th:first-child {
  border-top-left-radius: 12px;
}

.xtable thead th:last-child {
  border-top-right-radius: 12px;
}

.xtable tbody tr {
  background: #0a1720;
  border: none;
}

.xtable tbody tr:nth-child(even) {
  background: rgba(24, 207, 251, 0.03);
}

.xtable tbody tr:last-child td:first-child {
  border-bottom-left-radius: 12px;
}

.xtable tbody tr:last-child td:last-child {
  border-bottom-right-radius: 12px;
}

.xtable td {
  padding: 10px 12px;
  border: none;
  color: #ffffff;
  font-size: 14px;
  vertical-align: middle;
}

/* Enhanced text styling */
.xtableText {
  color: #ffffff;
  font-weight: 500;
}

.xtableText.textWhite {
  color: #ffffff;
}

.xtableText a {
  color: #18cffb;
  text-decoration: none;
  transition: color 0.3s ease;
}

.xtableText a:hover {
  color: #0ac3c6;
}

/* Coin/currency styling */
.xtableCoin {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.instrumentIconWrapper {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.instrumentIconWrapper svg {
  width: 16px;
  height: 16px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Hidden user styling */
.xtableText span svg {
  filter: hue-rotate(170deg) brightness(1.2);
}

/* Multiplier styling */
.textRight .xtableText {
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

/* Profit/Loss color coding */
.xtableCoin.textSecondary {
  color: #ff6b6b;
}

.xtableCoin.profitPositive {
  color: #2ed573;
}

.xtableCoin.profitNegative {
  color: #ff6b6b;
}

.xtableCoin span[id^="win-amount-"] {
  font-weight: 700;
}

/* Responsive design for tables */
@media (max-width: 768px) {
  .tableWrap {
    padding: 16px;
    margin: 16px 0;
    border-radius: 12px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .xtable {
    min-width: 600px;
  }

  .xtable thead th,
  .xtable td {
    padding: 8px 8px;
    font-size: 12px;
  }

  .tabsNavBtn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .navTabs {
    flex-wrap: wrap;
    gap: 2px;
  }
}

@media (max-width: 480px) {
  .tableWrap {
    padding: 12px;
  }

  .xtable {
    min-width: 500px;
  }

  .xtable thead th,
  .xtable td {
    padding: 6px 6px;
    font-size: 11px;
  }

  .xtableCoin {
    gap: 4px;
  }

  .instrumentIconWrapper {
    width: 16px;
    height: 16px;
  }

  .instrumentIconWrapper svg {
    width: 12px;
    height: 12px;
  }

  .tabsNavBtn {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* Game Bets Section Styles */
.gameBetsSection {
  margin-top: 24px;
}

.gameBetsHeader {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
  margin: 0;
}

.gameBetsTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.collapseIcon {
  color: #18cffb;
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.collapseIcon.collapsed {
  transform: rotate(-90deg);
}

.gameBetsContent {
  overflow: hidden;
  transition: max-height 0.3s ease;
  position: relative;
  z-index: 2;
  padding: 0;
  margin: 0;
}

.gameBetsContent.collapsed {
  max-height: 0;
  padding: 0;
}

.gameBetsContent.expanded {
  max-height: 1000px;
}

.allBetsSection {
  margin-top: 24px;
}

/* Rank icon styling */
.rankIcon {
  width: 24px;
  height: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Game cell styling for all bets table */
.gameCell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.gameImage {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

/* Highlighted game name */
.gameNameHighlight {
  color: #18cffb;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(24, 207, 251, 0.3);
}

/* Loading, error, and empty states */
.betsLoading,
.betsError,
.betsEmpty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.betsLoading {
  color: rgba(255, 255, 255, 0.6);
}

.betsError {
  color: #ff6b6b;
}

.betsEmpty {
  color: rgba(255, 255, 255, 0.6);
}

/* Description content styles */
.descriptionContent {
  padding: 24px;
  position: relative;
  z-index: 2;
}

.descriptionHeader {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.descriptionProviderImage {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
}

.descriptionGameInfo {
  flex: 1;
}

.descriptionGameName {
  font-size: 1.3rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.descriptionProvider {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.descriptionStats {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.descriptionStat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.descriptionStatLabel {
  font-size: 0.85rem;
  color: #18cffb;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.descriptionStatValue {
  font-size: 1.1rem;
  color: #ffffff;
  font-weight: 700;
}

/* Mobile responsive styles for game bets section */
@media (max-width: 768px) {
  .gameBetsSection {
    margin-top: 20px;
  }

  .allBetsSection {
    margin-top: 20px;
  }

  .gameBetsHeader {
    padding: 0;
  }

  .gameBetsContent {
    padding: 0;
  }

  .gameBetsContent.collapsed {
    padding: 0;
  }

  .gameBetsTitle {
    font-size: 1.1rem;
  }

  .rankIcon {
    width: 20px;
    height: 20px;
  }

  .gameImage {
    width: 36px;
    height: 36px;
  }

  .gameCell {
    gap: 8px;
  }

  .descriptionContent {
    padding: 20px;
  }

  .descriptionHeader {
    gap: 12px;
  }

  .descriptionProviderImage {
    width: 40px;
    height: 40px;
  }

  .descriptionGameName {
    font-size: 1.1rem;
  }

  .descriptionStats {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .gameBetsSection {
    margin-top: 16px;
  }

  .allBetsSection {
    margin-top: 16px;
  }

  .gameBetsHeader {
    padding: 0;
  }

  .gameBetsContent {
    padding: 0;
  }

  .gameBetsContent.collapsed {
    padding: 0;
  }

  .gameBetsTitle {
    font-size: 1rem;
  }

  .rankIcon {
    width: 18px;
    height: 18px;
  }

  .gameImage {
    width: 32px;
    height: 32px;
  }

  .gameCell {
    gap: 6px;
  }

  .descriptionContent {
    padding: 16px;
  }

  .descriptionHeader {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .descriptionProviderImage {
    width: 36px;
    height: 36px;
  }

  .descriptionGameName {
    font-size: 1rem;
  }

  .descriptionProvider {
    font-size: 0.85rem;
  }

  .descriptionStats {
    gap: 16px;
    justify-content: center;
  }

  .descriptionStat {
    text-align: center;
  }
}
