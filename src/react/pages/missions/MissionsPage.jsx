import { useState, useEffect } from "react";
import { HeaderSlider } from "./components/HeaderSlider";
import { MissionsStats } from "./components/MissionsStats";
import { DailyMissions } from "./components/DailyMissions";
import { WeeklyMissions } from "./components/WeeklyMissions";
import { MonthlyMissions } from "./components/MonthlyMissions";
import { FinalMissionsCarousel } from "./components/FinalMissionsCarousel";
import { MissionsShop } from "./components/MissionsShop";
import { getMissionsData, participateInMission, completeMission, claimFinalMissionReward } from "../../api/missions";
import { getMarketItems, purchaseMarketItem } from "../../api/market";
import styles from "./MissionsPage.module.css";

export const i18n = {
  en: {
    title: "Missions",
    subtitle: "Complete daily missions to earn diamond rewards",
    loading: "Loading missions...",
    error: "Failed to load missions. Please try again later.",
    authError: "Please log in to view missions.",
    noMissions: "No missions available at the moment.",
    dailyMissions: "Daily Missions",
    weeklyMissions: "Weekly Missions",
    monthlyMissions: "Monthly Missions",
    noDailyMissions: "No daily missions yet...",
    noWeeklyMissions: "No weekly missions yet...",
    noMonthlyMissions: "No monthly missions yet...",
    // Modal translations
    reward: "Reward",
    status: "Status",
    objectives: "Objectives",
    participate: "Participate",
    participating: "Participating...",
    inProgress: "In Progress",
    notParticipating: "Not Participating",
    completed: "Completed",
    incomplete: "Incomplete",
    close: "Close",
    clickToViewDetails: "Click to view details and participate",
    // Final mission claims translations
    claiming: "Claiming...",
    finalMissionClaimSuccess: "🎉 Final {type} mission reward claimed! You earned {amount} diamonds!",
    finalMissionAlreadyClaimed: "You have already claimed the {type} final mission reward for this period.",
    finalMissionNotCompleted: "You need to complete all {type} missions before claiming the final reward.",
    finalMissionNoActive: "No active {type} missions found.",
    finalMissionClaimFailed: "Failed to claim final reward: {error}",
    noMissionsAvailable: "No missions available",
  },
  tr: {
    title: "Görevler",
    subtitle: "Elmas ödülleri kazanmak için günlük görevleri tamamlayın",
    loading: "Görevler yükleniyor...",
    error: "Görevler yüklenemedi. Lütfen daha sonra tekrar deneyin.",
    authError: "Görevleri görüntülemek için lütfen giriş yapın.",
    noMissions: "Şu anda mevcut görev bulunmamaktadır.",
    dailyMissions: "Günlük Görevler",
    weeklyMissions: "Haftalık Görevler",
    monthlyMissions: "Aylık Görevler",
    noDailyMissions: "Henüz günlük görev yok...",
    noWeeklyMissions: "Henüz haftalık görev yok...",
    noMonthlyMissions: "Henüz aylık görev yok...",
    // Modal translations
    reward: "Ödül",
    status: "Durum",
    objectives: "Hedefler",
    participate: "Katıl",
    participating: "Katılıyor...",
    inProgress: "Devam Ediyor",
    notParticipating: "Katılmıyor",
    completed: "Tamamlandı",
    incomplete: "Tamamlanmadı",
    close: "Kapat",
    clickToViewDetails: "Detayları görmek ve katılmak için tıklayın",
    // Final mission claims translations
    claiming: "Alınıyor...",
    finalMissionClaimSuccess: "🎉 {type} final görev ödülü alındı! {amount} elmas kazandınız!",
    finalMissionAlreadyClaimed: "Bu dönem için {type} final görev ödülünü zaten aldınız.",
    finalMissionNotCompleted: "Final ödülü almadan önce tüm {type} görevlerini tamamlamanız gerekiyor.",
    finalMissionNoActive: "Aktif {type} görevi bulunamadı.",
    finalMissionClaimFailed: "Final ödül alınamadı: {error}",
    noMissionsAvailable: "Görev mevcut değil",
  },
};

const MissionsPage = ({
  i18n = {},
  language = "en",
  isAuthenticated = false,
}) => {
  const [missionsData, setMissionsData] = useState(null);
  const [shopItems, setShopItems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isClaimingFinalReward, setIsClaimingFinalReward] = useState(false);

  const handleParticipate = async (missionId) => {
    try {
      console.log('Participating in mission:', missionId);

      // Call the participation API
      const result = await participateInMission({ missionId });

      if (result.success) {
        console.log('Successfully participated in mission:', result);

        // Refresh missions data to get updated participation status
        const [missions, market] = await Promise.all([
          getMissionsData({ language }),
          getMarketItems(),
        ]);

        setMissionsData(missions);
        setShopItems(market.data || []);

        console.log('Missions data refreshed after participation');
      } else {
        throw new Error(result.message || 'Participation failed');
      }
    } catch (error) {
      console.error('Failed to participate in mission:', error);
      alert(`Failed to participate: ${error.message}`);
    }
  };

  const handleShopPurchase = async (purchaseData) => {
    try {
      console.log('Attempting to purchase shop item:', purchaseData);

      const result = await purchaseMarketItem(purchaseData);

      if (result.success) {
        console.log('Successfully purchased shop item:', result);

        // Refresh both missions and market data
        const [missions, market] = await Promise.all([
          getMissionsData({ language }),
          getMarketItems(),
        ]);

        setMissionsData(missions);
        setShopItems(market.data || []);

        console.log('Data refreshed after shop purchase');
      } else {
        throw new Error(result.message || 'Purchase failed');
      }
    } catch (error) {
      console.error('Failed to purchase shop item:', error);
      throw error; // Re-throw to let the modal handle the error
    }
  };

  const handleClaimReward = async (missionId) => {
    try {
      console.log('Claiming reward for mission:', missionId);

      // Call the mission completion API
      const result = await completeMission({ missionId });

      if (result.success) {
        console.log('Successfully claimed mission reward:', result);

        // Refresh missions data to get updated completion status
        const [missions, market] = await Promise.all([
          getMissionsData({ language }),
          getMarketItems(),
        ]);

        setMissionsData(missions);
        setShopItems(market.data || []);

        console.log('Missions data refreshed after claim');
      } else {
        throw new Error(result.message || 'Claim failed');
      }
    } catch (error) {
      console.error('Failed to claim mission reward:', error);
      alert(`Failed to claim reward: ${error.message}`);
    }
  };

  const handleClaimFinalReward = async (claimType) => {
    if (isClaimingFinalReward) {
      console.log('Final claim already in progress, ignoring duplicate request');
      return;
    }

    // Client-side validation
    if (!claimType || !['daily', 'weekly', 'monthly'].includes(claimType)) {
      alert('Invalid claim type. Please try again.');
      return;
    }

    // Check if final missions data is available
    if (!missionsData || !missionsData.finalMissions) {
      alert('Mission data not available. Please refresh the page and try again.');
      return;
    }

    // Check if the specific final mission can be claimed
    const finalMission = missionsData.finalMissions[claimType];
    if (!finalMission) {
      alert(`${claimType} final mission data not found.`);
      return;
    }

    if (finalMission.claimed) {
      const translations = i18n[language] || i18n.en;
      alert(translations.finalMissionAlreadyClaimed.replace('{type}', claimType));
      return;
    }

    if (!finalMission.canClaim) {
      const translations = i18n[language] || i18n.en;
      alert(translations.finalMissionNotCompleted.replace('{type}', claimType));
      return;
    }

    try {
      setIsClaimingFinalReward(true);
      console.log('Claiming final mission reward:', claimType);

      // Call the final mission claim API
      const result = await claimFinalMissionReward({ claimType });

      if (result.success) {
        console.log('Successfully claimed final mission reward:', result);

        // Analytics logging for successful claims
        console.log('Final Mission Claims Analytics: Success', {
          claimType,
          rewardAmount: result.data?.grantedReward || 0,
          userId: result.data?.userId,
          timestamp: new Date().toISOString(),
          language
        });

        // Show success message with reward amount using i18n
        const rewardAmount = result.data?.grantedReward || 0;
        const successMessage = (i18n[language]?.finalMissionClaimSuccess || i18n.en.finalMissionClaimSuccess)
          .replace('{type}', claimType)
          .replace('{amount}', rewardAmount.toLocaleString());
        alert(successMessage);

        // Refresh missions data to get updated claim status and balance
        console.log('Refreshing missions data after final claim...');
        const [missions, market] = await Promise.all([
          getMissionsData({ language }),
          getMarketItems(),
        ]);

        if (missions) {
          setMissionsData(missions);
          console.log('Final Mission Claims: Updated missions data after claim:', {
            finalMissions: missions.finalMissions,
            stats: missions.stats
          });
        }

        if (market && market.data) {
          setShopItems(market.data);
        }

        console.log('Missions data refreshed successfully after final claim');
      } else {
        throw new Error(result.message || 'Final claim failed');
      }
    } catch (error) {
      console.error('Failed to claim final mission reward:', error);

      // Analytics logging for failed claims
      console.log('Final Mission Claims Analytics: Error', {
        claimType,
        error: error.message,
        timestamp: new Date().toISOString(),
        language
      });

      // Handle specific error messages for better user experience using i18n
      let errorMessage = error.message;
      const translations = i18n[language] || i18n.en;

      if (error.message.includes('already claimed')) {
        errorMessage = translations.finalMissionAlreadyClaimed.replace('{type}', claimType);
      } else if (error.message.includes('not completed all')) {
        errorMessage = translations.finalMissionNotCompleted.replace('{type}', claimType);
      } else if (error.message.includes('No active')) {
        errorMessage = translations.finalMissionNoActive.replace('{type}', claimType);
      } else {
        errorMessage = translations.finalMissionClaimFailed.replace('{error}', error.message);
      }

      alert(errorMessage);
    } finally {
      setIsClaimingFinalReward(false);
    }
  };

  // @todo improve appearance
  useEffect(() => {
    const container = document.querySelector(".p-not-found")?.parentElement;
    if (!container) return;

    container.className = "";
    container.style.display = "none";
  }, []);

  useEffect(() => {
    const fetchMissionsData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!isAuthenticated) {
          setError("Authentication required");
          return;
        }

        console.log('MissionsPage: Starting missions data fetch with extended user creation...');

        const [missions, market] = await Promise.all([
          getMissionsData({ language }),
          getMarketItems(),
        ]);

        setMissionsData(missions);
        setShopItems(market.data || []);

        console.log('MissionsPage: Missions data loaded successfully');
      } catch (err) {
        console.error("Failed to fetch missions data:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMissionsData();
  }, [language, isAuthenticated]);

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>{i18n.title || "Missions"}</h1>
        </div>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p className={styles.loadingText}>
            {i18n.loading || "Loading missions..."}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>{i18n.title || "Missions"}</h1>
        </div>
        <div className={styles.errorContainer}>
          <p className={styles.errorText}>
            {error.includes("Authentication required")
              ? i18n.authError || "Please log in to view missions."
              : i18n.error ||
                "Failed to load missions. Please try again later."}
          </p>
        </div>
      </div>
    );
  }

  if (!missionsData) {
    // Create a default slide for empty state
    const defaultSlides = [
      {
        id: 'empty_slide',
        title: i18n.title || "Missions",
        subtitle: i18n.noMissions || "No missions available at the moment.",
        backgroundColor: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
        textColor: '#ffffff'
      }
    ];

    return (
      <div className={styles.container}>
        <HeaderSlider
          slides={defaultSlides}
          language={language}
        />
        <div className={styles.emptyContainer}>
          <p className={styles.emptyText}>
            {i18n.noMissions || "No missions available at the moment."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Header Slider */}
      <HeaderSlider
        slides={[{
          id: 'missions_slide_1',
          backgroundImage: `https://d3r2lacstledxo.cloudfront.net/img/missions/slider_1_${language}.png`,
        }, {
          id: 'missions_slide_2',
          backgroundImage: `https://d3r2lacstledxo.cloudfront.net/img/missions/slider_2_${language}.png`,
        }, {
          id: 'missions_slide_3',
          backgroundImage: `https://d3r2lacstledxo.cloudfront.net/img/missions/slider_3_${language}.png`,
        }]}
        mobileSlides={[{
          id: 'missions_slide_1',
          backgroundImage: `https://d3r2lacstledxo.cloudfront.net/img/missions/slider_1_${language}_m.png`,
        }, {
          id: 'missions_slide_2',
          backgroundImage: `https://d3r2lacstledxo.cloudfront.net/img/missions/slider_2_${language}_m.png`,
        }, {
          id: 'missions_slide_3',
          backgroundImage: `https://d3r2lacstledxo.cloudfront.net/img/missions/slider_3_${language}_m.png`,
        }]}
        language={language}
      />

      <div className={styles.content}>
        {/* Missions Statistics */}
        <MissionsStats
          data={missionsData.stats}
          i18n={i18n}
          language={language}
        />

        {/* Daily Missions */}
        <DailyMissions
          missions={missionsData.dailyMissions}
          i18n={i18n}
          language={language}
          onParticipate={handleParticipate}
          onClaimReward={handleClaimReward}
        />

        {/* Weekly Missions */}
        <WeeklyMissions
          missions={missionsData.weeklyMissions}
          i18n={i18n}
          language={language}
          onParticipate={handleParticipate}
          onClaimReward={handleClaimReward}
        />

        {/* Monthly Missions */}
        <MonthlyMissions
          missions={missionsData.monthlyMissions}
          i18n={i18n}
          language={language}
          onParticipate={handleParticipate}
          onClaimReward={handleClaimReward}
        />

        {/* Final Missions */}
        <FinalMissionsCarousel
          finalMissions={missionsData.finalMissions}
          dailyMissions={missionsData.dailyMissions}
          weeklyMissions={missionsData.weeklyMissions}
          monthlyMissions={missionsData.monthlyMissions}
          i18n={i18n}
          language={language}
          onClaimFinalReward={handleClaimFinalReward}
          isClaimingReward={isClaimingFinalReward}
        />

        {/* Missions Shop */}
        <MissionsShop
          items={shopItems}
          userPoints={missionsData.stats.diamonds}
          i18n={i18n}
          language={language}
          onPurchase={handleShopPurchase}
          onViewMore={() => {
            window.location.href = `/${language}/market`;
          }}
        />
      </div>
    </div>
  );
};

export { MissionsPage };
export default MissionsPage;
