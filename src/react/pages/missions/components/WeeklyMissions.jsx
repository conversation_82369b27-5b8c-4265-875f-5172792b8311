import { MissionsCarousel } from "./MissionsCarousel";

export const i18n = {
  en: {
    weeklyMissions: "Weekly Missions",
    progress: "Progress",
    inProgress: "In Progress",
    reward: "Reward",
    completed: "Completed",
    claim: "Claim",
    claimed: "Claimed",
    diamonds: "Diamonds",
  },
  tr: {
    weeklyMissions: "<PERSON>ftalı<PERSON> Görevler",
    progress: "İlerleme",
    inProgress: "Devam Ediyor",
    reward: "Ödül",
    completed: "Tamamlandı",
    claim: "Al",
    claimed: "Alındı",
    diamonds: "Elmas",
  },
};

const WeeklyMissions = ({ missions = [], i18n = {}, language = "en", onParticipate, onClaimReward }) => {
  const handleClaimReward = (missionId) => {
    if (onClaimReward) {
      onClaimReward(missionId);
    } else {
      console.log("Claiming reward for weekly mission:", missionId);
    }
  };

  const handleParticipate = async (missionId) => {
    if (onParticipate) {
      await onParticipate(missionId);
    } else {
      console.log("Participating in weekly mission:", missionId);
    }
  };

  return (
    <MissionsCarousel
      missions={missions}
      i18n={i18n}
      language={language}
      type="weekly"
      onClaimReward={handleClaimReward}
      onParticipate={handleParticipate}
    />
  );
};

export { WeeklyMissions };
export default WeeklyMissions;
