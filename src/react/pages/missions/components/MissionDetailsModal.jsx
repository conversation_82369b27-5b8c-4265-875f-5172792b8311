import { useState, useEffect } from 'react';
import styles from './MissionDetailsModal.module.css';
import { formatCountdown, formatCountdownString } from '../../../utils/timeUtils.js';

const MissionDetailsModal = ({
  mission,
  isOpen,
  onClose,
  onParticipate,
  i18n = {},
  language = "en"
}) => {
  const [countdown, setCountdown] = useState(0);
  const [isParticipating, setIsParticipating] = useState(false);

  // Initialize and update countdown for inactive missions
  useEffect(() => {
    if (mission?.isInactive && mission?.nextAllowedParticipationSeconds > 0) {
      setCountdown(mission.nextAllowedParticipationSeconds);
    }
  }, [mission]);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setCountdown(prev => Math.max(0, prev - 1));
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [countdown]);

  if (!isOpen || !mission) return null;

  const handleParticipate = async () => {
    if (mission.participating) return;

    setIsParticipating(true);
    try {
      await onParticipate(mission.id);
      // Close modal after successful participation
      onClose();
    } catch (error) {
      console.error('Failed to participate in mission:', error);
      // Keep modal open on error so user can try again
    } finally {
      setIsParticipating(false);
    }
  };

  const formatObjectiveDescription = (objective) => {
    const operatorText = {
      'ge': language === 'tr' ? 'en az' : 'at least',
      'gt': language === 'tr' ? 'daha fazla' : 'more than',
      'le': language === 'tr' ? 'en fazla' : 'at most',
      'lt': language === 'tr' ? 'daha az' : 'less than',
      'eq': language === 'tr' ? 'tam olarak' : 'exactly'
    };

    const typeText = {
      'deposit': language === 'tr' ? 'Para Yatırma' : 'Deposit',
      'slot': language === 'tr' ? 'Slot Oyunu' : 'Slot Game',
      'bet': language === 'tr' ? 'Bahis' : 'Bet',
      'win': language === 'tr' ? 'Kazanç' : 'Win'
    };

    const subtypeText = {
      'amount': language === 'tr' ? 'miktar' : 'amount',
      'spins': language === 'tr' ? 'çevirme' : 'spins',
      'count': language === 'tr' ? 'sayı' : 'count'
    };

    const operator = operatorText[objective.operator] || objective.operator;
    const type = typeText[objective.objectiveType] || objective.objectiveType;
    const subtype = subtypeText[objective.subtype] || objective.subtype;

    return `${type} ${subtype}: ${operator} ${objective.targetValue}`;
  };

  const getProgressForObjective = (objective) => {
    if (!mission.userAssignments) return 0;
    
    const assignment = mission.userAssignments.find(
      assignment => assignment.missionObjectiveId === objective.id
    );
    
    return assignment ? parseInt(assignment.progress || '0') : 0;
  };

  const isObjectiveCompleted = (objective) => {
    if (!mission.userAssignments) return false;

    const assignment = mission.userAssignments.find(
      assignment => assignment.missionObjectiveId === objective.id
    );

    if (!assignment) return false;

    // Use the isCompleted field if available, otherwise fallback to calculation
    if (assignment.hasOwnProperty('isCompleted')) {
      return assignment.isCompleted;
    }

    // Fallback to manual calculation for backward compatibility
    const progress = getProgressForObjective(objective);
    const target = parseInt(objective.targetValue || '1');

    switch (objective.operator) {
      case 'ge': return progress >= target;
      case 'gt': return progress > target;
      case 'le': return progress <= target;
      case 'lt': return progress < target;
      case 'eq': return progress === target;
      default: return progress >= target;
    }
  };

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2 className={styles.missionTitle}>{mission.title}</h2>
          <button className={styles.closeButton} onClick={onClose}>
            ×
          </button>
        </div>

        <div className={styles.modalBody}>
          <div className={styles.missionInfo}>
            <p className={styles.missionDescription}>{mission.description}</p>
            
            <div className={styles.rewardSection}>
              <span className={styles.rewardLabel}>
                {i18n.reward || 'Reward'}:
              </span>
              <span className={styles.rewardValue}>
                💎 {mission.reward.toLocaleString()}
              </span>
            </div>

            <div className={styles.statusSection}>
              <span className={styles.statusLabel}>
                {i18n.status || 'Status'}:
              </span>
              <span className={`${styles.statusValue} ${
                mission.isInactive ? styles.inactive :
                mission.participating ? styles.participating : styles.notParticipating
              }`}>
                {mission.isInactive
                  ? (language === 'tr' ? 'Tamamlandı - Bekleme Süresi' : 'Completed - Cooldown')
                  : mission.participating
                    ? (i18n.inProgress || 'In Progress')
                    : (i18n.notParticipating || 'Not Participating')
                }
              </span>
            </div>

            {/* Show countdown for inactive missions */}
            {mission.isInactive && (
              <div className={styles.cooldownSection}>
                <span className={styles.cooldownLabel}>
                  {language === 'tr' ? 'Tekrar mevcut:' : 'Available again in:'}
                </span>
                <span className={styles.cooldownTime}>
                  {countdown > 0 ? (
                    formatCountdownString(formatCountdown(countdown), language)
                  ) : (
                    language === 'tr' ? 'Şimdi mevcut!' : 'Available now!'
                  )}
                </span>
              </div>
            )}
          </div>

          <div className={styles.objectivesSection}>
            <h3 className={styles.objectivesTitle}>
              {i18n.objectives || 'Objectives'}
            </h3>
            
            <div className={styles.objectivesList}>
              {mission.objectives.map((objective, index) => {
                const progress = getProgressForObjective(objective);
                const target = parseInt(objective.targetValue || '1');
                const completed = isObjectiveCompleted(objective);
                const progressPercentage = Math.min((progress / target) * 100, 100);

                return (
                  <div key={objective.id || index} className={styles.objectiveItem}>
                    <div className={styles.objectiveHeader}>
                      <span className={styles.objectiveDescription}>
                        {objective.description || formatObjectiveDescription(objective)}
                      </span>
                      <span className={`${styles.objectiveStatus} ${
                        completed ? styles.completed : styles.incomplete
                      }`}>
                        {completed 
                          ? (i18n.completed || 'Completed')
                          : (i18n.incomplete || 'Incomplete')
                        }
                      </span>
                    </div>
                    
                    {mission.participating && (
                      <div className={styles.progressSection}>
                        <div className={styles.progressText}>
                          {progress} / {target}
                        </div>
                        <div className={styles.progressBar}>
                          <div 
                            className={styles.progressFill}
                            style={{ width: `${progressPercentage}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <div className={styles.modalFooter}>
          {!mission.participating && !mission.isInactive && (
            <button
              className={styles.participateButton}
              onClick={handleParticipate}
              disabled={isParticipating}
            >
              {isParticipating
                ? (i18n.participating || 'Participating...')
                : (i18n.participate || 'Participate')
              }
            </button>
          )}

          {mission.isInactive && (
            <button
              className={`${styles.participateButton} ${styles.inactiveButton}`}
              disabled={true}
            >
              {language === 'tr' ? 'Bekleme Süresi' : 'In Cooldown'}
            </button>
          )}
          
          <button className={styles.closeModalButton} onClick={onClose}>
            {i18n.close || 'Close'}
          </button>
        </div>
      </div>
    </div>
  );
};

export { MissionDetailsModal };
export default MissionDetailsModal;
