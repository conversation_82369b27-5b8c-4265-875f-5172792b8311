.container {
  width: 100%;
}


.title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

/* Theme-specific title colors */
.dailyTheme .title {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.weeklyTheme .title {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.monthlyTheme .title {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.missionsSlider {
  margin-top: 32px;
}

.sliderWrapper {
  position: relative;
  width: 100%;
}

.swiperContainer {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 12px 0;
}

.swiperSlideItem {
  height: auto;
}

.slideContent {
  width: 100%;
  height: 100%;
}

.missionCard {
  border-radius: 20px;
  padding: 24px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
}

.missionCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.02), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.missionCard:hover::before {
  opacity: 1;
}

/* Daily theme */
.dailyTheme .missionCard {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.08), rgba(10, 195, 198, 0.04));
  border: 1px solid rgba(24, 207, 251, 0.2);
}

.dailyTheme .missionCard:hover {
  transform: translateY(-4px);
  border-color: rgba(24, 207, 251, 0.4);
  box-shadow: 0 8px 32px rgba(24, 207, 251, 0.2);
}

/* Weekly theme */
.weeklyTheme .missionCard {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.08), rgba(245, 158, 11, 0.04));
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.weeklyTheme .missionCard:hover {
  transform: translateY(-4px);
  border-color: rgba(251, 191, 36, 0.4);
  box-shadow: 0 8px 32px rgba(251, 191, 36, 0.2);
}

.weeklyTheme .missionCard::before {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.02), transparent);
}

/* Monthly theme */
.monthlyTheme .missionCard {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08), rgba(124, 58, 237, 0.04));
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.monthlyTheme .missionCard:hover {
  transform: translateY(-4px);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.2);
}

.monthlyTheme .missionCard::before {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.02), transparent);
}

.missionCard.completed {
  border-color: rgba(27, 226, 100, 0.622);
  background: linear-gradient(225deg, rgba(31, 183, 87, 0.184), rgba(36, 210, 100, 0.526), rgba(31, 183, 87, 0.184));
}

.missionCard.completed:hover {
  border-color: rgba(8, 238, 92, 0.722);
  box-shadow: 0 8px 32px rgba(24, 251, 62, 0.2);
}

.missionCard.claimed {
  opacity: 0.7;
  border-color: rgba(156, 163, 175, 0.3);
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.08), rgba(156, 163, 175, 0.04));
}

.missionCard.claimed:hover {
  border-color: rgba(156, 163, 175, 0.3);
  box-shadow: none;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.titleSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.missionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  line-height: 1.2;
}

.categoryBadge {
  background: rgba(0, 0, 0, 0.3);
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
}

.rewardSection {
  display: flex;
  align-items: center;
  align-self: flex-start;
  gap: 8px;
  background: #13839f80;
  box-shadow: 0 4px 16px #0cceff3b;
  border: 1px solid #1696b6;
  padding: 8px 16px;
  border-radius: 20px;
  margin-bottom: 16px;
}

.weeklyTheme .rewardSection {
  background: rgba(251, 191, 36, 0.5);
  box-shadow: 0 4px 16px rgba(251, 191, 36, 0.23);
  border: 1px solid rgba(251, 191, 36, 0.7);
}

.monthlyTheme .rewardSection {
  background: rgba(139, 92, 246, 0.5);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.23);
  border: 1px solid rgba(139, 92, 246, 0.7);
}

.rewardIcon {
  display: flex;
  align-items: center;
}

.rewardValue {
  font-size: 1.25rem;
  font-weight: 700;
  color: #18cffb;
}

.weeklyTheme .rewardValue {
  color: #fbbf24;
}

.monthlyTheme .rewardValue {
  color: #8b5cf6;
}

.missionDescription {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.progressSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progressLabel {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.progressValue {
  font-size: 0.9rem;
  font-weight: 600;
  color: #18cffb;
}

.weeklyTheme .progressValue {
  color: #fbbf24;
}

.monthlyTheme .progressValue {
  color: #8b5cf6;
}

.progressValue.completed {
  color: #06d06a;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: rgba(24, 207, 251, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.weeklyTheme .progressBar {
  background: rgba(251, 191, 36, 0.1);
}

.monthlyTheme .progressBar {
  background: rgba(139, 92, 246, 0.1);
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 4px;
  transition: width 0.6s ease;
}

.weeklyTheme .progressFill {
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

.monthlyTheme .progressFill {
  background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 100%);
}

.missionCard.completed .progressFill {
  background: linear-gradient(90deg, #06d06a 0%, #30bf69 100%);
}

.actionSection {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: auto;
}

.claimButton {
  background: linear-gradient(135deg, #01fd86 0%, #06d06a 100%);
  color: #061d2b;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 100%;
}

.claimButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px #01fd8754;
}

.claimButton:active {
  transform: translateY(0);
}

.claimedBadge,
.inProgressBadge {
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  width: 100%;
}

.claimedBadge {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  color: #ffffff;
}

.inProgressBadge {
  background: rgba(24, 207, 251, 0.1);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(24, 207, 251, 0.2);
}

.weeklyTheme .inProgressBadge {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.monthlyTheme .inProgressBadge {
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.participateButton {
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  width: 100%;
  color: #ffffff;
}

.dailyTheme .participateButton {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
}

.dailyTheme .participateButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(24, 207, 251, 0.4);
}

.weeklyTheme .participateButton {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.weeklyTheme .participateButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(251, 191, 36, 0.4);
}

.monthlyTheme .participateButton {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.monthlyTheme .participateButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.participateButton:active {
  transform: translateY(0);
}


/* Mobile slide hint styles */
.mobileSlideHint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 8px 16px;
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.slideHintText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-weight: 500;
  text-align: center;
}

.slideHintArrows {
  display: flex;
  gap: 8px;
  justify-content: end;
}

.slideArrow {
  color: rgba(24, 207, 251, 0.8);
  font-size: 1rem;
  font-weight: bold;
  animation: slideHint 2s ease-in-out infinite;
}

.slideArrow:nth-child(2) {
  animation-delay: 0.5s;
}

@keyframes slideHint {

  0%,
  50%,
  100% {
    opacity: 0.5;
    transform: translateX(0);
  }

  25% {
    opacity: 1;
    transform: translateX(-2px);
  }

  75% {
    opacity: 1;
    transform: translateX(2px);
  }
}

/* Custom pagination styles */
.customPagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
  padding: 0 20px;
}

/* Swiper pagination bullet styles */
.customPagination :global(.swiper-pagination-bullet) {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  opacity: 1;
  transition: all 0.3s ease;
  cursor: pointer;
}

.customPagination :global(.swiper-pagination-bullet-active) {
  background: #18cffb;
  transform: scale(1.2);
}

.customPagination :global(.swiper-pagination-bullet-active-main) {
  background: #18cffb;
  transform: scale(1.4);
}

/* Hide mobile hints on larger screens */
@media (min-width: 1024px) {
  .mobileSlideHint {
    display: none;
  }

  .customPagination {
    display: none;
  }
}

/* Show pagination on mobile and tablet */
@media (max-width: 1023px) {
  .customPagination {
    display: flex;
  }

  .mobileSlideHint {
    display: flex;
  }
}

/* Theme-specific mobile hint colors */
.dailyTheme .mobileSlideHint {
  border-color: rgba(24, 207, 251, 0.2);
  background: rgba(24, 207, 251, 0.05);
}

.dailyTheme .slideArrow {
  color: rgba(24, 207, 251, 0.9);
}

.weeklyTheme .mobileSlideHint {
  border-color: rgba(251, 191, 36, 0.2);
  background: rgba(251, 191, 36, 0.05);
}

.weeklyTheme .slideArrow {
  color: rgba(251, 191, 36, 0.9);
}

.monthlyTheme .mobileSlideHint {
  border-color: rgba(139, 92, 246, 0.2);
  background: rgba(139, 92, 246, 0.05);
}

.monthlyTheme .slideArrow {
  color: rgba(139, 92, 246, 0.9);
}

/* Theme-specific pagination colors */
.dailyTheme .customPagination :global(.swiper-pagination-bullet-active),
.dailyTheme .customPagination :global(.swiper-pagination-bullet-active-main) {
  background: #18cffb;
}

.weeklyTheme .customPagination :global(.swiper-pagination-bullet-active),
.weeklyTheme .customPagination :global(.swiper-pagination-bullet-active-main) {
  background: #fbbf24;
}

.monthlyTheme .customPagination :global(.swiper-pagination-bullet-active),
.monthlyTheme .customPagination :global(.swiper-pagination-bullet-active-main) {
  background: #8b5cf6;
}

/* ===== CUSTOM NAVIGATION BUTTONS ===== */

/* Custom navigation buttons positioned outside overflow container */
.customNextButton,
.customPrevButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 52px;
  height: 52px;
  border: 2px solid;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(12px);
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
}

.dailyTheme .customNextButton,
.dailyTheme .customPrevButton {
  border-color: rgba(24, 207, 251, 0.3);
}

.dailyTheme .customNextButton:hover,
.dailyTheme .customPrevButton:hover {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.2), rgba(10, 195, 198, 0.1));
  border-color: rgba(24, 207, 251, 0.6);
  transform: translateY(-50%) scale(1.1);
}

.weeklyTheme .customNextButton,
.weeklyTheme .customPrevButton {
  border-color: rgba(251, 191, 36, 0.3);
}

.weeklyTheme .customNextButton:hover,
.weeklyTheme .customPrevButton:hover {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(245, 158, 11, 0.1));
  border-color: rgba(251, 191, 36, 0.6);
  transform: translateY(-50%) scale(1.1);
}

.monthlyTheme .customNextButton,
.monthlyTheme .customPrevButton {
  border-color: rgba(139, 92, 246, 0.3);
}

.monthlyTheme .customNextButton:hover,
.monthlyTheme .customPrevButton:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(124, 58, 237, 0.1));
  border-color: rgba(139, 92, 246, 0.6);
  transform: translateY(-50%) scale(1.1);
}

.customNextButton:active,
.customPrevButton:active {
  transform: translateY(-50%) scale(0.95);
}

.customNextButton:disabled,
.customPrevButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.customNextButton:disabled:hover,
.customPrevButton:disabled:hover {
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.customNextButton {
  right: -24px;
}

.customPrevButton {
  left: -24px;
}

.customNextButton svg,
.customPrevButton svg {
  transition: transform 0.2s ease;
}

.customNextButton:hover svg,
.customPrevButton:hover svg {
  transform: scale(1.1);
}

.customNextButton:disabled svg,
.customPrevButton:disabled svg {
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .title {
    font-size: 1.8rem;
  }

  .missionCard {
    padding: 20px;
    min-height: 230px;
  }

  .missionTitle {
    font-size: 1.3rem;
    width: 75%;
  }

  .missionDescription {
    font-size: 0.9rem;
  }

  .rewardSection {
    padding: 6px 12px;
    position: fixed;
    top: 12px;
    right: 8px;
  }

  .rewardValue {
    font-size: 1.1rem;
  }

  .progressSection {
    margin-top: 16px;
  }

  .claimButton {
    padding: 14px 28px;
    font-size: 0.9rem;
  }

  /* Smaller navigation buttons on tablet */
  .customNextButton,
  .customPrevButton {
    width: 44px;
    height: 44px;
  }

  .customNextButton {
    right: -50px;
  }

  .customPrevButton {
    left: -50px;
  }

  .customNextButton svg,
  .customPrevButton svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .missionCard {
    padding: 16px;
    min-height: 210px;
  }

  .missionTitle {
    font-size: 1.2rem;
  }

  .missionDescription {
    font-size: 0.85rem;
  }

  .progressLabel,
  .progressValue {
    font-size: 0.75rem;
  }

  .rewardValue {
    font-size: 1rem;
  }

  .claimButton {
    padding: 12px 24px;
    font-size: 0.85rem;
  }

  .categoryBadge {
    font-size: 0.7rem;
    padding: 3px 10px;
  }

  .rewardSection {
    padding: 6px 12px;
    position: fixed;
    top: 12px;
    right: 8px;
  }

  .progressBar {
    height: 10px;
  }

  .progressSection {
    margin-top: 16px;
  }

  /* Hide navigation buttons on mobile for cleaner look */
  .customNextButton,
  .customPrevButton {
    display: none;
  }
}

/* Empty state styles */
.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  margin: 20px 0;
}

.emptyMessage {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin: 0;
  font-weight: 500;
}

/* Theme-specific empty state colors */
.dailyTheme .emptyState {
  border-color: rgba(24, 207, 251, 0.3);
  background: rgba(24, 207, 251, 0.05);
}

.dailyTheme .emptyMessage {
  color: rgba(24, 207, 251, 0.8);
}

.weeklyTheme .emptyState {
  border-color: rgba(251, 191, 36, 0.3);
  background: rgba(251, 191, 36, 0.05);
}

.weeklyTheme .emptyMessage {
  color: rgba(251, 191, 36, 0.8);
}

.monthlyTheme .emptyState {
  border-color: rgba(139, 92, 246, 0.3);
  background: rgba(139, 92, 246, 0.05);
}

.monthlyTheme .emptyMessage {
  color: rgba(139, 92, 246, 0.8);
}

/* Inactive mission styles */
.missionCard.inactive {
  position: relative;
  opacity: 0.7;
  pointer-events: none;
}

.inactiveOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  z-index: 10;
}

.inactiveMessage {
  text-align: center;
  color: #ffffff;
  padding: 16px;
}

.inactiveTitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #fbbf24;
}

.inactiveCountdown {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.countdownLabel {
  font-size: 0.875rem;
  color: #d1d5db;
}

.countdownTime {
  font-size: 1rem;
  font-weight: 600;
  color: #18cffb;
  font-family: 'Courier New', monospace;
}

.mobileOnly {
  display: flex;
  gap: 8px;
  justify-content: end;
}

@media (min-width: 1024px) {
  .mobileOnly {
    display: none !important;
  }
}

