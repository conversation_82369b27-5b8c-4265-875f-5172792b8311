.container {
  width: 100%;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.08), rgba(10, 195, 198, 0.04));
  border: 1px solid rgba(24, 207, 251, 0.2);
  border-radius: 24px;
  padding: 32px;
  backdrop-filter: blur(12px);
  box-sizing: border-box;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.statCard {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1), rgba(10, 195, 198, 0.05));
  border: 1px solid rgba(24, 207, 251, 0.3);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  border-color: rgba(24, 207, 251, 0.5);
  box-shadow: 0 8px 32px rgba(24, 207, 251, 0.2);
}

.statIcon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 12px;
  color: #061d2b;
}

.statIcon svg { 
  width: 32px;
  height: 32px;
}

.statContent {
  flex: 1;
  min-width: 0;
}

.statValue {
  font-size: 1.8rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.statLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.3;
}

/* Progress Section */
.progressSection {
  margin-top: 32px;
  padding-top: 32px;
  border-top: 1px solid rgba(24, 207, 251, 0.2);
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progressLabel {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.progressValue {
  font-size: 1.1rem;
  font-weight: 700;
  color: #18cffb;
}

.progressBar {
  width: 100%;
  height: 12px;
  background: rgba(24, 207, 251, 0.1);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 6px;
  transition: width 0.8s ease;
  position: relative;
}

.progressFill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(150%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 24px;
  }

  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .statCard {
    padding: 16px;
    gap: 12px;
  }

  .statIcon {
    width: 40px;
    height: 40px;
  }

  .statIcon svg {
    width: 24px;
    height: 24px;
  }

  .statValue {
    font-size: 1.5rem;
  }

  .statLabel {
    font-size: 0.8rem;
  }

  .progressSection {
    margin-top: 24px;
    padding-top: 24px;
  }

  .progressLabel,
  .progressValue {
    font-size: 1rem;
  }

  .progressBar {
    height: 10px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 20px;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 20px;
  }

  .statCard {
    padding: 14px;
    gap: 10px;
  }

  .statIcon {
    width: 36px;
    height: 36px;
  }

  .statIcon svg {
    width: 20px;
    height: 20px;
  }

  .statValue {
    font-size: 1.3rem;
  }

  .statLabel {
    font-size: 0.75rem;
  }

  .progressSection {
    margin-top: 20px;
    padding-top: 20px;
  }

  .progressLabel,
  .progressValue {
    font-size: 0.9rem;
  }

  .progressBar {
    height: 8px;
  }
}

/* Reset Times Section */
.resetTimesSection {
  margin-top: 32px;
  padding-top: 32px;
  border-top: 1px solid rgba(24, 207, 251, 0.2);
}

.resetTimesTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 20px 0;
  text-align: center;
}

.resetTimesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.resetTimeCard {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1), rgba(10, 195, 198, 0.05));
  border: 1px solid rgba(24, 207, 251, 0.3);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s ease;
}

.resetTimeCard:hover {
  transform: translateY(-2px);
  border-color: rgba(24, 207, 251, 0.5);
  box-shadow: 0 4px 16px rgba(24, 207, 251, 0.2);
}

.resetTimeLabel {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
}

.resetTimeValue {
  display: flex;
  justify-content: center;
  gap: 4px;
  flex-wrap: wrap;
}

.timeUnit {
  font-size: 1rem;
  font-weight: 700;
  color: #18cffb;
  font-family: 'Courier New', monospace;
  background: rgba(24, 207, 251, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(24, 207, 251, 0.2);
}

.loading {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.6);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Responsive adjustments for reset times */
@media (max-width: 768px) {
  .resetTimesSection {
    margin-top: 24px;
    padding-top: 24px;
  }

  .resetTimesTitle {
    font-size: 1.1rem;
    margin-bottom: 16px;
  }

  .resetTimesGrid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }

  .resetTimeCard {
    padding: 12px;
  }

  .resetTimeLabel {
    font-size: 0.8rem;
    margin-bottom: 6px;
  }

  .timeUnit {
    font-size: 0.9rem;
    padding: 1px 4px;
  }
}

@media (max-width: 480px) {
  .resetTimesSection {
    margin-top: 20px;
    padding-top: 20px;
  }

  .resetTimesTitle {
    font-size: 1rem;
    margin-bottom: 12px;
  }

  .resetTimesGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .resetTimeCard {
    padding: 10px;
  }

  .resetTimeLabel {
    font-size: 0.75rem;
    margin-bottom: 4px;
  }

  .timeUnit {
    font-size: 0.8rem;
    padding: 1px 3px;
  }
}
