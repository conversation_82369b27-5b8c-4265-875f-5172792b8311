/* ================================================================
   FINAL MISSIONS – FULL STYLESHEET
   (Original rules first, followed by a non‑destructive alignment patch)
   ================================================================ */

/* ---------- ORIGINAL RULES ---------- */

.container {
  width: 100%;
  margin-top: 40px;
}

.header {
  margin-bottom: 32px;
}

.title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
}

.finalMissionsSlider {
  margin-top: 32px;
}

/* Slider wrapper with navigation buttons */
.sliderWrapper {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

/* Swiper container */
.swiperContainer {
  width: 100%;
  height: auto;
  padding: 12px 0;
  box-sizing: border-box;
  overflow: hidden !important;
}

/* Ensure swiper slides take full width of their allocated space */
.swiperContainer .swiper-slide {
  width: auto !important;
  flex-shrink: 0;
}

.swiperSlideItem {
  height: auto;
  width: 100%;
}

/* ENSURE SWIPER SLIDES DON'T INTERFERE WITH CARD HEIGHTS */
.swiperContainer .swiper-slide {
  height: auto !important;
}

.slideContent {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
}

.finalMissionCard {
  width: 100%;
  border-radius: 20px;
  padding: 32px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 480px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
}

/* Theme-specific card styles */
.dailyTheme.finalMissionCard {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.08), rgba(10, 195, 198, 0.04));
  border: 1px solid rgba(24, 207, 251, 0.3);
}

.weeklyTheme.finalMissionCard {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.08), rgba(245, 158, 11, 0.04));
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.monthlyTheme.finalMissionCard {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08), rgba(124, 58, 237, 0.04));
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.finalMissionCard:hover {
  transform: translateY(-4px);
}

.dailyTheme.finalMissionCard:hover {
  border-color: rgba(24, 207, 251, 0.5);
  box-shadow: 0 8px 32px rgba(24, 207, 251, 0.15);
}

.weeklyTheme.finalMissionCard:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 8px 32px rgba(251, 191, 36, 0.15);
}

.monthlyTheme.finalMissionCard:hover {
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.15);
}

.finalMissionCard.unlocked {
  border-width: 2px;
}

.finalMissionCard.completed {
  border: 3px solid #22c55e;
  background: linear-gradient(135deg,
      rgba(34, 197, 94, 0.3) 0%,
      rgba(22, 163, 74, 0.25) 25%,
      rgba(16, 185, 129, 0.2) 50%,
      rgba(5, 150, 105, 0.15) 75%,
      rgba(4, 120, 87, 0.1) 100%);
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.25),
    0 6px 16px rgba(34, 197, 94, 0.15);
  position: relative;
  overflow: hidden;
}

.finalMissionCard.completed::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(34, 197, 94, 0.15) 50%,
      transparent 100%);
  animation: shimmer 4s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.finalMissionCard.completed:hover {
  border-color: #16a34a;
  background: linear-gradient(135deg,
      rgba(34, 197, 94, 0.4) 0%,
      rgba(22, 163, 74, 0.35) 25%,
      rgba(16, 185, 129, 0.3) 50%,
      rgba(5, 150, 105, 0.25) 75%,
      rgba(4, 120, 87, 0.2) 100%);
  box-shadow: 0 0 25px rgba(34, 197, 94, 0.3),
    0 8px 24px rgba(34, 197, 94, 0.2);
  transform: translateY(-6px);
}

.finalMissionCard.claimed {
  opacity: 0.7;
  border-color: rgba(156, 163, 175, 0.3);
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.08), rgba(156, 163, 175, 0.04));
}

.finalMissionCard.locked {
  opacity: 0.6;
}



.missionIcon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px auto;
  color: white;
  font-size: 2rem;
}

.dailyTheme .missionIcon {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  box-shadow: 0 4px 12px rgba(24, 207, 251, 0.2);
}

.weeklyTheme .missionIcon {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.2);
}

.monthlyTheme .missionIcon {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.missionContent {
  flex: 1;
  display: flex;
  flex-direction: column;
}



.missionHeader {
  text-align: center;
  margin-bottom: 20px;
}

.missionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.unlockedBadge {
  display: inline-block;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.missionDescription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  line-height: 1.5;
  margin: 0 0 24px 0;
  text-align: center;
}

.prerequisiteSection {
  margin-bottom: 20px;
}

.prerequisiteHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.prerequisiteLabel {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.prerequisiteValue {
  font-size: 0.85rem;
  font-weight: 600;
  color: #ffffff;
}

.missionProgressSection {
  margin-bottom: 24px;
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progressLabel {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.progressValue {
  font-size: 0.85rem;
  font-weight: 600;
  color: #ffffff;
}

.progressValue.completed {
  color: #22c55e;
}

.progressBar {
  width: 100%;
  height: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  border-radius: 5px;
  transition: width 0.3s ease;
}

.dailyTheme .progressFill {
  background: linear-gradient(90deg, #18cffb 0%, #0ac3c6 100%);
}

.weeklyTheme .progressFill {
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

.monthlyTheme .progressFill {
  background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 100%);
}

.finalMissionCard.completed .progressFill {
  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
}

.rewardSection {
  margin-top: auto;
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 0;
  box-sizing: border-box;
}

.rewardInfo {
  margin-bottom: 0;
  width: 100%;
  text-align: center;
}

.rewardLabel {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
}

.rewardValue {
  font-size: 1.8rem;
  font-weight: 700;
  display: block;
}

.dailyTheme .rewardValue {
  color: #18cffb;
}

.weeklyTheme .rewardValue {
  color: #fbbf24;
}

.monthlyTheme .rewardValue {
  color: #8b5cf6;
}

.finalMissionCard.completed .rewardValue {
  color: #22c55e;
  text-shadow: 0 0 6px rgba(34, 197, 94, 0.4);
}

.finalMissionCard.completed .missionTitle {
  color: #ffffff;
  text-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
}

.finalMissionCard.completed .missionDescription {
  color: rgba(255, 255, 255, 0.95);
}

.finalMissionCard.completed .missionIcon {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  box-shadow: 0 4px 16px rgba(34, 197, 94, 0.3);
}

.claimButton {
  width: 100%;
  max-width: 100%;
  padding: 16px 20px;
  border: none;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dailyTheme .claimButton {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  color: #ffffff;
}

.weeklyTheme .claimButton {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #000000;
}

.monthlyTheme .claimButton {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: #ffffff;
}

.finalMissionCard.completed .claimButton {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: #ffffff;
}

.claimButton:hover {
  transform: translateY(-2px);
}

.dailyTheme .claimButton:hover {
  box-shadow: 0 8px 24px rgba(24, 207, 251, 0.4);
}

.weeklyTheme .claimButton:hover {
  box-shadow: 0 8px 24px rgba(251, 191, 36, 0.4);
}

.monthlyTheme .claimButton:hover {
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}

.finalMissionCard.completed .claimButton:hover {
  box-shadow: 0 8px 24px rgba(34, 197, 94, 0.4);
}

.claimButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.claimedBadge {
  width: 100%;
  max-width: 100%;
  padding: 16px 20px;
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 16px;
  font-weight: 700;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.inProgressBadge {
  width: 100%;
  max-width: 100%;
  padding: 16px 20px;
  border: 1px solid;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dailyTheme .inProgressBadge {
  background: rgba(24, 207, 251, 0.1);
  color: rgba(24, 207, 251, 0.8);
  border-color: rgba(24, 207, 251, 0.2);
}

.weeklyTheme .inProgressBadge {
  background: rgba(251, 191, 36, 0.1);
  color: rgba(251, 191, 36, 0.8);
  border-color: rgba(251, 191, 36, 0.2);
}

.monthlyTheme .inProgressBadge {
  background: rgba(139, 92, 246, 0.1);
  color: rgba(139, 92, 246, 0.8);
  border-color: rgba(139, 92, 246, 0.2);
}

/* ===== CUSTOM NAVIGATION BUTTONS ===== */

.customNextButton,
.customPrevButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 52px;
  height: 52px;
  border: 2px solid rgba(251, 191, 36, 0.3);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(12px);
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
}

.customNextButton:hover,
.customPrevButton:hover {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(245, 158, 11, 0.1));
  border-color: rgba(251, 191, 36, 0.6);
  transform: translateY(-50%) scale(1.1);
}

.customNextButton:active,
.customPrevButton:active {
  transform: translateY(-50%) scale(0.95);
}

.customNextButton:disabled,
.customPrevButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.customNextButton:disabled:hover,
.customPrevButton:disabled:hover {
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.customNextButton {
  right: -24px;
}

.customPrevButton {
  left: -24px;
}

.customNextButton svg,
.customPrevButton svg {
  transition: transform 0.2s ease;
}

.customNextButton:hover svg,
.customPrevButton:hover svg {
  transform: scale(1.1);
}

.customNextButton:disabled svg,
.customPrevButton:disabled svg {
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }

  .finalMissionCard {
    min-height: 380px;
    padding: 28px;
  }

  .missionIcon {
    width: 70px;
    height: 70px;
    margin-bottom: 20px;
  }

  .missionTitle {
    font-size: 1.3rem;
  }

  .rewardValue {
    font-size: 1.5rem;
  }

  /* Smaller navigation buttons on tablet */
  .customNextButton,
  .customPrevButton {
    width: 44px;
    height: 44px;
  }

  .customNextButton {
    right: -50px;
  }

  .customPrevButton {
    left: -50px;
  }

  .customNextButton svg,
  .customPrevButton svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {

  /* Hide navigation buttons on mobile for cleaner look */
  .customNextButton,
  .customPrevButton {
    display: none;
  }

  .finalMissionCard {
    min-height: 350px;
    padding: 24px;
  }

  .missionIcon {
    width: 60px;
    height: 60px;
  }

  .missionTitle {
    font-size: 1.2rem;
  }

  .rewardValue {
    font-size: 1.4rem;
  }
}

/* Additional styles for final mission claims */
.claimReward {
  display: block;
  font-size: 0.9rem;
  margin-top: 8px;
  font-weight: 600;
  color: #22c55e;
}

.lockedBadge {
  width: 100%;
  max-width: 100%;
  padding: 16px 20px;
  background: rgba(107, 114, 128, 0.2);
  color: rgba(107, 114, 128, 0.8);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 16px;
  font-weight: 600;
  font-size: 0.9rem;
  text-align: center;
  line-height: 1.4;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}





/* ---------- END ORIGINAL RULES ---------- */



/* ================================================================
   ALIGNMENT PATCH – DO NOT EDIT ORIGINAL RULES ABOVE
   ================================================================ */

/* 1. Unify minimum heights at every breakpoint */
.finalMissionCard {                       /* desktop */
  min-height: 450px;
}
@media (max-width: 768px) {               /* tablet */
  .finalMissionCard {
    min-height: 380px;
  }
}
@media (max-width: 480px) {               /* mobile */
  .finalMissionCard {
    min-height: 350px;
  }
}

/* 2. Make bottom action/badge blocks identical height */
.claimButton,
.claimedBadge,
.inProgressBadge {
  min-height: 60px;        /* 16 px top + 16 px bottom padding ⇒ 60 px */
  line-height: normal;
}

/* ================================================================
   MISSION ICON ANIMATIONS
   ================================================================ */

/* Special animations for different mission types */
.monthlyTheme .missionIcon svg {
  animation: crownPulse 2s ease-in-out infinite alternate;
}

@keyframes crownPulse {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

/* Hover effects for mission icons */
.dailyTheme .missionIcon:hover svg {
  transform: scale(1.1);
}

.weeklyTheme .missionIcon:hover svg {
  transform: scale(1.1);
}

.monthlyTheme .missionIcon:hover svg {
  transform: scale(1.15);
  animation-play-state: paused;
}

/* Enhanced effects for completed missions */
.finalMissionCard.completed .missionIcon {
  animation: completedGlow 3s ease-in-out infinite alternate;
}

.finalMissionCard.completed .missionIcon svg {
  animation: completedIconPulse 2s ease-in-out infinite alternate !important;
}

@keyframes completedGlow {
  from {
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.3);
  }
  to {
    box-shadow: 0 4px 20px rgba(34, 197, 94, 0.5), 0 0 30px rgba(34, 197, 94, 0.2);
  }
}

@keyframes completedIconPulse {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.1);
  }
}

/* ================================================================
   END OF FILE
   ================================================================ */
