import styles from "./MissionsStats.module.css";
import { IoDiamondOutline } from "react-icons/io5";
import { useMissionResetTimes } from "../../../hooks/useMissionResetTimes";
import { formatTime } from "../../../utils/timeUtils";

export const i18n = {
  en: {
    diamondBalance: "Diamond Balance",
    completionRate: "Completion Rate",
    completedMissions: "Completed Missions",
  },
  tr: {
    diamondBalance: "Elmas Bakiyesi",
    completionRate: "Tamamlama Oranı",
    completedMissions: "Tamamlanan Görevler",
  },
};

const MissionsStats = ({ data, i18n = {}, language = "en" }) => {
  if (!data) return null;

  const {
    diamonds = 0,
    totalParticipations = 0,
    completedParticipations = 0,
    completionRate = 0,
  } = data;

  // Use the reset times hook
  const { countdowns, loading: resetTimesLoading } = useMissionResetTimes();

  return (
    <div className={styles.container}>
      <div className={styles.statsGrid}>
        {/* Diamond Balance */}
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <IoDiamondOutline />
          </div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>{diamonds.toLocaleString()}</div>
            <div className={styles.statLabel}>
              {i18n.diamondBalance || "Diamond Balance"}
            </div>
          </div>
        </div>

        {/* Completed Missions */}
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <svg
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <polyline
                points="14,2 14,8 20,8"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <line
                x1="16"
                y1="13"
                x2="8"
                y2="13"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <line
                x1="16"
                y1="17"
                x2="8"
                y2="17"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <polyline
                points="10,9 9,9 9,10"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>
              {completedParticipations.toLocaleString()}/{totalParticipations.toLocaleString()}
            </div>
            <div className={styles.statLabel}>
              {i18n.completedMissions || "Completed Missions"}
            </div>
          </div>
        </div>

        {/* Completion Rate */}
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <svg
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path
                d="m9 12 2 2 4-4"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>
              {completionRate}%
            </div>
            <div className={styles.statLabel}>
              {i18n.completionRate || "Completion Rate"}
            </div>
          </div>
        </div>


      </div>

      {/* Progress Bar */}
      <div className={styles.progressSection}>
        <div className={styles.progressHeader}>
          <span className={styles.progressLabel}>
            {i18n.completionRate || "Completion Rate"}
          </span>
          <span className={styles.progressValue}>
            {completionRate}%
          </span>
        </div>
        <div className={styles.progressBar}>
          <div
            className={styles.progressFill}
            style={{ width: `${completionRate}%` }}
          />
        </div>
      </div>

      {/* Reset Times Section */}
      <div className={styles.resetTimesSection}>
        <h3 className={styles.resetTimesTitle}>
          {language === 'tr' ? 'Sıfırlama Zamanları' : 'Reset Times'}
        </h3>
        <div className={styles.resetTimesGrid}>
          {/* Daily Reset */}
          <div className={styles.resetTimeCard}>
            <div className={styles.resetTimeLabel}>
              {language === 'tr' ? 'Günlük' : 'Daily'}
            </div>
            <div className={styles.resetTimeValue}>
              {resetTimesLoading ? (
                <span className={styles.loading}>...</span>
              ) : (
                <>
                  <span className={styles.timeUnit}>
                    {formatTime(countdowns.daily.hours)}h
                  </span>
                  <span className={styles.timeUnit}>
                    {formatTime(countdowns.daily.minutes)}m
                  </span>
                  <span className={styles.timeUnit}>
                    {formatTime(countdowns.daily.seconds)}s
                  </span>
                </>
              )}
            </div>
          </div>

          {/* Weekly Reset */}
          <div className={styles.resetTimeCard}>
            <div className={styles.resetTimeLabel}>
              {language === 'tr' ? 'Haftalık' : 'Weekly'}
            </div>
            <div className={styles.resetTimeValue}>
              {resetTimesLoading ? (
                <span className={styles.loading}>...</span>
              ) : (
                <>
                  <span className={styles.timeUnit}>
                    {countdowns.weekly.days}d
                  </span>
                  <span className={styles.timeUnit}>
                    {formatTime(countdowns.weekly.hours)}h
                  </span>
                  <span className={styles.timeUnit}>
                    {formatTime(countdowns.weekly.minutes)}m
                  </span>
                </>
              )}
            </div>
          </div>

          {/* Monthly Reset */}
          <div className={styles.resetTimeCard}>
            <div className={styles.resetTimeLabel}>
              {language === 'tr' ? 'Aylık' : 'Monthly'}
            </div>
            <div className={styles.resetTimeValue}>
              {resetTimesLoading ? (
                <span className={styles.loading}>...</span>
              ) : (
                <>
                  <span className={styles.timeUnit}>
                    {countdowns.monthly.days}d
                  </span>
                  <span className={styles.timeUnit}>
                    {formatTime(countdowns.monthly.hours)}h
                  </span>
                  <span className={styles.timeUnit}>
                    {formatTime(countdowns.monthly.minutes)}m
                  </span>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export { MissionsStats };
export default MissionsStats;
