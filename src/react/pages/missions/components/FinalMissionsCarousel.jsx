import { useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import styles from "./FinalMissionsCarousel.module.css";

const FinalMissionsCarousel = ({
  finalMissions,
  dailyMissions = [],
  weeklyMissions = [],
  monthlyMissions = [],
  i18n = {},
  language = "en",
  onClaimFinalReward,
  isClaimingReward = false
}) => {
  const [swiperInstance, setSwiperInstance] = useState(null);
  const [activeSlide, setActiveSlide] = useState(0);

  if (!finalMissions) return null;

  console.log('FinalMissionsCarousel: finalMissions data:', finalMissions);

  const handleClaimFinalReward = async (claimType) => {
    if (onClaimFinalReward) {
      try {
        await onClaimFinalReward(claimType);
      } catch (error) {
        console.error("Error claiming final mission reward:", error);
      }
    } else {
      console.log("Claiming final mission reward:", claimType);
    }
  };

  // Create final missions array from the real data structure, filtering out empty missions
  const finalMissionsArray = [
    {
      ...finalMissions.daily,
      type: 'daily',
      progressLabel: language === 'tr' ? 'Günlük Görevler' : 'Daily Missions'
    },
    {
      ...finalMissions.weekly,
      type: 'weekly',
      progressLabel: language === 'tr' ? 'Haftalık Görevler' : 'Weekly Missions'
    },
    {
      ...finalMissions.monthly,
      type: 'monthly',
      progressLabel: language === 'tr' ? 'Aylık Görevler' : 'Monthly Missions'
    }
  ].filter(mission => !mission.isEmpty && mission.totalMissions > 0);

  console.log('FinalMissionsCarousel: finalMissionsArray:', finalMissionsArray);

  // If no missions are available, don't render the component
  if (finalMissionsArray.length === 0) {
    return null;
  }

  const handlePrevSlide = () => {
    if (swiperInstance) {
      swiperInstance.slidePrev();
    }
  };

  const handleNextSlide = () => {
    if (swiperInstance) {
      swiperInstance.slideNext();
    }
  };

  const handleSlideChange = (swiper) => {
    setActiveSlide(swiper.realIndex);
  };

  const getLockedText = (type) => {
    switch(type) {
      case 'daily': return i18n.dailyLocked || "Complete all daily missions to unlock";
      case 'weekly': return i18n.weeklyLocked || "Complete all weekly missions to unlock";
      case 'monthly': return i18n.monthlyLocked || "Complete all monthly missions to unlock";
      default: return "Complete all missions to unlock";
    }
  };

  const getThemeClass = (type) => {
    switch(type) {
      case 'daily': return styles.dailyTheme;
      case 'weekly': return styles.weeklyTheme;
      case 'monthly': return styles.monthlyTheme;
      default: return styles.dailyTheme;
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          {i18n.finalMissions || "Final Missions"}
        </h2>
      </div>

      <div className={styles.finalMissionsSlider}>
        <div className={styles.sliderWrapper}>
          <Swiper
            modules={[Autoplay]}
            spaceBetween={24}
            slidesPerView={3}
            centeredSlides={false}
            loop={false}
            autoplay={false}
            speed={600}
            onSwiper={setSwiperInstance}
            onSlideChange={handleSlideChange}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 16,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 20,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 24,
              },
            }}
            className={styles.swiperContainer}
          >
            {finalMissionsArray.map((mission, index) => {
              const isUnlocked = mission.completedMissions === mission.totalMissions && mission.totalMissions > 0;
              const isCompleted = mission.current >= mission.target && mission.target > 0;
              const isClaimed = mission.claimed;
              const canClaim = mission.canClaim;

              console.log(`Final Mission UI: ${mission.type} mission data:`, {
                completedMissions: mission.completedMissions,
                totalMissions: mission.totalMissions,
                isUnlocked,
                isCompleted,
                isClaimed,
                canClaim,
                progressPercentage: mission.totalMissions > 0 ? (mission.completedMissions / mission.totalMissions) * 100 : 0
              });

              return (
                <SwiperSlide key={mission.id || index} className={styles.swiperSlideItem}>
                  <div className={styles.slideContent}>
                    <div
                      className={`${styles.finalMissionCard} ${getThemeClass(mission.type)} ${
                        isUnlocked ? styles.unlocked : styles.locked
                      } ${isCompleted ? styles.completed : ""} ${
                        isClaimed ? styles.claimed : ""
                      }`}
                    >
                      <div className={styles.missionIcon}>
                        {mission.type === 'daily' && (
                          <svg
                            width="36px"
                            height="36px"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            {/* Enhanced sun icon for daily missions */}
                            <circle cx="12" cy="12" r="4.5" fill="currentColor" opacity="0.9"/>
                            <circle cx="12" cy="12" r="2.5" fill="rgba(255,255,255,0.3)"/>
                            <g stroke="currentColor" strokeWidth="2.5" strokeLinecap="round">
                              <path d="M12 1v3M12 20v3M4.22 4.22l2.12 2.12M17.66 17.66l2.12 2.12M1 12h3M20 12h3M4.22 19.78l2.12-2.12M17.66 6.34l2.12-2.12"/>
                            </g>
                          </svg>
                        )}
                        {mission.type === 'weekly' && (
                          <svg
                            width="36px"
                            height="36px"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            {/* Enhanced calendar icon for weekly missions */}
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2" fill="rgba(255,255,255,0.1)"/>
                            <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round"/>
                            <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round"/>
                            <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" strokeWidth="2"/>
                            {/* Week days pattern */}
                            <rect x="6" y="13" width="2.5" height="2.5" rx="0.5" fill="currentColor"/>
                            <rect x="10.25" y="13" width="2.5" height="2.5" rx="0.5" fill="currentColor"/>
                            <rect x="14.5" y="13" width="2.5" height="2.5" rx="0.5" fill="rgba(255,255,255,0.6)"/>
                            <rect x="6" y="17" width="2.5" height="2.5" rx="0.5" fill="rgba(255,255,255,0.6)"/>
                            <rect x="10.25" y="17" width="2.5" height="2.5" rx="0.5" fill="rgba(255,255,255,0.6)"/>
                            <rect x="14.5" y="17" width="2.5" height="2.5" rx="0.5" fill="rgba(255,255,255,0.6)"/>
                            <rect x="18.75" y="17" width="2.5" height="2.5" rx="0.5" fill="rgba(255,255,255,0.6)"/>
                          </svg>
                        )}
                        {mission.type === 'monthly' && (
                          <svg
                            width="36px"
                            height="36px"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            {/* Enhanced crown icon for monthly missions */}
                            <defs>
                              <linearGradient id="crownGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" stopColor="rgba(255,255,255,0.4)"/>
                                <stop offset="100%" stopColor="currentColor"/>
                              </linearGradient>
                            </defs>
                            <path d="M5 16L3 8l5.5 4L12 4l3.5 8L21 8l-2 8H5z" fill="url(#crownGradient)" stroke="rgba(255,255,255,0.2)" strokeWidth="0.5"/>
                            <path d="M5 16h14v2.5a2 2 0 01-2 2H7a2 2 0 01-2-2V16z" fill="currentColor"/>
                            {/* Crown jewels */}
                            <circle cx="12" cy="9" r="1.8" fill="rgba(255,255,255,0.9)" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5"/>
                            <circle cx="7.5" cy="11.5" r="1.2" fill="rgba(255,255,255,0.8)" stroke="rgba(255,255,255,0.2)" strokeWidth="0.3"/>
                            <circle cx="16.5" cy="11.5" r="1.2" fill="rgba(255,255,255,0.8)" stroke="rgba(255,255,255,0.2)" strokeWidth="0.3"/>
                            <circle cx="12" cy="9" r="0.8" fill="rgba(255,255,255,0.6)"/>
                          </svg>
                        )}
                      </div>

                      <div className={styles.missionContent}>
                        <div className={styles.missionHeader}>
                          <h3 className={styles.missionTitle}>{mission.title}</h3>
                          {isUnlocked && (
                            <div className={styles.unlockedBadge}>
                              {i18n.unlocked || "Unlocked!"}
                            </div>
                          )}
                        </div>

                        <p className={styles.missionDescription}>
                          {isUnlocked ? mission.description : getLockedText(mission.type)}
                        </p>

                        {/* Prerequisite Progress */}
                        <div className={styles.prerequisiteSection}>
                          <div className={styles.prerequisiteHeader}>
                            <span className={styles.prerequisiteLabel}>
                              {i18n.progress || "Progress"}
                            </span>
                            <span className={styles.prerequisiteValue}>
                              {`${mission.completedMissions}/${mission.totalMissions} ${mission.progressLabel}`}
                            </span>
                          </div>
                          <div className={styles.progressBar}>
                            <div
                              className={`${styles.progressFill} ${getThemeClass(mission.type)}`}
                              style={{
                                width: `${
                                  mission.totalMissions > 0 ? (mission.completedMissions / mission.totalMissions) * 100 : 0
                                }%`,
                              }}
                            />
                          </div>
                        </div>

                        {/* Mission Progress (only show if unlocked) */}
                        {isUnlocked && (
                          <div className={styles.missionProgressSection}>
                            <div className={styles.progressHeader}>
                              <span className={styles.progressLabel}>
                                Mission {i18n.progress || "Progress"}
                              </span>
                              <span className={styles.progressValue}>
                                {mission.current.toLocaleString()}/
                                {mission.target.toLocaleString()}
                              </span>
                            </div>
                            <div className={styles.progressBar}>
                              <div
                                className={styles.progressFill}
                                style={{
                                  width: `${
                                    mission.target > 0
                                      ? Math.min((mission.current / mission.target) * 100, 100)
                                      : 0
                                  }%`,
                                }}
                              />
                            </div>
                          </div>
                        )}

                        <div className={styles.rewardSection}>
                          <div className={styles.rewardInfo}>
                            <span className={styles.rewardLabel}>
                              {i18n.bigReward || "Big Reward"}:
                            </span>
                            <span className={styles.rewardValue}>
                              💎 {mission.reward.toLocaleString()}
                            </span>
                          </div>

                          {canClaim && (
                            <button
                              className={styles.claimButton}
                              onClick={() => handleClaimFinalReward(mission.type)}
                              disabled={isClaimingReward}
                            >
                              {isClaimingReward ? (i18n.claiming || "Claiming...") : (i18n.claim || "Claim")}
                            </button>
                          )}

                          {isClaimed && (
                            <div className={styles.claimedBadge}>
                              {i18n.claimed || "Claimed"}
                              {mission.claimData && (
                                <span className={styles.claimReward}>
                                  💎 {mission.claimData.grantedReward?.toLocaleString()}
                                </span>
                              )}
                            </div>
                          )}

                          {isUnlocked && !isCompleted && !isClaimed && (
                            <div className={styles.inProgressBadge}>
                              {i18n.progress || "In Progress"}
                            </div>
                          )}

                          {!isUnlocked && (
                            <div className={styles.lockedBadge}>
                              {getLockedText(mission.type)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </SwiperSlide>
              );
            })}
          </Swiper>

          {/* Custom Navigation Buttons */}
          {finalMissionsArray.length > 3 && (
            <>
              <button
                className={styles.customPrevButton}
                type="button"
                aria-label="Previous slide"
                onClick={handlePrevSlide}
                disabled={!swiperInstance}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              <button
                className={styles.customNextButton}
                type="button"
                aria-label="Next slide"
                onClick={handleNextSlide}
                disabled={!swiperInstance}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export { FinalMissionsCarousel };
export default FinalMissionsCarousel;
