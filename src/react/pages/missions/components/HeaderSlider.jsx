import { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay, Navigation } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import styles from "./HeaderSlider.module.css";

const HeaderSlider = ({ slides = [], mobileSlides = [], language = "en" }) => {
  const [swiperInstance, setSwiperInstance] = useState(null);
  const [activeSlide, setActiveSlide] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Use mobile slides if available and on mobile, otherwise use desktop slides
  const currentSlides = isMobile && mobileSlides.length > 0 ? mobileSlides : slides;

  if (!currentSlides.length) {
    return null;
  }

  const handleSlideChange = (swiper) => {
    setActiveSlide(swiper.realIndex);
  };

  return (
    <div className={styles.headerSlider}>
      <Swiper
        modules={[Pagination, Autoplay, Navigation]}
        spaceBetween={0}
        slidesPerView={1}
        centeredSlides={true}
        // loop={currentSlides.length > 1}
        autoplay={currentSlides.length > 1 ? {
          delay: 5000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true
        } : false}
        speed={800}
        pagination={{
          el: `.${styles.customPagination}`,
          clickable: true,
          bulletClass: styles.paginationBullet,
          bulletActiveClass: styles.paginationBulletActive,
          renderBullet: function (index, className) {
            return `<span class="${className}"></span>`;
          },
        }}
        onSwiper={setSwiperInstance}
        onSlideChange={handleSlideChange}
        className={styles.swiperContainer}
        touchRatio={1}
        touchAngle={45}
        grabCursor={true}
        allowTouchMove={true}
        simulateTouch={true}
        watchSlidesProgress={true}
      >
        {currentSlides.map((slide, index) => (
          <SwiperSlide key={slide.id || index} className={styles.swiperSlideItem}>
            <div 
              className={styles.slideContent}
              style={{
                background: slide.backgroundColor,
                color: slide.textColor
              }}
            >
              {/* Background Image */}
              {slide.backgroundImage && (
                <div 
                  className={styles.backgroundImage}
                  style={{
                    backgroundImage: `url(${slide.backgroundImage})`
                  }}
                />
              )}
              
              {/* Overlay */}
              <div className={styles.overlay} />
              
              {/* Content */}
              {(slide.title || slide.subtitle) && (
                <div className={styles.textContent}>
                  {slide.title && (
                    <h1 className={styles.slideTitle}>
                      {slide.title}
                    </h1>
                  )}
                  {slide.subtitle && (
                    <p className={styles.slideSubtitle}>
                      {slide.subtitle}
                    </p>
                  )}
                </div>
              )}
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
      
      {/* Custom Pagination */}
      {currentSlides.length > 1 && (
        <div className={styles.customPagination} />
      )}
    </div>
  );
};

export { HeaderSlider };
export default HeaderSlider;
