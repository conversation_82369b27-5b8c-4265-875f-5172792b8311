.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(24, 207, 251, 0.1);
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 24px;
}

.missionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  margin-bottom: 16px;
}

.closeButton {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.modalBody {
  padding: 0 24px;
}

.missionInfo {
  margin-bottom: 32px;
}

.missionDescription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.rewardSection,
.statusSection {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.rewardLabel,
.statusLabel {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.rewardValue {
  font-size: 1.1rem;
  font-weight: 700;
  color: #18cffb;
}

.statusValue {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusValue.participating {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.statusValue.notParticipating {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

.objectivesSection {
  margin-bottom: 24px;
}

.objectivesTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.objectivesList {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 300px; /* Approximately 3 objectives */
  overflow-y: auto;
  padding-right: 8px;
}

/* Custom scrollbar for objectives list */
.objectivesList::-webkit-scrollbar {
  width: 6px;
}

.objectivesList::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.objectivesList::-webkit-scrollbar-thumb {
  background: rgba(24, 207, 251, 0.5);
  border-radius: 3px;
}

.objectivesList::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 207, 251, 0.7);
}

.objectiveItem {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.objectiveHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.objectiveDescription {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  flex: 1;
}

.objectiveStatus {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.objectiveStatus.completed {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.objectiveStatus.incomplete {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.progressSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progressText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 60px;
}

.progressBar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #18cffb, #0ac3c6);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.modalFooter {
  display: flex;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  justify-content: flex-end;
}

.participateButton {
  background: linear-gradient(135deg, #18cffb, #0ac3c6);
  color: #000000;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.participateButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 207, 251, 0.3);
}

.participateButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.closeModalButton {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.closeModalButton:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 10px;
  }

  .modalContent {
    max-height: 95vh;
  }

  .modalHeader {
    padding: 20px 20px 0 20px;
  }

  .modalBody {
    padding: 0 20px;
  }

  .modalFooter {
    padding: 20px;
    flex-direction: column;
  }

  .objectiveHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .progressSection {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .progressText {
    min-width: auto;
    text-align: center;
  }
}

/* Inactive mission styles */
.statusValue.inactive {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.cooldownSection {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(251, 191, 36, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.cooldownLabel {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.cooldownTime {
  font-size: 1rem;
  font-weight: 700;
  color: #18cffb;
  font-family: 'Courier New', monospace;
}

.inactiveButton {
  background: rgba(156, 163, 175, 0.2) !important;
  color: rgba(156, 163, 175, 0.8) !important;
  cursor: not-allowed !important;
}

.inactiveButton:hover {
  transform: none !important;
  box-shadow: none !important;
}
