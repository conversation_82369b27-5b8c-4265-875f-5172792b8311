import { MissionsCarousel } from "./MissionsCarousel";

export const i18n = {
  en: {
    dailyMissions: "Daily Missions",
    progress: "Progress",
    inProgress: "In Progress",
    reward: "Reward",
    completed: "Completed",
    claim: "Claim",
    claimed: "Claimed",
    diamonds: "Diamonds",
  },
  tr: {
    dailyMissions: "<PERSON>ün<PERSON><PERSON><PERSON> Görevler",
    progress: "İlerleme",
    inProgress: "Devam Ediyor",
    reward: "Ödül",
    completed: "Tamamlandı",
    claim: "Al",
    claimed: "Alındı",
    diamonds: "Elmas",
  },
};

const DailyMissions = ({ missions = [], i18n = {}, language = "en", onParticipate, onClaimReward }) => {
  const handleClaimReward = (missionId) => {
    if (onClaimReward) {
      onClaimReward(missionId);
    } else {
      console.log("Claiming reward for daily mission:", missionId);
    }
  };

  const handleParticipate = async (missionId) => {
    if (onParticipate) {
      await onParticipate(missionId);
    } else {
      console.log("Participating in daily mission:", missionId);
    }
  };

  return (
    <MissionsCarousel
      missions={missions}
      i18n={i18n}
      language={language}
      type="daily"
      onClaimReward={handleClaimReward}
      onParticipate={handleParticipate}
    />
  );
};

export { DailyMissions };
export default DailyMissions;
