import { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper/modules';
import MissionDetailsModal from './MissionDetailsModal';
import { formatCountdown, formatCountdownString } from '../../../utils/timeUtils.js';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import styles from "./MissionsCarousel.module.css";

const MissionsCarousel = ({
  missions = [],
  i18n = {},
  language = "en",
  type = "daily", // daily, weekly, monthly
  onClaimReward,
  onParticipate
}) => {
  const [swiperInstance, setSwiperInstance] = useState(null);
  const [activeSlide, setActiveSlide] = useState(0);
  const [selectedMission, setSelectedMission] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [countdowns, setCountdowns] = useState({});

  // Initialize countdowns for inactive missions
  useEffect(() => {
    const initialCountdowns = {};
    missions.forEach(mission => {
      if (mission.isInactive && mission.nextAllowedParticipationSeconds > 0) {
        initialCountdowns[mission.id] = mission.nextAllowedParticipationSeconds;
      }
    });
    setCountdowns(initialCountdowns);
  }, [missions]);

  // Update countdowns every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdowns(prev => {
        const updated = { ...prev };
        let hasChanges = false;

        Object.keys(updated).forEach(missionId => {
          if (updated[missionId] > 0) {
            updated[missionId]--;
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getTypeConfig = () => {
    const missionCount = missions.length;

    switch(type) {
      case 'weekly':
        return {
          title: `${i18n.weeklyMissions || "Weekly Missions"} (${missionCount})`,
          slidesPerView: 3,
          autoplayDelay: 6000,
          themeClass: styles.weeklyTheme,
          emptyMessage: i18n.noWeeklyMissions || "No weekly missions yet..."
        };
      case 'monthly':
        return {
          title: `${i18n.monthlyMissions || "Monthly Missions"} (${missionCount})`,
          slidesPerView: 3,
          autoplayDelay: 8000,
          themeClass: styles.monthlyTheme,
          emptyMessage: i18n.noMonthlyMissions || "No monthly missions yet..."
        };
      default: // daily
        return {
          title: `${i18n.dailyMissions || "Daily Missions"} (${missionCount})`,
          slidesPerView: 4,
          autoplayDelay: 4000,
          themeClass: styles.dailyTheme,
          emptyMessage: i18n.noDailyMissions || "No daily missions yet..."
        };
    }
  };

  const config = getTypeConfig();

  if (!missions.length) {
    return (
      <div className={`${styles.container} ${config.themeClass}`}>
        <div className={styles.header}>
          <h2 className={styles.title}>
            {config.title}
          </h2>
        </div>
        <div className={styles.emptyState}>
          <p className={styles.emptyMessage}>
            {config.emptyMessage}
          </p>
        </div>
      </div>
    );
  }



  const handlePrevSlide = () => {
    if (swiperInstance) {
      swiperInstance.slidePrev();
    }
  };

  const handleNextSlide = () => {
    if (swiperInstance) {
      swiperInstance.slideNext();
    }
  };

  const handleSlideChange = (swiper) => {
    setActiveSlide(swiper.realIndex);
  };

  const handleClaimReward = (missionId) => {
    if (onClaimReward) {
      onClaimReward(missionId);
    } else {
      console.log(`Claiming reward for ${type} mission:`, missionId);
    }
  };

  const handleMissionClick = (mission) => {
    setSelectedMission(mission);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedMission(null);
  };

  const handleParticipate = async (missionId) => {
    if (onParticipate) {
      await onParticipate(missionId);
    } else {
      console.log(`Participating in mission:`, missionId);
    }
    // Modal will be closed by the modal component itself after successful participation
  };

  // Calculate mission state based on assignments
  const calculateMissionState = (mission) => {
    if (!mission.userAssignments || mission.userAssignments.length === 0) {
      return 'not_started';
    }

    // Check if all assignments are complete
    const allComplete = mission.objectives.every(objective => {
      const assignment = mission.userAssignments.find(
        assignment => assignment.missionObjectiveId === objective.id
      );

      if (!assignment) return false;

      // Use the isCompleted field if available, otherwise fallback to calculation
      if (assignment.hasOwnProperty('isCompleted')) {
        return assignment.isCompleted;
      }

      // Fallback to manual calculation for backward compatibility
      const progress = parseInt(assignment.progress || '0');
      const target = parseInt(objective.targetValue || '1');

      // Check completion based on operator
      switch (objective.operator) {
        case 'ge': return progress >= target;
        case 'gt': return progress > target;
        case 'le': return progress <= target;
        case 'lt': return progress < target;
        case 'eq': return progress === target;
        default: return progress >= target;
      }
    });

    return allComplete ? 'complete' : 'incomplete';
  };

  // Get the first objective type for the chip
  const getFirstObjectiveType = (mission) => {
    if (!mission.objectives || mission.objectives.length === 0) {
      return mission.category || 'GENERAL';
    }
    return mission.objectives[0].objectiveType?.toUpperCase() || mission.category || 'GENERAL';
  };

  return (
    <div className={`${styles.container} ${config.themeClass}`}>
      <div >
        <h2 className={styles.title}>
          {config.title}
        </h2>
      </div>

      <div className={styles.missionsSlider}>
        {/* Mobile slide indicator */}
        {missions.length > 1 && (
            <div className={styles.slideHintArrows + ' ' + styles.mobileOnly}>
              <span className={styles.slideArrow} onClick={handlePrevSlide} style={{cursor: 'pointer'}}>
                ←
              </span>
              <span className={styles.slideArrow} onClick={handleNextSlide} style={{cursor: 'pointer'}}>
                →
              </span>
            </div>
        )}

        <div className={styles.sliderWrapper}>
          <Swiper
            modules={[Autoplay, Pagination]}
            spaceBetween={20}
            slidesPerView={config.slidesPerView}
            centeredSlides={false}
            loop={missions.length > config.slidesPerView}
            autoplay={missions.length > config.slidesPerView ? {
              delay: config.autoplayDelay,
              disableOnInteraction: false,
              pauseOnMouseEnter: true
            } : false}
            speed={600}
            onSwiper={setSwiperInstance}
            onSlideChange={handleSlideChange}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 16,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 18,
              },
              1024: {
                slidesPerView: type === 'daily' ? 3 : 2,
                spaceBetween: 20,
              },
              1440: {
                slidesPerView: config.slidesPerView,
                spaceBetween: 24,
              },
            }}
            pagination={{
              clickable: true,
              dynamicBullets: true,
              dynamicMainBullets: 3,
              el: `.${styles.customPagination}`,
            }}
            className={styles.swiperContainer}
          >
            {missions.map((mission, index) => {
              const missionState = calculateMissionState(mission);
              const firstObjectiveType = getFirstObjectiveType(mission);
              const isCompleted = missionState === 'complete';
              const isClaimed = mission.claimed;
              const isInactive = mission.isInactive;
              const currentCountdown = countdowns[mission.id] || 0;

              // Determine if user is participating based on presence of userAssignments
              const isParticipating = mission.userAssignments && mission.userAssignments.length > 0;

              // Calculate progress for the mission
              let progressPercentage = 0;
              let current = 0;
              let target = 1;

              if (mission.objectives && mission.objectives.length > 0) {
                // Use first objective for progress calculation
                const firstObjective = mission.objectives[0];
                const assignment = mission.userAssignments?.find(
                  assignment => assignment.missionObjectiveId === firstObjective.id
                );
                current = assignment ? parseInt(assignment.progress || '0') : 0;
                target = parseInt(firstObjective.targetValue || '1');
                progressPercentage = target > 0 ? (current / target) * 100 : 0;
              } else if (mission.current !== undefined && mission.target !== undefined) {
                current = mission.current;
                target = mission.target;
                progressPercentage = target > 0 ? (current / target) * 100 : 0;
              }

              return (
                <SwiperSlide key={mission.id || index} className={styles.swiperSlideItem}>
                  <div className={styles.slideContent}>
                    <div
                      className={`${styles.missionCard} ${
                        isCompleted ? styles.completed : ""
                      } ${isClaimed ? styles.claimed : ""} ${
                        isInactive ? styles.inactive : ""
                      }`}
                      onClick={() => !isInactive && handleMissionClick(mission)}
                    >
                      <div className={styles.cardHeader}>
                        <div className={styles.titleSection}>
                          <h3 className={styles.missionTitle}>{mission.title}</h3>
                          <div className={styles.categoryBadge}>
                            {firstObjectiveType}
                          </div>
                        </div>
                      </div>

                      <div className={styles.rewardSection}>
                        💎
                        <span className={styles.rewardValue}>
                          {mission.reward.toLocaleString()}
                        </span>
                      </div>

                      {/* Inactive mission overlay */}
                      {isInactive && (
                        <div className={styles.inactiveOverlay}>
                          <div className={styles.inactiveMessage}>
                            <div className={styles.inactiveTitle}>
                              {language === 'tr' ? 'Görev Tamamlandı' : 'Mission Completed'}
                            </div>
                            <div className={styles.inactiveCountdown}>
                              {currentCountdown > 0 ? (
                                <>
                                  <span className={styles.countdownLabel}>
                                    {language === 'tr' ? 'Tekrar mevcut:' : 'Available again in:'}
                                  </span>
                                  <span className={styles.countdownTime}>
                                    {formatCountdownString(formatCountdown(currentCountdown), language)}
                                  </span>
                                </>
                              ) : (
                                <span className={styles.countdownLabel}>
                                  {language === 'tr' ? 'Şimdi mevcut!' : 'Available now!'}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      <p className={styles.missionDescription}>
                        {mission.description || (mission.objectives && mission.objectives[0]?.description) || `Complete ${firstObjectiveType} objectives`}
                      </p>

                      <div className={styles.progressSection}>
                        <div className={styles.progressHeader}>
                          <span className={styles.progressLabel}>
                            {i18n.progress || "PROGRESS"}
                          </span>
                          <span
                            className={`${styles.progressValue} ${
                              isCompleted ? styles.completed : ""
                            }`}
                          >
                            {current.toLocaleString()}/
                            {target.toLocaleString()}
                          </span>
                        </div>
                        <div className={styles.progressBar}>
                          <div
                            className={styles.progressFill}
                            style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                          />
                        </div>
                      </div>

                      <div className={styles.actionSection}>
                        {isCompleted && !isClaimed && (
                          <button
                            className={styles.claimButton}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleClaimReward(mission.id);
                            }}
                          >
                            {i18n.claim || "CLAIM"}
                          </button>
                        )}

                        {isClaimed && (
                          <div className={styles.claimedBadge}>
                            {i18n.claimed || "CLAIMED"}
                          </div>
                        )}

                        {!isCompleted && !isClaimed && isParticipating && (
                          <div className={styles.inProgressBadge}>
                            {i18n.inProgress || "IN PROGRESS"}
                          </div>
                        )}

                        {!isParticipating && !isClaimed && (
                          <button
                            className={styles.participateButton}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMissionClick(mission);
                            }}
                          >
                            {i18n.participate || "PARTICIPATE"}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </SwiperSlide>
              );
            })}
          </Swiper>

          {/* Pagination dots for mobile */}
          {missions.length > 1 && (
            <div className={styles.customPagination}></div>
          )}

          {/* Custom Navigation Buttons */}
          {missions.length > config.slidesPerView && (
            <>
              <button
                className={styles.customPrevButton}
                type="button"
                aria-label="Previous slide"
                onClick={handlePrevSlide}
                disabled={!swiperInstance}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              <button
                className={styles.customNextButton}
                type="button"
                aria-label="Next slide"
                onClick={handleNextSlide}
                disabled={!swiperInstance}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </>
          )}
        </div>
      </div>

      <MissionDetailsModal
        mission={selectedMission}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onParticipate={handleParticipate}
        i18n={i18n}
        language={language}
      />
    </div>
  );
};

export { MissionsCarousel };
export default MissionsCarousel;
