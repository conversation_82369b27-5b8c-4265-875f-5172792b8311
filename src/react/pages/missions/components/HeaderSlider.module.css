.headerSlider {
  width: 100%;
  height: 290px;
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  margin-bottom: 48px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.swiperContainer {
  width: 100%;
  height: 100%;
  border-radius: 24px;
  touch-action: pan-y pinch-zoom;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.swiperSlideItem {
  height: 100%;
  touch-action: pan-y pinch-zoom;
}

.slideContent {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 24px;
}

.backgroundImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.05) 50%,
      rgba(255, 255, 255, 0.05) 100%);
  z-index: 2;
}

.textContent {
  position: relative;
  z-index: 3;
  text-align: center;
  max-width: 800px;
  padding: 0 40px;
  pointer-events: none;
}

.slideTitle {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0 0 16px 0;
  line-height: 1.1;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg,
      currentColor 0%,
      rgba(255, 255, 255, 0.9) 50%,
      currentColor 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.slideSubtitle {
  font-size: 1.4rem;
  font-weight: 500;
  margin: 0;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Custom Pagination */
.customPagination {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.paginationBullet {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(8px);
}

.paginationBullet:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.2);
}

.paginationBulletActive {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 1);
  transform: scale(1.3);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .headerSlider {
    height: 350px;
    margin-bottom: 32px;
    border-radius: 20px;
  }

  .swiperContainer {
    border-radius: 20px;
  }

  .slideContent {
    border-radius: 20px;
  }

  .textContent {
    padding: 0 24px;
  }

  .slideTitle {
    font-size: 2.5rem;
  }

  .slideSubtitle {
    font-size: 1.1rem;
  }

  .decorativeCircle1 {
    width: 80px;
    height: 80px;
  }

  .decorativeCircle2 {
    width: 60px;
    height: 60px;
  }

  .decorativeCircle3 {
    width: 40px;
    height: 40px;
  }

  .customPagination {
    bottom: 16px;
    gap: 8px;
  }

  .paginationBullet {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 480px) {
  .headerSlider {
    height: 220px;
    margin-bottom: 24px;
    border-radius: 16px;
  }

  .swiperContainer {
    border-radius: 16px;
  }

  .slideContent {
    border-radius: 16px;
  }

  .textContent {
    padding: 0 20px;
  }

  .slideTitle {
    font-size: 2rem;
    margin-bottom: 12px;
  }

  .slideSubtitle {
    font-size: 1rem;
  }

  .decorativeCircle1 {
    width: 60px;
    height: 60px;
  }

  .decorativeCircle2 {
    width: 40px;
    height: 40px;
  }

  .decorativeCircle3 {
    width: 30px;
    height: 30px;
  }

  .customPagination {
    bottom: 12px;
    gap: 6px;
  }

  .paginationBullet {
    width: 8px;
    height: 8px;
  }
}
