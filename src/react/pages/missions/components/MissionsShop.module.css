.container {
  width: 100%;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.03) 0%,
      rgba(124, 58, 237, 0.02) 50%,
      rgba(139, 92, 246, 0.03) 100%);
  border-radius: 24px;
  z-index: -1;
  opacity: 0.8;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 20px;
}

.title {
  font-size: 2.2rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  position: relative;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 100%);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.userBalance {
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(124, 58, 237, 0.1));
  border: 2px solid rgba(139, 92, 246, 0.4);
  border-radius: 20px;
  padding: 14px 20px;
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.2);
  position: relative;
  overflow: hidden;
}

.userBalance::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(139, 92, 246, 0.1) 50%,
      transparent 100%);
  animation: balanceShimmer 3s infinite;
}

@keyframes balanceShimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.diamondIcon {
  color: #18cffb;
  display: flex;
  align-items: center;
  justify-content: center;
}

.balanceText {
  font-size: 1.2rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.shopGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.shopItem {
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.12) 0%,
      rgba(124, 58, 237, 0.08) 50%,
      rgba(139, 92, 246, 0.06) 100%);
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 24px;
  padding: 24px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.1);
}

.shopItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.05) 0%,
      rgba(168, 85, 247, 0.03) 50%,
      transparent 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.shopItem::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.4) 0%,
      rgba(168, 85, 247, 0.3) 50%,
      rgba(124, 58, 237, 0.4) 100%);
  border-radius: 26px;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.shopItem:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(139, 92, 246, 0.6);
  box-shadow: 0 12px 40px rgba(139, 92, 246, 0.25),
    0 4px 16px rgba(139, 92, 246, 0.15);
}

.shopItem:hover::before {
  opacity: 1;
}

.shopItem:hover::after {
  opacity: 1;
}

.shopItem.unaffordable {
  opacity: 0.6;
  border-color: rgba(239, 68, 68, 0.3);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08), rgba(239, 68, 68, 0.04));
}

.shopItem.purchased {
  opacity: 0.7;
  border-color: rgba(34, 197, 94, 0.3);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.08), rgba(34, 197, 94, 0.04));
}

.itemImage {
  width: 100%;
  aspect-ratio: 1;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.15) 0%,
      rgba(168, 85, 247, 0.1) 50%,
      rgba(124, 58, 237, 0.08) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 2px solid rgba(139, 92, 246, 0.2);
  transition: all 0.3s ease;
}

.itemImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.shopItem:hover .itemImage {
  border-color: rgba(139, 92, 246, 0.4);
  transform: scale(1.05);
}

.shopItem:hover .itemImage::before {
  opacity: 1;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.placeholderImage {
  color: rgba(255, 255, 255, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.itemContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.itemName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  line-height: 1.3;
}

.itemDescription {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.itemQuantity {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantityLabel {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.quantityValue {
  font-size: 0.9rem;
  font-weight: 600;
  color: #8b5cf6;
}

.actionSection {
  margin-top: 16px;
}

.buyButton {
  width: 100%;
  padding: 16px 20px;
  background: linear-gradient(135deg,
      #8b5cf6 0%,
      #a855f7 50%,
      #7c3aed 100%);
  color: #ffffff;
  border: none;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1.1rem;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.buyButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.2) 50%,
      transparent 100%);
  transition: left 0.6s ease;
}

.buyButton:hover::before {
  left: 100%;
}

.buyButton:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 28px rgba(139, 92, 246, 0.5),
    0 4px 12px rgba(139, 92, 246, 0.3);
  background: linear-gradient(135deg,
      #9333ea 0%,
      #a855f7 50%,
      #8b5cf6 100%);
}

.buyButton:active {
  transform: translateY(-1px) scale(1.01);
}

.buyButton.disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: not-allowed;
  opacity: 0.6;
}

.buyButton.disabled:hover {
  transform: none;
  box-shadow: none;
}

.purchasedBadge {
  width: 100%;
  padding: 12px 16px;
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  font-weight: 700;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.viewMoreSection {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  position: relative;
}

.viewMoreSection::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(139, 92, 246, 0.5) 50%,
      transparent 100%);
}

.viewMoreButton {
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.15) 0%,
      rgba(168, 85, 247, 0.1) 50%,
      rgba(124, 58, 237, 0.08) 100%);
  border: 2px solid rgba(139, 92, 246, 0.4);
  color: #a855f7;
  padding: 16px 40px;
  border-radius: 20px;
  font-size: 1.1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(16px);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.2);
}

.viewMoreButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(139, 92, 246, 0.1) 50%,
      transparent 100%);
  transition: left 0.6s ease;
}

.viewMoreButton:hover::before {
  left: 100%;
}

.viewMoreButton:hover {
  background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.25) 0%,
      rgba(168, 85, 247, 0.2) 50%,
      rgba(124, 58, 237, 0.15) 100%);
  border-color: rgba(139, 92, 246, 0.6);
  color: #ffffff;
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3),
    0 4px 16px rgba(139, 92, 246, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .title {
    font-size: 1.8rem;
  }

  .userBalance {
    align-self: flex-end;
  }

  .shopGrid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .shopItem {
    padding: 16px;
  }

  .itemImage {
    margin-bottom: 12px;
  }

  .itemName {
    font-size: 1.1rem;
  }

  .itemDescription {
    font-size: 0.85rem;
  }

  .viewMoreButton {
    padding: 12px 24px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .shopGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .shopItem {
    padding: 14px;
  }

  .itemImage {
    margin-bottom: 10px;
  }

  .itemName {
    font-size: 1rem;
  }

  .itemDescription {
    font-size: 0.8rem;
  }

  .balanceText {
    font-size: 1rem;
  }

  .buyButton {
    padding: 10px 16px;
  }

  .buttonText {
    font-size: 0.8rem;
  }

  .buttonPrice {
    font-size: 0.75rem;
  }

  .viewMoreButton {
    padding: 10px 20px;
    font-size: 0.85rem;
  }
}
