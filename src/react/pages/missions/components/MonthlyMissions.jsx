import { MissionsCarousel } from "./MissionsCarousel";

export const i18n = {
  en: {
    monthlyMissions: "Monthly Missions",
    progress: "Progress",
    inProgress: "In Progress",
    reward: "Reward",
    completed: "Completed",
    claim: "Claim",
    claimed: "Claimed",
    diamonds: "Diamonds",
  },
  tr: {
    monthlyMissions: "<PERSON><PERSON><PERSON><PERSON>",
    progress: "İlerleme",
    inProgress: "Devam Ediyor",
    reward: "Ödül",
    completed: "Tamamlandı",
    claim: "Al",
    claimed: "Alındı",
    diamonds: "Elmas",
  },
};

const MonthlyMissions = ({ missions = [], i18n = {}, language = "en", onParticipate, onClaimReward }) => {
  const handleClaimReward = (missionId) => {
    if (onClaimReward) {
      onClaimReward(missionId);
    } else {
      console.log("Claiming reward for monthly mission:", missionId);
    }
  };

  const handleParticipate = async (missionId) => {
    if (onParticipate) {
      await onParticipate(missionId);
    } else {
      console.log("Participating in monthly mission:", missionId);
    }
  };

  return (
    <MissionsCarousel
      missions={missions}
      i18n={i18n}
      language={language}
      type="monthly"
      onClaimReward={handleClaimReward}
      onParticipate={handleParticipate}
    />
  );
};

export { MonthlyMissions };
export default MonthlyMissions;
