.container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 40px 20px;
  box-sizing: border-box;
  min-height: 100vh;
}

/* Header styles removed - now using HeaderSlider component */

.content {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.loadingSpinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(24, 207, 251, 0.2);
  border-top: 4px solid #18cffb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loadingText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0;
}

/* Error State */
.errorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.errorText {
  color: #ff6b6b;
  font-size: 1.1rem;
  text-align: center;
  margin: 0;
  padding: 24px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(12px);
}

/* Empty State */
.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.emptyText {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.1rem;
  text-align: center;
  margin: 0;
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(12px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 20px 16px;
  }

  .content {
    gap: 24px;
  }

  .loadingContainer,
  .errorContainer,
  .emptyContainer {
    padding: 60px 16px;
  }

  .loadingSpinner {
    width: 40px;
    height: 40px;
    border-width: 3px;
  }

  .loadingText,
  .errorText,
  .emptyText {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 16px 12px;
  }

  .content {
    gap: 20px;
  }

  .loadingContainer,
  .errorContainer,
  .emptyContainer {
    padding: 40px 12px;
  }

  .errorText,
  .emptyText {
    padding: 20px;
    font-size: 0.9rem;
  }
}
