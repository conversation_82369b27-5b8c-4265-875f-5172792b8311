/**
 * Time utility functions for missions system
 */

/**
 * Converts seconds to a human-readable countdown format
 * @param {number} seconds - Total seconds remaining
 * @returns {Object} Object with days, hours, minutes, seconds
 */
export const formatCountdown = (seconds) => {
  if (seconds <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }

  const days = Math.floor(seconds / (24 * 60 * 60));
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((seconds % (60 * 60)) / 60);
  const remainingSeconds = seconds % 60;

  return {
    days,
    hours,
    minutes,
    seconds: remainingSeconds
  };
};

/**
 * Formats countdown object to display string
 * @param {Object} countdown - Object with days, hours, minutes, seconds
 * @param {string} language - Language code ('en' or 'tr')
 * @returns {string} Formatted countdown string
 */
export const formatCountdownString = (countdown, language = 'en') => {
  const { days, hours, minutes, seconds } = countdown;
  
  const labels = {
    en: { d: 'd', h: 'h', m: 'm', s: 's' },
    tr: { d: 'g', h: 's', m: 'd', s: 's' }
  };
  
  const l = labels[language] || labels.en;
  
  const parts = [];
  
  if (days > 0) {
    parts.push(`${days}${l.d}`);
  }
  if (hours > 0) {
    parts.push(`${hours}${l.h}`);
  }
  if (minutes > 0) {
    parts.push(`${minutes}${l.m}`);
  }
  if (seconds > 0 || parts.length === 0) {
    parts.push(`${seconds}${l.s}`);
  }
  
  return parts.join(' ');
};

/**
 * Gets the appropriate message for completed missions
 * @param {number} nextAllowedSeconds - Seconds until next participation allowed
 * @param {string} language - Language code ('en' or 'tr')
 * @returns {string} Formatted message
 */
export const getCompletedMissionMessage = (nextAllowedSeconds, language = 'en') => {
  const countdown = formatCountdown(nextAllowedSeconds);
  const countdownString = formatCountdownString(countdown, language);
  
  const messages = {
    en: `You've already completed this mission, come back in ${countdownString}`,
    tr: `Bu görevi zaten tamamladınız, ${countdownString} sonra geri gelin`
  };
  
  return messages[language] || messages.en;
};

/**
 * Checks if a mission is currently inactive (completed and in cooldown)
 * @param {Object} latestParticipation - Latest participation object from backend
 * @returns {boolean} True if mission is inactive
 */
export const isMissionInactive = (latestParticipation) => {
  if (!latestParticipation) return false;
  
  return latestParticipation.isCompleted && 
         latestParticipation.nextAllowedParticipationSeconds > 0;
};

/**
 * Formats time value with leading zero
 * @param {number} value - Time value
 * @returns {string} Formatted time value
 */
export const formatTime = (value) => {
  return value.toString().padStart(2, '0');
};

/**
 * Sorts missions by priority: claimable (completed) → in-progress → not participating
 * @param {Array} missions - Array of mission objects
 * @returns {Array} Sorted missions array
 */
export const sortMissionsByPriority = (missions) => {
  if (!missions || !Array.isArray(missions)) return [];

  return missions.sort((a, b) => {
    // Priority levels: 1 = claimable, 2 = in-progress, 3 = not participating, 4 = inactive
    const getPriority = (mission) => {
      if (mission.isInactive) return 4; // Inactive missions last
      if (mission.completed && !mission.claimed) return 1; // Claimable first
      if (mission.participating) return 2; // In-progress second
      return 3; // Not participating third
    };

    const priorityA = getPriority(a);
    const priorityB = getPriority(b);

    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }

    // If same priority, sort by reward (higher rewards first)
    return (b.reward || 0) - (a.reward || 0);
  });
};
