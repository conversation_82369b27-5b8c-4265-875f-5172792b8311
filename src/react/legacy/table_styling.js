export const renderTable = () => {
  // Switch to enable/disable this script
  const betroz0002active = true;

  if (betroz0002active) {
    // Flag to track if styles have been applied
    let betroz0002stylesApplied = false;

    // Function to apply enhanced table styling
    function betroz0002applyTableStyling() {
      // Check if styles are already applied
      if (betroz0002stylesApplied || document.getElementById('betroz0002-table-styles')) {
        return;
      }

      // Create and inject styles
      const style = document.createElement('style');
      style.id = 'betroz0002-table-styles';
      style.textContent = `
        /* Enhanced Table Styling - Betroz Design */
        .table-wrap {
          background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%) !important;
          border-radius: 16px !important;
          padding: 24px !important;
          margin: 20px 0 !important;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(24, 207, 251, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
          position: relative !important;
          overflow: hidden !important;
          isolation: isolate !important;
        }

        /* Particle background effect */
        .table-wrap::before {
          content: '' !important;
          position: absolute !important;
          top: 0 !important;
          left: 0 !important;
          width: 100% !important;
          height: 100% !important;
          background: radial-gradient(circle at 20% 50%, rgba(24, 207, 251, 0.03) 0%, transparent 50%),
                      radial-gradient(circle at 80% 20%, rgba(10, 195, 198, 0.03) 0%, transparent 50%),
                      radial-gradient(circle at 40% 80%, rgba(24, 207, 251, 0.02) 0%, transparent 50%) !important;
          pointer-events: none !important;
          z-index: 0 !important;
        }

        /* Tab navigation styling */
        .table-wrap .nav-tabs {
          background: rgba(24, 207, 251, 0.05) !important;
          border-radius: 12px !important;
          padding: 4px !important;
          margin-bottom: 20px !important;
          border: 1px solid rgba(24, 207, 251, 0.2) !important;
          position: relative !important;
          z-index: 2 !important;
        }

        .table-wrap .tabs-nav__btn {
          background: transparent !important;
          border: none !important;
          color: rgba(255, 255, 255, 0.7) !important;
          padding: 12px 24px !important;
          border-radius: 8px !important;
          font-weight: 600 !important;
          transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important;
          cursor: pointer !important;
          position: relative !important;
          overflow: hidden !important;
        }

        .table-wrap .tabs-nav__btn.active {
          background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%) !important;
          color: #061d2b !important;
          box-shadow: 0 4px 12px rgba(24, 207, 251, 0.3) !important;
        }

        .table-wrap .tabs-nav__btn:hover:not(.active) {
          background: rgba(24, 207, 251, 0.1) !important;
          color: #ffffff !important;
        }



        /* Enhanced table styling */
        .table-wrap .xtable {
          width: 100% !important;
          border-collapse: separate !important;
          border-spacing: 0 !important;
          background: transparent !important;
          position: relative !important;
          z-index: 2 !important;
        }

        .table-wrap .xtable thead th {
          background: #1b384d !important;
          color: #ffffff !important;
          font-weight: 700 !important;
          padding: 12px 12px !important;
          text-align: left !important;
          border: none !important;
          font-size: 14px !important;
          letter-spacing: 0.5px !important;
          text-transform: uppercase !important;
          position: relative !important;
        }

        .table-wrap .xtable thead th:first-child {
          border-top-left-radius: 12px !important;
        }

        .table-wrap .xtable thead th:last-child {
          border-top-right-radius: 12px !important;
        }

        .table-wrap .xtable tbody tr {
          background: rgba(255, 255, 255, 0.02) !important;
          border: none !important;
        }

        .table-wrap .xtable tbody tr:nth-child(even) {
          background: rgba(24, 207, 251, 0.03) !important;
        }

        .table-wrap .xtable tbody tr:last-child td:first-child {
          border-bottom-left-radius: 12px !important;
        }

        .table-wrap .xtable tbody tr:last-child td:last-child {
          border-bottom-right-radius: 12px !important;
        }

        .table-wrap .xtable td {
          padding: 10px 12px !important;
          border: none !important;
          color: #ffffff !important;
          font-size: 14px !important;
          vertical-align: middle !important;
        }

        /* Enhanced text styling */
        .table-wrap .xtable__text {
          color: #ffffff !important;
          font-weight: 500 !important;
        }

        .table-wrap .xtable__text.text-white {
          color: #ffffff !important;
        }

        .table-wrap .xtable__text a {
          color: #18cffb !important;
          text-decoration: none !important;
          transition: color 0.3s ease !important;
        }

        .table-wrap .xtable__text a:hover {
          color: #0ac3c6 !important;
        }

        /* Coin/currency styling */
        .table-wrap .xtable__coin {
          display: flex !important;
          align-items: center !important;
          gap: 8px !important;
          font-weight: 600 !important;
        }

        .table-wrap .xtable__coin .instrument-icon-wrapper {
          width: 20px !important;
          height: 20px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }

        .table-wrap .xtable__coin .instrument-icon-wrapper svg {
          width: 16px !important;
          height: 16px !important;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
        }

        /* Hidden user styling */
        .table-wrap .xtable__text span svg {
          filter: hue-rotate(170deg) brightness(1.2) !important;
        }

        /* Multiplier styling */
        .table-wrap .xtable .text-right .xtable__text {
          font-weight: 700 !important;
          font-family: 'Courier New', monospace !important;
        }

        /* Profit/Loss color coding */
        .table-wrap .xtable__coin.text-secondary {
          color: #ff6b6b !important;
        }

        .table-wrap .xtable__coin span[id^="win-amount-"] {
          font-weight: 700 !important;
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .table-wrap {
            padding: 16px !important;
            margin: 16px 0 !important;
            border-radius: 12px !important;
          }

          .table-wrap .xtable thead th,
          .table-wrap .xtable td {
            padding: 8px 8px !important;
            font-size: 12px !important;
          }

          .table-wrap .tabs-nav__btn {
            padding: 10px 16px !important;
            font-size: 14px !important;
          }
        }

        @media (max-width: 480px) {
          .table-wrap {
            padding: 12px !important;
          }

          .table-wrap .xtable thead th,
          .table-wrap .xtable td {
            padding: 6px 6px !important;
            font-size: 11px !important;
          }

          .table-wrap .xtable__coin {
            gap: 4px !important;
          }

          .table-wrap .xtable__coin .instrument-icon-wrapper {
            width: 16px !important;
            height: 16px !important;
          }

          .table-wrap .xtable__coin .instrument-icon-wrapper svg {
            width: 12px !important;
            height: 12px !important;
          }
        }
      `;

      document.head.appendChild(style);
      betroz0002stylesApplied = true;
      console.log('Betroz Table Styling: Enhanced styles applied successfully');
    }

    // Function to check if DOM is ready
    function betroz0002isDomReady() {
      return document.readyState === 'complete' || document.readyState === 'interactive';
    }

    // Function to wait for table elements and apply styling
    function betroz0002waitForTable() {
      let attempts = 0;
      const maxAttempts = 50;

      function checkForTable() {
        const tableWrap = document.querySelector('.table-wrap');
        const xtable = document.querySelector('.xtable');

        if (tableWrap || xtable) {
          betroz0002applyTableStyling();
          console.log('Betroz Table Styling: Table found and styled');
          return;
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(checkForTable, 200);
        } else {
          console.log('Betroz Table Styling: Max attempts reached, table not found');
        }
      }

      checkForTable();
    }

    // Set up MutationObserver to watch for dynamically added tables
    function betroz0002setupObserver() {
      const observer = new MutationObserver((mutations) => {
        let shouldCheck = false;

        for (const mutation of mutations) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                if (node.classList?.contains('table-wrap') || 
                    node.classList?.contains('xtable') ||
                    node.querySelector?.('.table-wrap') ||
                    node.querySelector?.('.xtable')) {
                  shouldCheck = true;
                  break;
                }
              }
            }
          }
        }

        if (shouldCheck && !betroz0002stylesApplied) {
          betroz0002applyTableStyling();
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    // Initialize the script
    if (betroz0002isDomReady()) {
      betroz0002waitForTable();
      betroz0002setupObserver();
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        betroz0002waitForTable();
        betroz0002setupObserver();
      });
    }

    console.log('Betroz Table Styling: Script loaded and ready');
  }
}
