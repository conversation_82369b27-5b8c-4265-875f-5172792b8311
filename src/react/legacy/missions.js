import { API_ENDPOINTS } from '../config/environment.js';

export const createMissionsLifecycle = () => {
  // Get bearer token from localStorage
  function betroz0002getBearerToken() {
    try {
      return localStorage.getItem('bearer');
    } catch (error) {
      console.warn('Betroz Missions: Failed to get bearer token from localStorage:', error);
      return null;
    }
  }

  // Create extended user via custom API
  async function betroz0002createExtendedUser() {
    const bearerToken = betroz0002getBearerToken();

    if (!bearerToken) {
      console.log('Betroz Missions: No bearer token found, user not logged in');
      return null;
    }

    try {
      const response = await fetch(API_ENDPOINTS.EXTENDED_USERS_ME, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`Betroz Missions: Extended user creation failed with status ${response.status}`);
        return null;
      }

      const data = await response.json();
      console.log('Betroz Missions: Extended user created successfully:', data);
      return data;
    } catch (error) {
      console.error('Betroz Missions: Failed to create extended user:', error);
      return null;
    }
  }

  // Fetch real missions data from backend
  async function betroz0002fetchRealMissionsData() {
    const bearerToken = betroz0002getBearerToken();

    if (!bearerToken) {
      console.log('Betroz Missions: No bearer token found for missions fetch');
      return null;
    }

    try {
      const response = await fetch(API_ENDPOINTS.MISSIONS, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`Betroz Missions: Missions fetch failed with status ${response.status}`);
        return null;
      }

      const data = await response.json();
      console.log('Betroz Missions: Real missions data fetched successfully:', data);
      return data;
    } catch (error) {
      console.error('Betroz Missions: Failed to fetch real missions data:', error);
      return null;
    }
  }

  // Initialize missions page with extended user creation
  async function betroz0002initializeMissions() {
    console.log('Betroz Missions: Initializing missions page...');

    // Step 1: Create extended user before loading missions
    const extendedUser = await betroz0002createExtendedUser();

    if (extendedUser && extendedUser.success) {
      console.log('Betroz Missions: Extended user ready, proceeding with missions initialization');
      // Store extended user data for later use if needed
      try {
        sessionStorage.setItem('extendedUser', JSON.stringify(extendedUser));
      } catch (error) {
        console.warn('Betroz Missions: Failed to store extended user data:', error);
      }

      // Step 2: Fetch real missions data
      const missionsData = await betroz0002fetchRealMissionsData();
      if (missionsData && missionsData.success) {
        console.log('Betroz Missions: Real missions data fetched successfully');
        try {
          sessionStorage.setItem('realMissionsData', JSON.stringify(missionsData));
        } catch (error) {
          console.warn('Betroz Missions: Failed to store missions data:', error);
        }
      }
    } else {
      console.warn('Betroz Missions: Failed to create extended user, but continuing with missions initialization');
    }

    return extendedUser;
  }

  const render = async (target) => {
    console.log('Betroz Missions: Render called');
    
    // Initialize extended user creation before missions page loads
    await betroz0002initializeMissions();
    
    // The actual missions page rendering will be handled by the React component
    // This lifecycle mainly handles the extended user creation
  }

  const onLanguageChange = (language) => {
    console.log('Betroz Missions: Language changed to:', language);
    // Handle language change if needed
  }

  // Participate in a mission
  async function betroz0002participateInMission(missionId) {
    const bearerToken = betroz0002getBearerToken();

    if (!bearerToken) {
      console.log('Betroz Missions: No bearer token found for participation');
      throw new Error('No bearer token found, user not logged in');
    }

    try {
      const response = await fetch(API_ENDPOINTS.MISSION_PARTICIPATE(missionId), {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Betroz Missions: Mission participation successful:', data);
      return data;
    } catch (error) {
      console.error('Betroz Missions: Failed to participate in mission:', error);
      throw error;
    }
  }

  // Fetch mission statistics
  async function betroz0002fetchMissionStats() {
    const bearerToken = betroz0002getBearerToken();

    if (!bearerToken) {
      console.log('Betroz Missions: No bearer token found for stats fetch');
      return null;
    }

    try {
      const response = await fetch(API_ENDPOINTS.MISSION_STATS, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`Betroz Missions: Stats fetch failed with status ${response.status}`);
        return null;
      }

      const data = await response.json();
      console.log('Betroz Missions: Mission stats fetched successfully:', data);
      return data;
    } catch (error) {
      console.error('Betroz Missions: Failed to fetch mission stats:', error);
      return null;
    }
  }

  return {
    render,
    onLanguageChange,
    createExtendedUser: betroz0002createExtendedUser,
    fetchRealMissionsData: betroz0002fetchRealMissionsData,
    fetchMissionStats: betroz0002fetchMissionStats,
    initializeMissions: betroz0002initializeMissions,
    participateInMission: betroz0002participateInMission
  }
}
