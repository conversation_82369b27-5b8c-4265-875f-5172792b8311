export const createVipClubLifecycle = () => {
  // Flags to track rendering

  // Get bearer token from localStorage
  function betroz0001getBearerToken() {
    try {
      return localStorage.getItem('bearer');
    } catch (error) {
      console.warn('Betroz VIP Club: Failed to get bearer token from localStorage:', error);
      return null;
    }
  }

  // Ping API to find the user's rank
  async function betroz0001fetchUserData() {
    const bearerToken = betroz0001getBearerToken();

    if (!bearerToken) {
      console.log('Betroz VIP Club: No bearer token found, user not logged in');
      return null;
    }

    try {
      const response = await fetch('https://api1.ebetlab.com/api/player/user', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`Betroz VIP Club: API request failed with status ${response.status}`);
        return null;
      }

      const data = await response.json();

      if (data.success && data.data && data.data.rank) {
        return {
          rank: data.data.rank,
          percentage: data.data.percentage || '0.00',
          turnover: data.data.turnover || '0.00'
        };
      } else {
        console.warn('Betroz VIP Club: Invalid API response structure');
        return null;
      }
    } catch (error) {
      console.error('Betroz VIP Club: Failed to fetch user data:', error);
      return null;
    }
  }

  // Complete VIP tier data - All 23 levels with exact data from CSV
  const betroz0001vipTiers = [
    {
      id: 'no-vip',
      name: 'Non‑VIP',
      rank: 0,
      wagerAmount: '$0',
      wagerAmountFull: '$0',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #071e2d 50%, #082030 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $0',
        'Rakeback: 0%',
        'Instant Loss Back: 10%',
        'Weekly Loss Back: 0%',
        'Monthly Loss Back: 0%',
        'Daily Reload: $0'
      ]
    },
    {
      id: 'iron',
      name: 'Iron',
      rank: 1,
      wagerAmount: '$5k',
      wagerAmountFull: '$5,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #071e2d 50%, #082030 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $10',
        'Rakeback: 0.05%',
        'Instant Loss Back: 10%',
        'Weekly Loss Back: 6%',
        'Monthly Loss Back: 7%',
        'Daily Reload: $0.5'
      ]
    },
    {
      id: 'copper',
      name: 'Copper',
      rank: 2,
      wagerAmount: '$10k',
      wagerAmountFull: '$10,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #071e2d 50%, #082030 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $15',
        'Rakeback: 0.05%',
        'Instant Loss Back: 10%',
        'Weekly Loss Back: 6%',
        'Monthly Loss Back: 7%',
        'Daily Reload: $0.75'
      ]
    },
    {
      id: 'bronze',
      name: 'Bronze',
      rank: 3,
      wagerAmount: '$25k',
      wagerAmountFull: '$25,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #071e2d 50%, #082030 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $32.5',
        'Rakeback: 0.05%',
        'Instant Loss Back: 10%',
        'Weekly Loss Back: 6%',
        'Monthly Loss Back: 7%',
        'Daily Reload: $1.63'
      ]
    },
    {
      id: 'brass',
      name: 'Brass',
      rank: 4,
      wagerAmount: '$50k',
      wagerAmountFull: '$50,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #071e2d 50%, #082030 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $50',
        'Rakeback: 0.05%',
        'Instant Loss Back: 12%',
        'Weekly Loss Back: 6%',
        'Monthly Loss Back: 7%',
        'Daily Reload: $2.5'
      ]
    },
    {
      id: 'nickel',
      name: 'Nickel',
      rank: 5,
      wagerAmount: '$75k',
      wagerAmountFull: '$75,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #082030 50%, #092235 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $75',
        'Rakeback: 0.07%',
        'Instant Loss Back: 12%',
        'Weekly Loss Back: 7%',
        'Monthly Loss Back: 8%',
        'Daily Reload: $3.75'
      ]
    },
    {
      id: 'steel',
      name: 'Steel',
      rank: 6,
      wagerAmount: '$100k',
      wagerAmountFull: '$100,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #082030 50%, #092235 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $100',
        'Rakeback: 0.07%',
        'Instant Loss Back: 12%',
        'Weekly Loss Back: 7%',
        'Monthly Loss Back: 8%',
        'Daily Reload: $5'
      ]
    },
    {
      id: 'cobalt',
      name: 'Cobalt',
      rank: 7,
      wagerAmount: '$150k',
      wagerAmountFull: '$150,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #082030 50%, #092235 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $150',
        'Rakeback: 0.07%',
        'Instant Loss Back: 12%',
        'Weekly Loss Back: 7%',
        'Monthly Loss Back: 8%',
        'Daily Reload: $7.5'
      ]
    },
    {
      id: 'titanium',
      name: 'Titanium',
      rank: 8,
      wagerAmount: '$250k',
      wagerAmountFull: '$250,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #082030 50%, #092235 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $250',
        'Rakeback: 0.07%',
        'Instant Loss Back: 14%',
        'Weekly Loss Back: 7%',
        'Monthly Loss Back: 8%',
        'Daily Reload: $12.5'
      ]
    },
    {
      id: 'tungsten',
      name: 'Tungsten',
      rank: 9,
      wagerAmount: '$500k',
      wagerAmountFull: '$500,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #082030 50%, #092235 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $500',
        'Rakeback: 0.07%',
        'Instant Loss Back: 14%',
        'Weekly Loss Back: 7%',
        'Monthly Loss Back: 8%',
        'Daily Reload: $25'
      ]
    },
    {
      id: 'silver',
      name: 'Silver',
      rank: 10,
      wagerAmount: '$1M',
      wagerAmountFull: '$1,000,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #082030 50%, #0a2438 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $1,000',
        'Rakeback: 0.07%',
        'Instant Loss Back: 14%',
        'Weekly Loss Back: 7%',
        'Monthly Loss Back: 8%',
        'Daily Reload: $50'
      ]
    },
    {
      id: 'gold',
      name: 'Gold',
      rank: 11,
      wagerAmount: '$2M',
      wagerAmountFull: '$2,000,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #082030 50%, #0a2438 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $1,750',
        'Rakeback: 0.07%',
        'Instant Loss Back: 14%',
        'Weekly Loss Back: 7%',
        'Monthly Loss Back: 8%',
        'Daily Reload: $87.5'
      ]
    },
    {
      id: 'platinum',
      name: 'Platinum',
      rank: 12,
      wagerAmount: '$3.5M',
      wagerAmountFull: '$3,500,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0a2438 50%, #0c2a42 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $2,500',
        'Rakeback: 0.10%',
        'Instant Loss Back: 16%',
        'Weekly Loss Back: 8%',
        'Monthly Loss Back: 9%',
        'Daily Reload: $125'
      ]
    },
    {
      id: 'palladium',
      name: 'Palladium',
      rank: 13,
      wagerAmount: '$5M',
      wagerAmountFull: '$5,000,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0a2438 50%, #0c2a42 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $3,000',
        'Rakeback: 0.10%',
        'Instant Loss Back: 16%',
        'Weekly Loss Back: 8%',
        'Monthly Loss Back: 9%',
        'Daily Reload: $150'
      ]
    },
    {
      id: 'rhodium',
      name: 'Rhodium',
      rank: 14,
      wagerAmount: '$7.5M',
      wagerAmountFull: '$7,500,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0a2438 50%, #0c2a42 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $3,500',
        'Rakeback: 0.10%',
        'Instant Loss Back: 16%',
        'Weekly Loss Back: 8%',
        'Monthly Loss Back: 9%',
        'Daily Reload: $175'
      ]
    },
    {
      id: 'osmium',
      name: 'Osmium',
      rank: 15,
      wagerAmount: '$10M',
      wagerAmountFull: '$10,000,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0a2438 50%, #0c2a42 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $4,000',
        'Rakeback: 0.10%',
        'Instant Loss Back: 16%',
        'Weekly Loss Back: 8%',
        'Monthly Loss Back: 9%',
        'Daily Reload: $200'
      ]
    },
    {
      id: 'iridium',
      name: 'Iridium',
      rank: 16,
      wagerAmount: '$15M',
      wagerAmountFull: '$15,000,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0a2438 50%, #0c2a42 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $6,000',
        'Rakeback: 0.10%',
        'Instant Loss Back: 18%',
        'Weekly Loss Back: 8%',
        'Monthly Loss Back: 9%',
        'Daily Reload: $300'
      ]
    },
    {
      id: 'mithril',
      name: 'Mithril',
      rank: 17,
      wagerAmount: '$25M',
      wagerAmountFull: '$25,000,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0c2a42 50%, #0e2f4a 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $10,000',
        'Rakeback: 0.12%',
        'Instant Loss Back: 18%',
        'Weekly Loss Back: 9%',
        'Monthly Loss Back: 10%',
        'Daily Reload: $500'
      ]
    },
    {
      id: 'adamantite',
      name: 'Adamantite',
      rank: 18,
      wagerAmount: '$50M',
      wagerAmountFull: '$50,000,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0c2a42 50%, #0e2f4a 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $32,500',
        'Rakeback: 0.12%',
        'Instant Loss Back: 18%',
        'Weekly Loss Back: 9%',
        'Monthly Loss Back: 10%',
        'Daily Reload: $1,625'
      ]
    },
    {
      id: 'orichalcum',
      name: 'Orichalcum',
      rank: 19,
      wagerAmount: '$100M',
      wagerAmountFull: '$100,000,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0c2a42 50%, #0e2f4a 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $65,000',
        'Rakeback: 0.12%',
        'Instant Loss Back: 18%',
        'Weekly Loss Back: 9%',
        'Monthly Loss Back: 10%',
        'Daily Reload: $3,250'
      ]
    },
    {
      id: 'vibranium',
      name: 'Vibranium',
      rank: 20,
      wagerAmount: '$150M',
      wagerAmountFull: '$150,000,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0c2a42 50%, #0e2f4a 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $75,000',
        'Rakeback: 0.12%',
        'Instant Loss Back: 20%',
        'Weekly Loss Back: 9%',
        'Monthly Loss Back: 10%',
        'Daily Reload: $3,750'
      ]
    },
    {
      id: 'unobtanium',
      name: 'Unobtanium',
      rank: 21,
      wagerAmount: '$200M',
      wagerAmountFull: '$200,000,000',
      color: '#0ac3c6',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0c2a42 50%, #0e2f4a 100%)',
      borderColor: '#0ac3c6',
      benefits: [
        'Gift: $85,000',
        'Rakeback: 0.12%',
        'Instant Loss Back: 20%',
        'Weekly Loss Back: 9%',
        'Monthly Loss Back: 10%',
        'Daily Reload: $4,250'
      ]
    },
    {
      id: 'eternium',
      name: 'Eternium',
      rank: 22,
      wagerAmount: '$300M',
      wagerAmountFull: '$300,000,000',
      color: '#18cffb',
      bgGradient: 'linear-gradient(135deg, #061d2b 0%, #0c2a42 50%, #0e2f4a 100%)',
      borderColor: '#18cffb',
      benefits: [
        'Gift: $100,000',
        'Rakeback: 0.12%',
        'Instant Loss Back: 20%',
        'Weekly Loss Back: 9%',
        'Monthly Loss Back: 10%',
        'Daily Reload: $5,000'
      ]
    }
  ];

  // VIP benefits data for the benefits section
  const betroz0001vipBenefits = [
    {
      id: 'boost',
      icon: `<svg viewBox="0 0 24 24"><path d="M13,10H18L16.5,8.5L17.92,7.08L21.84,11L17.92,14.92L16.5,13.5L18,12H13V10M11,12H6L7.5,13.5L6.08,14.92L2.16,11L6.08,7.08L7.5,8.5L6,10H11V12Z"/></svg>`
    },
    {
      id: 'vipHost',
      icon: `<svg viewBox="0 0 24 24"><path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/></svg>`
    },
    {
      id: 'recentPlay',
      icon: `<svg viewBox="0 0 24 24"><path d="M5,6H23V18H5V6M14,9A3,3 0 0,1 17,12A3,3 0 0,1 14,15A3,3 0 0,1 11,12A3,3 0 0,1 14,9M9,8A2,2 0 0,1 7,10V14A2,2 0 0,1 9,16H19A2,2 0 0,1 21,14V10A2,2 0 0,1 19,8H9Z"/></svg>`
    },
    {
      id: 'levelUps',
      icon: `<svg viewBox="0 0 24 24"><path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/></svg>`
    },
    {
      id: 'bespoke',
      icon: `<svg viewBox="0 0 24 24"><path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/></svg>`
    },
    {
      id: 'exclusive',
      icon: `<svg viewBox="0 0 24 24"><path d="M5,16L3,5H1V3H4L6,14H18.5L19.5,12H21.7L20.2,16H5M6,18A2,2 0 0,0 8,20A2,2 0 0,0 6,18M15,18A2,2 0 0,0 17,20A2,2 0 0,0 15,18M12.5,7L13.5,9H16L14,10.5L15,12.5L12.5,11L10,12.5L11,10.5L9,9H11.5L12.5,7Z"/></svg>`
    }
  ];

  // Function to add styled container (adapted from reference addStyledContainer)
  async function betroz0001addStyledContainer(parentElement) {
    // Create the container wrapper
    const container = document.createElement("div");
    container.id = "betroz0001-vip-ranking-wrapper";
    container.className = "betroz0001vip-ranking-container";

    // Get current translations
    const t = betroz0001getTranslations();

    // Fetch user data to determine current level
    const userData = await betroz0001fetchUserData();

    console.log('Betroz VIP Club: User data:', userData);

    // Generate all VIP tier cards with user data
    let cardsHtml = '';
    betroz0001vipTiers.forEach(tier => {
      cardsHtml += betroz0001generateVipCard(tier, userData);
    });

    // Generate VIP benefits HTML
    const benefitsHTML = betroz0001vipBenefits.map((benefit, index) => `
      <div class="betroz0001-benefit-item">
        <div class="betroz0001-benefit-icon">
          ${benefit.icon}
        </div>
        <div class="betroz0001-benefit-content">
          <h3 class="betroz0001-benefit-title">${t.benefits[benefit.id].title}</h3>
          <p class="betroz0001-benefit-description">${t.benefits[benefit.id].description}</p>
        </div>
      </div>
    `).join('');

    // Set the container content with integrated benefits
    container.innerHTML = `
      <div class="betroz0001particle-container" id="betroz0001particle-container"></div>
      <div class="betroz0001diamond-section">
        <div class="betroz0001diamond-container">
          <div class="betroz0001diamond-shape"></div>
          <div class="betroz0001diamond-shape"></div>
          <div class="betroz0001diamond-shape"></div>
        </div>
      </div>
      <h2 class="betroz0001vip-ranking-title">${t.title}</h2>
      <div class="betroz0001cards-slider">
        ${cardsHtml}
      </div>
      <div class="betroz0001-vip-benefits-section">
        <h2 class="betroz0001-vip-title">${t.benefitsTitle}</h2>
        <div class="betroz0001-benefits-grid">
          ${benefitsHTML}
        </div>
      </div>
    `;

    // Append to parent element
    parentElement.appendChild(container);

    console.log('Betroz VIP Club: VIP ranking system with integrated benefits added to container');

    // Initialize particle animation after rendering
    setTimeout(() => {
      betroz0001initParticleAnimation();
    }, 100);

    // Initialize drag functionality for the cards slider
    setTimeout(() => {
      betroz0001initDragFunctionality();
    }, 50);
  }

  // Function to get current language
  function betroz0001getCurrentLanguage() {
    return document.documentElement.lang || "en";
  }

  // Translations for different languages
  const betroz0001translations = {
    en: {
      title: "Betroz <span class='betroz0001vip-highlight'>VIP</span> ranking system",
      wagerAmount: "Wager amount",
      benefitsTitle: "Betroz VIP Club benefits",
      benefits: {
        boost: {
          title: "Performance Boost",
          description: "Unlock weekly and monthly rewards that scale with your gaming activity. Your dedication translates into bigger bonuses."
        },
        vipHost: {
          title: "Personal VIP Manager",
          description: "Get assigned a personal VIP manager who understands your preferences and provides tailored assistance."
        },
        recentPlay: {
          title: "Cashback Rewards",
          description: "Experience setbacks? Betroz provides cashback on your losses whenever you advance to the next level."
        },
        levelUps: {
          title: "Tier Advancement Rewards",
          description: "Unlock instant rewards when you progress to higher tiers. Each advancement brings increasingly valuable benefits."
        },
        bespoke: {
          title: "Customized Perks",
          description: "Collaborate with your VIP manager to create personalized rewards that match your gaming style and preferences."
        },
        exclusive: {
          title: "Premium Access",
          description: "Enjoy exclusive access to special tournaments, unique promotions, and priority customer service."
        }
      }
    },
    tr: {
      title: "Betroz <span class='betroz0001vip-highlight'>VIP</span> sıralama sistemi",
      wagerAmount: "Bahis miktarı",
      benefitsTitle: "Betroz VIP Kulüp Avantajları",
      benefits: {
        boost: {
          title: "Artırım",
          description: "Her hafta ve her ay, son oyunlarınıza dayalı taze bir bonus bekleyin. Ne kadar çok oynarsanız, bonuslar o kadar yüksek olur."
        },
        vipHost: {
          title: "Özel VIP Temsilcisi",
          description: "Bahis ihtiyaçlarınızı destekleyecek ve karşılayacak kendi özel VIP temsilcinizi alın."
        },
        recentPlay: {
          title: "Son Oyun Bonusları",
          description: "Şansınız kötü mü gidiyor? Betroz her seviye atladığınızda kayıplarınızdan para iadesi sunar."
        },
        levelUps: {
          title: "Seviye Atlamaları",
          description: "Yeni bir seviyeye ulaşın ve ödeme alın. Seviye atlamaları ne kadar yüksek giderseniz o kadar iyi olur."
        },
        bespoke: {
          title: "Özel Avantajlar",
          description: "Oyun ihtiyaçlarınıza göre avantajları uyarlamak için özel VIP temsilcinizle çalışın."
        },
        exclusive: {
          title: "Özel Ödüller",
          description: "Özel turnuvalara, özel promosyonlara ve premium müşteri desteğine erişim."
        }
      }
    },
    de: {
      title: "Betroz <span class='betroz0001vip-highlight'>VIP</span> Ranking-System",
      wagerAmount: "Einsatzbetrag",
      benefitsTitle: "Betroz VIP Club Vorteile",
      benefits: {
        boost: {
          title: "Boost",
          description: "Jede Woche und jeden Monat erwarten Sie einen frischen Bonus basierend auf Ihren letzten Spielen. Je mehr Sie spielen, desto höher die Boni."
        },
        vipHost: {
          title: "Persönlicher VIP Host",
          description: "Erhalten Sie Ihren eigenen persönlichen VIP-Host, der Ihre Wettbedürfnisse unterstützt und betreut."
        },
        recentPlay: {
          title: "Aktuelle Spiel-Boni",
          description: "Haben Sie eine Pechsträhne? Betroz bietet Geld zurück bei Verlusten jedes Mal, wenn Sie ein Level aufsteigen."
        },
        levelUps: {
          title: "Level-Ups",
          description: "Erreichen Sie ein neues Level und werden Sie bezahlt. Die Level-Ups werden besser, je höher Sie kommen."
        },
        bespoke: {
          title: "Maßgeschneiderte Vorteile",
          description: "Arbeiten Sie mit Ihrem persönlichen VIP-Host zusammen, um Vorteile an Ihre Spielbedürfnisse anzupassen."
        },
        exclusive: {
          title: "Exklusive Belohnungen",
          description: "Zugang zu exklusiven Turnieren, besonderen Aktionen und Premium-Kundensupport."
        }
      }
    }
  };

  // Function to get translations for current language
  function betroz0001getTranslations() {
    const lang = betroz0001getCurrentLanguage();
    return betroz0001translations[lang] || betroz0001translations.en;
  }

  // Function to generate a single VIP tier card
  function betroz0001generateVipCard(tierData, userData = null) {
    // Use actual user data if available, otherwise no level is current
    const userRank = userData ? userData.rank : null;
    const userLevel = userData ? userData.level : null;

    // Compare with the card's rank (numeric) to determine achievement
    const isAchieved = userLevel !== null ? tierData.rank <= userLevel : false;
    const isCurrentLevel = userRank !== null ? tierData.id === userRank : false;

    const benefitsList = tierData.benefits.map(benefit =>
      `<div class="betroz0001tier-benefit-item">
        <div class="betroz0001tier-benefit-check">✓</div>
        <span class="betroz0001tier-benefit-text">${benefit}</span>
      </div>`
    ).join('');

    return `
      <div class="betroz0001vip-card ${isCurrentLevel ? 'current-level' : ''}" data-tier="${tierData.id}"
            style="--tier-color: ${tierData.color}; --tier-bg: ${tierData.bgGradient}; --tier-border: ${tierData.borderColor};">
        <div class="betroz0001progress-circle ${isAchieved ? 'achieved' : 'locked'}">
          <div class="betroz0001progress-icon">
            ${isAchieved ? '✓' : '?'}
          </div>
        </div>
        <div class="betroz0001card-header">
          <div class="betroz0001tier-badge">${tierData.name}</div>
          <div class="betroz0001wager-amount">
            <div class="betroz0001wager-value">${tierData.wagerAmount}</div>
            <div class="betroz0001wager-label">${betroz0001getTranslations().wagerAmount}</div>
          </div>
        </div>
        <div class="betroz0001tier-benefits-list">
          ${benefitsList}
        </div>
      </div>
    `;
  }

  // Simple mouse drag functionality for cards slider
  function betroz0001initDragFunctionality() {
    const slider = document.querySelector('.betroz0001cards-slider');
    if (!slider) return;

    let isDown = false;
    let startX;
    let scrollLeft;

    slider.addEventListener('mousedown', (e) => {
      isDown = true;
      slider.classList.add('active');
      startX = e.pageX - slider.offsetLeft;
      scrollLeft = slider.scrollLeft;
    });

    slider.addEventListener('mouseleave', () => {
      isDown = false;
      slider.classList.remove('active');
    });

    slider.addEventListener('mouseup', () => {
      isDown = false;
      slider.classList.remove('active');
    });

    slider.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - slider.offsetLeft;
      const walk = (x - startX) * 2;
      slider.scrollLeft = scrollLeft - walk;
    });
  }

  // Particle animation system
  function betroz0001initParticleAnimation() {
    const particleContainer = document.getElementById('betroz0001particle-container');
    if (!particleContainer) return;

    function betroz0001createParticle() {
      const particle = document.createElement('div');
      particle.className = 'betroz0001particle';

      // Random horizontal position
      particle.style.left = Math.random() * 100 + '%';

      // Random animation delay
      particle.style.animationDelay = Math.random() * 10 + 's';

      particleContainer.appendChild(particle);

      // Clear particle after animation
      setTimeout(() => {
        // Check if particle still exists before clearing
        if (particle) {
          try {
            particle.innerHTML = '';
            particle.style.display = 'none';
          } catch (error) {
            console.warn('Betroz VIP Club: Failed to clear particle:', error);
          }
        }
      }, 10000);
    }

    // Create initial particles
    for (let i = 0; i < 12; i++) {
      setTimeout(() => betroz0001createParticle(), i * 600);
    }

    // Continue creating particles
    setInterval(betroz0001createParticle, 1000);
  }

  // Function to update all language content (integrated version)
  function betroz0001updateLanguageContent() {
    const existingRanking = document.getElementById('betroz0001-vip-ranking-wrapper');
    if (!existingRanking) {
      return;
    }

    const t = betroz0001getTranslations();

    // Update ranking title
    const rankingTitle = existingRanking.querySelector('.betroz0001vip-ranking-title');
    if (rankingTitle) {
      rankingTitle.innerHTML = t.title;
    }

    // Update wager amount labels in all cards
    const wagerLabels = existingRanking.querySelectorAll('.betroz0001wager-label');
    wagerLabels.forEach(label => {
      label.textContent = t.wagerAmount;
    });

    // Update benefits title
    const benefitsTitle = existingRanking.querySelector('.betroz0001-vip-title');
    if (benefitsTitle) benefitsTitle.textContent = t.benefitsTitle;

    // Update benefit items
    const benefitItems = existingRanking.querySelectorAll('.betroz0001-benefit-item');
    benefitItems.forEach((item, index) => {
      const benefit = betroz0001vipBenefits[index];
      if (benefit && t.benefits[benefit.id]) {
        const titleElement = item.querySelector('.betroz0001-benefit-title');
        const descElement = item.querySelector('.betroz0001-benefit-description');

        if (titleElement) titleElement.textContent = t.benefits[benefit.id].title;
        if (descElement) descElement.textContent = t.benefits[benefit.id].description;
      }
    });
  }

  // Add styles for both VIP ranking and benefits sections
  if (!document.getElementById('betroz0001-vip-styles')) {
    const style = document.createElement('style');
    style.id = 'betroz0001-vip-styles';
    style.textContent = `
      /* VIP Ranking System Styles */
      .betroz0001vip-ranking-container {
        width: 100%;
        margin: 60px 0;
        position: relative;
        background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
        border-radius: 16px;
        padding: 60px 40px;
        box-sizing: border-box;
        overflow: hidden;
        isolation: isolate;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(24, 207, 251, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      .betroz0001particle-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
        pointer-events: none;
      }

      .betroz0001particle {
        position: absolute;
        width: 3px;
        height: 3px;
        background: #18cffb;
        border-radius: 50%;
        opacity: 0;
        animation: betroz0001particleFloat 10s infinite linear;
      }

      @keyframes betroz0001particleFloat {
        0% { transform: translateY(100vh) translateX(0); opacity: 0; }
        10% { opacity: 0.8; }
        90% { opacity: 0.8; }
        100% { transform: translateY(-100px) translateX(30px); opacity: 0; }
      }

      .betroz0001vip-ranking-title {
        text-align: center;
        color: white;
        font-size: 36px;
        font-weight: 700;
        margin-bottom: 80px;
        text-transform: uppercase;
        letter-spacing: 1.5px;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      }

      .betroz0001vip-ranking-title .betroz0001vip-highlight {
        background: linear-gradient(135deg, #18cffb, #0ac3c6, #18cffb);
        background-size: 200% 100%;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: betroz0001vipShine 3s ease-in-out infinite;
        text-shadow: 0 0 20px rgba(24, 207, 251, 0.5);
      }



      @keyframes betroz0001vipShine {
        0%, 100% { background-position: 200% 0; }
        50% { background-position: -200% 0; }
      }

      .betroz0001diamond-section {
        position: absolute;
        top: 20px;
        right: 40px;
        width: 120px;
        height: 120px;
        z-index: 1;
        opacity: 0.6;
      }

      .betroz0001diamond-container {
        position: relative;
        width: 100%;
        height: 100%;
      }

      .betroz0001diamond-shape {
        position: absolute;
        background: linear-gradient(135deg, rgba(24, 207, 251, 0.2), rgba(10, 195, 198, 0.1));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(24, 207, 251, 0.3);
        transform: rotate(45deg);
        box-shadow: 0 4px 16px rgba(24, 207, 251, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      .betroz0001diamond-shape:nth-child(1) {
        width: 60px;
        height: 60px;
        top: 30px;
        left: 30px;
        animation: betroz0001diamondRotate 20s linear infinite;
      }

      .betroz0001diamond-shape:nth-child(2) {
        width: 40px;
        height: 40px;
        top: 10px;
        left: 10px;
        animation: betroz0001diamondRotate 15s linear infinite reverse;
      }

      .betroz0001diamond-shape:nth-child(3) {
        width: 30px;
        height: 30px;
        bottom: 10px;
        right: 10px;
        animation: betroz0001diamondRotate 25s linear infinite;
      }

      @keyframes betroz0001diamondRotate {
        from { transform: rotate(45deg); }
        to { transform: rotate(405deg); }
      }

      .betroz0001cards-slider {
        display: flex;
        gap: 32px;
        padding: 40px 30px 20px 30px;
        overflow-x: auto;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        scrollbar-color: #18cffb rgba(255, 255, 255, 0.1);
        position: relative;
        z-index: 2;
        cursor: grab;
      }

      .betroz0001cards-slider.active {
        cursor: grabbing;
        user-select: none;
        scroll-behavior: auto !important;
      }

      .betroz0001cards-slider::-webkit-scrollbar {
        height: 10px;
      }

      .betroz0001cards-slider::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        margin: 0 20px;
      }

      .betroz0001cards-slider::-webkit-scrollbar-thumb {
        background: linear-gradient(90deg, #18cffb, #0ac3c6);
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .betroz0001vip-card {
        background: var(--tier-bg);
        border: 2px solid var(--tier-border);
        border-radius: 12px;
        padding: 28px;
        padding-top: 48px;
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        backdrop-filter: blur(15px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.12);
        overflow: visible;
        min-width: 320px;
        flex-shrink: 0;
      }

      .betroz0001vip-card.current-level {
        border-color: #18cffb;
        background: linear-gradient(135deg, rgba(24, 207, 251, 0.12) 0%, var(--tier-bg) 30%);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 3px #18cffb, 0 0 12px rgba(24, 207, 251, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15);
        animation: betroz0001currentLevelGlow 3s ease-in-out infinite alternate;
        transform: scale(1.05);
      }

      @keyframes betroz0001currentLevelGlow {
        0% {
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 3px #18cffb, 0 0 12px rgba(24, 207, 251, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }
        100% {
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 3px #18cffb, 0 0 20px rgba(24, 207, 251, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }
      }

      .betroz0001vip-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.4), 0 0 0 1px var(--tier-color), inset 0 1px 0 rgba(255, 255, 255, 0.15);
        border-color: var(--tier-color);
      }

      .betroz0001vip-card.current-level:hover {
        transform: scale(1.05) translateY(-6px);
      }

      .betroz0001progress-circle {
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        transition: all 0.3s ease;
      }

      .betroz0001progress-circle.achieved {
        background: linear-gradient(135deg, #18cffb, #0ac3c6);
        border: 3px solid #0891b2;
        box-shadow: 0 4px 12px rgba(24, 207, 251, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.1);
      }

      .betroz0001progress-circle.locked {
        background: linear-gradient(135deg, #64748b, #475569);
        border: 3px solid #334155;
        box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3), 0 0 0 2px rgba(255, 255, 255, 0.1);
      }

      .betroz0001progress-icon {
        color: white;
        font-size: 18px;
        font-weight: 700;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      .betroz0001vip-card:hover .betroz0001progress-circle {
        transform: translateX(-50%) scale(1.1);
      }

      .betroz0001vip-card.current-level .betroz0001progress-circle {
        background: linear-gradient(135deg, #18cffb, #0ac3c6);
        border: 3px solid #18cffb;
        box-shadow: 0 4px 12px rgba(24, 207, 251, 0.4), 0 0 8px rgba(24, 207, 251, 0.3), 0 0 0 2px rgba(255, 255, 255, 0.2);
        animation: betroz0001currentCircleGlow 3s ease-in-out infinite alternate;
        transform: translateX(-50%) scale(1.15);
      }

      @keyframes betroz0001currentCircleGlow {
        0% {
          box-shadow: 0 4px 12px rgba(24, 207, 251, 0.4), 0 0 8px rgba(24, 207, 251, 0.3), 0 0 0 2px rgba(255, 255, 255, 0.2);
        }
        100% {
          box-shadow: 0 4px 12px rgba(24, 207, 251, 0.5), 0 0 12px rgba(24, 207, 251, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.25);
        }
      }

      .betroz0001card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 24px;
        position: relative;
        z-index: 2;
      }

      .betroz0001tier-badge {
        background: linear-gradient(135deg, var(--tier-color), rgba(255, 255, 255, 0.9));
        color: #000;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 700;
        font-size: 15px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
      }

      .betroz0001wager-amount {
        text-align: right;
        color: white;
        position: relative;
        z-index: 2;
      }

      .betroz0001wager-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--tier-color);
        margin-bottom: 6px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .betroz0001wager-label {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.8);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
      }

      .betroz0001tier-benefits-list {
        display: flex;
        flex-direction: column;
        gap: 14px;
        position: relative;
        z-index: 2;
      }

      .betroz0001tier-benefit-item {
        display: flex;
        align-items: center;
        gap: 14px;
        color: white;
        font-size: 15px;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .betroz0001tier-benefit-check {
        width: 24px;
        height: 24px;
        background: linear-gradient(135deg, var(--tier-color), rgba(255, 255, 255, 0.9));
        color: #000;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 13px;
        flex-shrink: 0;
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
      }

      /* Integrated VIP benefits section */
      .betroz0001-vip-benefits-section {
        width: 100%;
        margin: 60px 0 0 0;
        padding: 40px 20px 0 20px;
        position: relative;
        z-index: 2;
      }

      /* Section title */
      .betroz0001-vip-title {
        font-size: 32px;
        font-weight: 700;
        color: #ffffff;
        text-align: center;
        margin: 0 0 40px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      /* Benefits grid */
      .betroz0001-benefits-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        max-width: 1000px;
        margin: 0 auto;
      }

      /* Individual benefit item */
      .betroz0001-benefit-item {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(255, 132, 38, 0.2);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }

      .betroz0001-benefit-item:hover {
        background: rgba(255, 132, 38, 0.1);
        border-color: rgba(255, 132, 38, 0.4);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      }

      /* Benefit icon */
      .betroz0001-benefit-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #ff8426, #ff6a00);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        box-shadow: 0 4px 12px rgba(255, 132, 38, 0.3);
      }

      .betroz0001-benefit-icon svg {
        width: 24px;
        height: 24px;
        fill: white;
      }

      /* Benefit content */
      .betroz0001-benefit-content {
        flex: 1;
      }

      .betroz0001-benefit-title {
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        margin: 0 0 8px 0;
      }

      .betroz0001-benefit-description {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.5;
        margin: 0;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .betroz0001-vip-benefits-section {
          margin: 40px 0 0 0;
          padding: 30px 16px 0 16px;
        }

        .betroz0001-vip-title {
          font-size: 28px;
          margin-bottom: 30px;
        }

        .betroz0001-benefits-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .betroz0001-benefit-item {
          padding: 16px;
        }

        .betroz0001-benefit-icon {
          width: 40px;
          height: 40px;
        }

        .betroz0001-benefit-icon svg {
          width: 20px;
          height: 20px;
        }

        .betroz0001-benefit-title {
          font-size: 16px;
        }

        .betroz0001-benefit-description {
          font-size: 13px;
        }
      }

      @media (max-width: 480px) {
        .betroz0001-vip-title {
          font-size: 24px;
        }

        .betroz0001-benefit-item {
          gap: 12px;
          padding: 14px;
        }
      }
    `;
    document.head.appendChild(style);
  }

  const render = async (target) => {
    const wrapper = target.parentElement.children[target.parentElement.children.length - 2].firstElementChild
    wrapper.firstElementChild.className = ''
    wrapper.firstElementChild.style.display = 'none'

    await betroz0001addStyledContainer(wrapper)
  }

  const onLanguageChange = (language) => {
    console.dir(language)
    betroz0001updateLanguageContent();
  }

  return {
    render,
    onLanguageChange
  }
}
