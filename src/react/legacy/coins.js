export const renderCoins = (renderAfterId) => {
  // Switch to enable/disable this script
  const custom0007active = true;

  if (custom0007active) {
    // Flag to track if container has been rendered
    let containerRendered = false;

    // Insertional script: finds main-slider-swiper by ID and inserts content after it
    function renderBackgroundContainer() {
      // Check if container already exists and is properly positioned
      const existingContainer = document.getElementById("background-container-wrapper");
      if (existingContainer && existingContainer.parentNode && containerRendered) {
        // Container exists and is properly attached, no need to re-render
        return;
      }

      // Determine the page language, defaulting to English if not explicitly set
      const lang = document.documentElement.lang || "en";

      // Find the main slider element
      const mainSlider = document.getElementById(renderAfterId);
      if (!mainSlider) {
        return;
      }

      // Remove any existing background container only if it's not properly positioned
      if (existingContainer) {
        // Clean up particle animation if it exists
        cleanupParticleAnimation();
        // Remove the container
        existingContainer.remove();
        containerRendered = false;
      }

      // Create the wrapper element for the background container
      const wrapper = document.createElement("div");
      wrapper.id = "background-container-wrapper";

      // Insert directly after the main slider
      mainSlider.after(wrapper);

      // Translations for different languages
      const custom0007translations = {
        en: {
          mainTitle: "Experience the Future",
          subTitle: "of Digital Gaming",
          feature1: "Instant Transactions",
          feature2: "24/7 Support",
          feature3: "Secure Platform",
          ctaButton: "Start Playing"
        },
        tr: {
          mainTitle: "Dijital Oyunun",
          subTitle: "Geleceğini Yaşayın",
          feature1: "Anında İşlemler",
          feature2: "7/24 Destek",
          feature3: "Güvenli Platform",
          ctaButton: "Oynamaya Başla"
        }
      };

      // Function to get current language
      function custom0007getCurrentLanguage() {
        return document.documentElement.lang || "en";
      }

      // Function to get translations for current language
      function custom0007getTranslations() {
        const lang = custom0007getCurrentLanguage();
        return custom0007translations[lang] || custom0007translations.en;
      }

      // Get the appropriate translations
      const t = custom0007getTranslations();

      // Inject the HTML structure and inline styles for the background container
      wrapper.innerHTML = `
        <style>
          /* Hero banner container - completely different approach */
          .custom0007-hero-banner {
            width: 100%;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
            border-radius: 20px;
            min-height: 280px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: center;
            gap: 40px;
            padding: 40px;
            box-sizing: border-box;
            isolation: isolate;
          }

          /* Animated particle background */
          .custom0007-particle-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
            pointer-events: none;
          }

          .custom0007-particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #18cffb;
            border-radius: 50%;
            opacity: 0;
            animation: custom0007float 8s infinite linear;
          }

          @keyframes custom0007float {
            0% {
              transform: translateY(100vh) translateX(0);
              opacity: 0;
            }
            10% {
              opacity: 1;
            }
            90% {
              opacity: 1;
            }
            100% {
              transform: translateY(-100px) translateX(50px);
              opacity: 0;
            }
          }

          /* Content area - left side */
          .custom0007-content-area {
            position: relative;
            z-index: 2;
            color: white;
          }

          /* Main title styling */
          .custom0007-main-title {
            font-size: 42px;
            font-weight: 900;
            margin: 0 0 8px 0;
            background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
            text-shadow: 0 0 30px rgba(24, 207, 251, 0.3);
          }

          .custom0007-sub-title {
            font-size: 42px;
            font-weight: 900;
            margin: 0 0 24px 0;
            color: #ffffff;
            line-height: 1.1;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
          }

          /* Feature list styling */
          .custom0007-features {
            list-style: none;
            padding: 0;
            margin: 0 0 32px 0;
          }

          .custom0007-feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 16px;
            color: #ffffff;
            opacity: 0.9;
          }

          .custom0007-feature-icon {
            width: 20px;
            height: 20px;
            background: #18cffb;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 0 10px rgba(24, 207, 251, 0.4);
          }

          .custom0007-feature-icon::after {
            content: '✓';
            color: #061d2b;
            font-size: 12px;
            font-weight: bold;
          }

          /* CTA Button styling - completely different design */
          .custom0007-cta-button {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
            color: #061d2b;
            font-weight: 800;
            padding: 16px 32px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 18px;
            box-shadow:
              0 8px 25px rgba(24, 207, 251, 0.4),
              0 0 0 1px rgba(255, 255, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
          }

          .custom0007-cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
          }

          .custom0007-cta-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow:
              0 12px 35px rgba(24, 207, 251, 0.6),
              0 0 0 1px rgba(255, 255, 255, 0.2);
          }

          .custom0007-cta-button:hover::before {
            left: 100%;
          }

          .custom0007-cta-arrow {
            font-size: 16px;
            transition: transform 0.3s ease;
          }

          .custom0007-cta-button:hover .custom0007-cta-arrow {
            transform: translateX(4px);
          }

          /* Visual area - right side with geometric shapes */
          .custom0007-visual-area {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
          }

          .custom0007-geometric-container {
            position: relative;
            width: 320px;
            height: 320px;
          }

          .custom0007-shape {
            position: absolute;
            border-radius: 25px;
            background: linear-gradient(135deg, rgba(24, 207, 251, 0.15), rgba(10, 195, 198, 0.08));
            backdrop-filter: blur(15px);
            border: 2px solid rgba(24, 207, 251, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            box-shadow:
              0 8px 32px rgba(24, 207, 251, 0.2),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
          }

          .custom0007-shape img {
            width: 70%;
            height: 70%;
            object-fit: contain;
            filter: hue-rotate(170deg) drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
            transition: all 0.3s ease;
            animation: custom0007coinFloat 3s ease-in-out infinite;
          }

          .custom0007-shape:hover img {
            transform: scale(1.1);
            filter: hue-rotate(170deg) drop-shadow(0 6px 16px rgba(24, 207, 251, 0.4));
          }

          .custom0007-shape:nth-child(1) img {
            animation: custom0007coinCounterRotate1 20s ease-in-out infinite;
            animation-delay: 0s;
          }

          .custom0007-shape:nth-child(2) img {
            animation: custom0007coinCounterRotate2 15s ease-in-out infinite;
            animation-delay: 1s;
          }

          .custom0007-shape:nth-child(3) img {
            animation: custom0007coinCounterRotate3 25s ease-in-out infinite;
            animation-delay: 2s;
          }

          @keyframes custom0007coinCounterRotate1 {
            0% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-4px) rotate(-90deg); }
            50% { transform: translateY(-8px) rotate(-180deg); }
            75% { transform: translateY(-4px) rotate(-270deg); }
            100% { transform: translateY(0px) rotate(-360deg); }
          }

          @keyframes custom0007coinCounterRotate2 {
            0% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-4px) rotate(90deg); }
            50% { transform: translateY(-8px) rotate(180deg); }
            75% { transform: translateY(-4px) rotate(270deg); }
            100% { transform: translateY(0px) rotate(360deg); }
          }

          @keyframes custom0007coinCounterRotate3 {
            0% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-4px) rotate(-90deg); }
            50% { transform: translateY(-8px) rotate(-180deg); }
            75% { transform: translateY(-4px) rotate(-270deg); }
            100% { transform: translateY(0px) rotate(-360deg); }
          }

          .custom0007-shape:nth-child(1) {
            width: 160px;
            height: 160px;
            top: 0;
            left: 0;
            animation: custom0007rotate 20s linear infinite;
          }

          .custom0007-shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 80px;
            right: 0;
            animation: custom0007rotate 15s linear infinite reverse;
          }

          .custom0007-shape:nth-child(3) {
            width: 100px;
            height: 100px;
            bottom: 0;
            left: 60px;
            animation: custom0007rotate 25s linear infinite;
          }

          @keyframes custom0007rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }

          /* Responsive design */
          @media (max-width: 1024px) {
            .custom0007-hero-banner {
              grid-template-columns: 1fr;
              text-align: center;
              gap: 30px;
              padding: 30px;
              min-height: 450px;
            }

            .custom0007-main-title,
            .custom0007-sub-title {
              font-size: 36px;
            }

            .custom0007-geometric-container {
              width: 250px;
              height: 250px;
            }

            .custom0007-shape:nth-child(1) {
              width: 120px;
              height: 120px;
            }

            .custom0007-shape:nth-child(2) {
              width: 90px;
              height: 90px;
              top: 60px;
            }

            .custom0007-shape:nth-child(3) {
              width: 75px;
              height: 75px;
              left: 45px;
            }
          }

          @media (max-width: 768px) {
            .custom0007-hero-banner {
              margin: 20px 0;
              padding: 24px;
              border-radius: 16px;
              min-height: 400px;
            }

            .custom0007-main-title,
            .custom0007-sub-title {
              font-size: 28px;
            }

            .custom0007-feature-item {
              font-size: 14px;
            }

            .custom0007-cta-button {
              padding: 14px 28px;
              font-size: 16px;
            }

            .custom0007-geometric-container {
              width: 200px;
              height: 200px;
            }

            .custom0007-shape:nth-child(1) {
              width: 100px;
              height: 100px;
            }

            .custom0007-shape:nth-child(2) {
              width: 75px;
              height: 75px;
              top: 50px;
            }

            .custom0007-shape:nth-child(3) {
              width: 60px;
              height: 60px;
              left: 35px;
            }
          }

          @media (max-width: 480px) {
            .custom0007-hero-banner {
              padding: 20px;
              margin: 16px 0;
              min-height: 350px;
            }

            .custom0007-main-title,
            .custom0007-sub-title {
              font-size: 24px;
            }

            .custom0007-cta-button {
              padding: 12px 24px;
              font-size: 14px;
            }

            .custom0007-geometric-container {
              width: 160px;
              height: 160px;
            }

            .custom0007-shape:nth-child(1) {
              width: 80px;
              height: 80px;
            }

            .custom0007-shape:nth-child(2) {
              width: 60px;
              height: 60px;
              top: 40px;
            }

            .custom0007-shape:nth-child(3) {
              width: 50px;
              height: 50px;
              left: 30px;
            }
          }

        </style>

        <!-- Hero Banner Container -->
        <div class="custom0007-hero-banner">
          <!-- Animated particle background -->
          <div class="custom0007-particle-container" id="custom0007-particle-container">
            <!-- Particles will be dynamically generated here -->
          </div>

          <!-- Content Area -->
          <div class="custom0007-content-area">
            <h1 class="custom0007-main-title">${t.mainTitle}</h1>
            <h2 class="custom0007-sub-title">${t.subTitle}</h2>

            <ul class="custom0007-features">
              <li class="custom0007-feature-item">
                <div class="custom0007-feature-icon"></div>
                ${t.feature1}
              </li>
              <li class="custom0007-feature-item">
                <div class="custom0007-feature-icon"></div>
                ${t.feature2}
              </li>
              <li class="custom0007-feature-item">
                <div class="custom0007-feature-icon"></div>
                ${t.feature3}
              </li>
            </ul>

            <!-- <a href="javascript:void(0);" class="custom0007-cta-button">${t.ctaButton}</a> -->
          </div>

          <!-- Visual Area -->
          <div class="custom0007-visual-area">
            <div class="custom0007-geometric-container">
              <div class="custom0007-shape">
                <img src="https://i.ibb.co/39dFz5jP/1.png" alt="Bitcoin" loading="lazy">
              </div>
              <div class="custom0007-shape">
                <img src="https://i.ibb.co/nNDDVDbb/2.png" alt="Ethereum" loading="lazy">
              </div>
              <div class="custom0007-shape">
                <img src="https://i.ibb.co/TxH58gvC/3.png" alt="Litecoin" loading="lazy">
              </div>
            </div>
          </div>
        </div>
      `;

      // Add click event handler to the CTA button
      setTimeout(() => {
        const ctaButton = document.querySelector('.custom0007-cta-button');
        if (ctaButton) {
          ctaButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Check if wallet button exists
            const walletBtnExists = document.querySelector('a.header__wallet-btn');

            // Get current URL
            let currentUrl = window.location.href;

            // Remove any existing modal parameter
            currentUrl = currentUrl.replace(/[?&]modal=[^&]+/, '');

            // Add the appropriate modal parameter
            const modalParam = walletBtnExists ? 'wallet' : 'register';
            const separator = currentUrl.includes('?') ? '&' : '?';

            // Navigate to the new URL
            window.location.href = `${currentUrl}${separator}modal=${modalParam}`;
          });
        }
      }, 100);

      // Initialize particle animation
      initParticleAnimation();

      // Mark container as successfully rendered
      containerRendered = true;
    }

    // Function to update text content for language changes without re-rendering
    function updateLanguageContent() {
      const existingContainer = document.getElementById("background-container-wrapper");
      if (!existingContainer || !containerRendered) {
        return;
      }

      // Get current language
      const lang = document.documentElement.lang || "en";

      // Translations for different languages
      const custom0007translations = {
        en: {
          mainTitle: "Experience the Future",
          subTitle: "of Digital Gaming",
          feature1: "Instant Transactions",
          feature2: "24/7 Support",
          feature3: "Secure Platform",
          ctaButton: "Start Playing"
        },
        tr: {
          mainTitle: "Dijital Oyunun",
          subTitle: "Geleceğini Yaşayın",
          feature1: "Anında İşlemler",
          feature2: "7/24 Destek",
          feature3: "Güvenli Platform",
          ctaButton: "Oynamaya Başla"
        }
      };

      // Get the appropriate translations
      const t = custom0007translations[lang] || custom0007translations.en;

      // Update text content without re-rendering the entire container
      const mainTitle = existingContainer.querySelector('.custom0007-main-title');
      const subTitle = existingContainer.querySelector('.custom0007-sub-title');
      const features = existingContainer.querySelectorAll('.custom0007-feature-item');
      const ctaButton = existingContainer.querySelector('.custom0007-cta-button');

      if (mainTitle) mainTitle.textContent = t.mainTitle;
      if (subTitle) subTitle.textContent = t.subTitle;

      if (features.length >= 3) {
        // Update feature text (preserve the icon div)
        const featureTexts = [t.feature1, t.feature2, t.feature3];
        features.forEach((feature, index) => {
          if (featureTexts[index]) {
            feature.innerHTML = `<div class="custom0007-feature-icon"></div>${featureTexts[index]}`;
          }
        });
      }

      if (ctaButton) ctaButton.textContent = t.ctaButton;
    }

    // Set up language change detection
    const observer = new MutationObserver(() => {
      const existingContainer = document.getElementById("background-container-wrapper");

      if (existingContainer && existingContainer.parentNode && containerRendered) {
        // Container exists, just update the language content
        updateLanguageContent();
        return;
      }

      // Container doesn't exist, render it
      renderBackgroundContainer();
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['lang']
    });

    // Particle animation system
    function initParticleAnimation() {
      const particleContainer = document.getElementById('custom0007-particle-container');
      if (!particleContainer) return;

      function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'custom0007-particle';

        // Random horizontal position
        particle.style.left = Math.random() * 100 + '%';

        // Random animation delay
        particle.style.animationDelay = Math.random() * 8 + 's';

        particleContainer.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
          if (particle.parentNode) {
            particle.remove();
          }
        }, 8000);
      }

      // Create initial particles
      for (let i = 0; i < 15; i++) {
        setTimeout(() => createParticle(), i * 500);
      }

      // Continue creating particles
      setInterval(createParticle, 800);
    }

    // Cleanup function for particle animation
    function cleanupParticleAnimation() {
      const particleContainer = document.getElementById('custom0007-particle-container');
      if (particleContainer) {
        particleContainer.innerHTML = '';
      }
    }

    // Check if the main slider exists and render background container immediately
    if (document.getElementById(renderAfterId)) {
      // Only render if container doesn't already exist
      const existingContainer = document.getElementById("background-container-wrapper");
      if (!existingContainer || !containerRendered) {
        renderBackgroundContainer();
      }
    } else {
      // Use MutationObserver to detect when the slider is added to the DOM
      const bodyObserver = new MutationObserver((mutations) => {
        // Check if container already exists before processing mutations
        const existingContainer = document.getElementById("background-container-wrapper");
        if (existingContainer && existingContainer.parentNode && containerRendered) {
          // Container exists and is properly attached, disconnect observer
          bodyObserver.disconnect();
          return;
        }

        for (const mutation of mutations) {
          if (mutation.type === 'childList' && mutation.addedNodes.length) {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                if (node.id === renderAfterId || node.querySelector(`#${renderAfterId}`)) {
                  renderBackgroundContainer();
                  bodyObserver.disconnect();
                  return;
                }
              }
            }
          }
        }
      });

      bodyObserver.observe(document.body, {
        childList: true,
        subtree: true
      });
    }
  }
}
