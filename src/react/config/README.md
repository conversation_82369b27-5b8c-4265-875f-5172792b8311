# Environment Configuration

This directory contains environment configuration files for easy switching between different deployment environments.

## Quick Start

To switch between environments, simply change the `ENVIRONMENT_HOST` constant in `environment.js`:

```javascript
// For local development
export const ENVIRONMENT_HOST = 'http://localhost:3000';

// For production
export const ENVIRONMENT_HOST = 'https://gateway.makroz.org';

// For staging (example)
export const ENVIRONMENT_HOST = 'https://staging.makroz.org';
```

## Files

### `environment.js`
Main configuration file containing:
- `ENVIRONMENT_HOST`: The base URL for API endpoints
- `API_ENDPOINTS`: Object containing all API endpoint URLs
- Helper functions for environment detection
- Automatic environment logging

## Usage

Import the configuration in your API files:

```javascript
import { API_ENDPOINTS } from '../config/environment.js';

// Use the endpoints
const response = await fetch(API_ENDPOINTS.MISSIONS, {
  method: 'GET',
  headers: { /* ... */ }
});
```

## Benefits

1. **Single Point of Change**: Change one constant to switch all endpoints
2. **Environment Detection**: Built-in helpers to detect current environment
3. **Type Safety**: Centralized endpoint definitions prevent typos
4. **Debugging**: Automatic environment logging on import
5. **Maintainability**: Easy to add new endpoints or environments

## Supported Endpoints

- `EXTENDED_USERS_ME`: Create/get extended user
- `MISSIONS`: Get user missions
- `MISSION_STATS`: Get mission statistics
- `MISSION_PARTICIPATE(missionId)`: Participate in a mission
- `MISSION_COMPLETE(missionId)`: Complete/claim a mission

## Environment Detection

```javascript
import { getEnvironmentInfo, isLocalDevelopment } from '../config/environment.js';

if (isLocalDevelopment()) {
  console.log('Running in local development mode');
}

console.log('Environment info:', getEnvironmentInfo());
```
