/**
 * Environment Configuration
 * 
 * Change ENVIRONMENT_HOST to switch between environments:
 * - Production: 'https://gateway.makroz.org'
 * - Local Development: 'http://localhost:3000'
 * - Staging: 'https://staging.makroz.org' (example)
 */

// Main environment host - change this single constant to switch environments
export const ENVIRONMENT_HOST = 'https://gateway.makroz.org';
// export const ENVIRONMENT_HOST = 'http://127.0.0.1:3000';

// API endpoints configuration
export const API_ENDPOINTS = {
  // Missions API endpoints
  EXTENDED_USERS_ME: `${ENVIRONMENT_HOST}/api/makroz/extended-users/me`,
  MISSIONS: `${ENVIRONMENT_HOST}/api/makroz/extended-users/me/missions`,
  MISSION_STATS: `${ENVIRONMENT_HOST}/api/makroz/extended-users/me/missions/stats`,
  MISSION_PARTICIPATE: (missionId) => `${ENVIRONMENT_HOST}/api/makroz/missions/${missionId}/participations`,
  MISSION_COMPLETE: (missionId) => `${ENVIRONMENT_HOST}/api/makroz/missions/${missionId}/complete`,
  MISSION_RESET_TIMES: `${ENVIRONMENT_HOST}/api/makroz/missions/reset-times`,

  // Final Mission Claims API endpoints
  FINAL_MISSION_CLAIMS: `${ENVIRONMENT_HOST}/api/v1/makroz/final-mission-claims`,
  FINAL_MISSION_CLAIMS_ME: `${ENVIRONMENT_HOST}/api/v1/makroz/final-mission-claims/me`,
};

// Environment detection helpers
export const isProduction = () => ENVIRONMENT_HOST.includes('gateway.makroz.org');
export const isLocalDevelopment = () => ENVIRONMENT_HOST.includes('localhost');
export const isStaging = () => ENVIRONMENT_HOST.includes('staging');

// Environment info for debugging
export const getEnvironmentInfo = () => ({
  host: ENVIRONMENT_HOST,
  isProduction: isProduction(),
  isLocalDevelopment: isLocalDevelopment(),
  isStaging: isStaging(),
});

// Log current environment on import (for debugging)
console.log('🌍 Environment Configuration:', getEnvironmentInfo());
