/* Custom Select Component Styles */
.customSelect {
  position: relative;
  width: 100%;
  font-family: inherit;
}

.selectTrigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* width: 100%; */
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(12px);
  position: relative;
  overflow: hidden;
}

.selectTrigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1) 0%, rgba(10, 195, 198, 0.1) 50%, rgba(30, 64, 175, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.selectTrigger:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 207, 251, 0.2);
}

.selectTrigger:hover::before {
  opacity: 1;
}

.selectTrigger:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: #18cffb;
  box-shadow: 0 0 0 3px rgba(24, 207, 251, 0.2);
}

.selectTrigger.open {
  background: rgba(255, 255, 255, 0.2);
  border-color: #18cffb;
  box-shadow: 0 0 0 3px rgba(24, 207, 251, 0.2);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.selectTrigger.open::before {
  opacity: 1;
}

.selectedValue {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  transition: transform 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.arrowUp {
  transform: rotate(180deg);
}

.optionsContainer {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(26, 26, 26, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-top: none;
  border-radius: 0 0 12px 12px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideDown 0.2s ease-out;
  overflow: hidden;
}

.optionsList {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 300px;
  overflow-y: auto;
}

.optionsList::-webkit-scrollbar {
  width: 6px;
}

.optionsList::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.optionsList::-webkit-scrollbar-thumb {
  background: rgba(24, 207, 251, 0.5);
  border-radius: 3px;
}

.optionsList::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 207, 251, 0.7);
}

.option {
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(24, 207, 251, 0.1), transparent);
  transition: left 0.3s ease;
}

.option:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.option:hover::before {
  left: 100%;
}

.option.highlighted {
  background: rgba(24, 207, 251, 0.2);
  color: white;
}

.option.selected {
  background: rgba(24, 207, 251, 0.3);
  color: white;
  font-weight: 600;
}

.option.selected::after {
  content: '✓';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #18cffb;
  font-weight: bold;
}

.option:last-child {
  border-bottom: none;
}

/* Disabled state */
.disabled .selectTrigger {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.disabled .selectTrigger:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  transform: none;
  box-shadow: none;
}

.disabled .selectTrigger::before {
  display: none;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .selectTrigger {
    padding: 14px 16px;
    font-size: 1rem;
  }
  
  .option {
    padding: 14px 16px;
    font-size: 1rem;
  }
  
  .optionsList {
    max-height: 180px;
  }
}

@media (max-width: 480px) {
  .selectTrigger {
    padding: 16px;
    font-size: 1rem;
  }
  
  .option {
    padding: 16px;
    font-size: 1rem;
  }
  
  .optionsList {
    max-height: 160px;
  }
}
