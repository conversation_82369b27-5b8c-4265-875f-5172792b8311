import { useState, useEffect } from 'react'
import { FaGoogle, FaFacebook, FaTelegram, FaTwitch, FaCrown, FaStar } from 'react-icons/fa'
import { Button } from './Button'
import { SocialMediaButton } from './SocialMediaButton'
import { MainCarousel } from '../MainCarousel/MainCarousel'
import { VIPProgressCard } from './VIPProgressCard'
import { QuickStatsCard } from './QuickStatsCard'
import { LastPlayedGameCard } from './LastPlayedGameCard'
import { getUser, getUserVipState, getUserStats, getLastPlayedGames } from '../../api/user'
import styles from './Hero.module.css'

export const i18n = {
  en: {
    title: 'Unparalleled online casino and sports betting',
    sign_up: 'Sign Up',
    or_continue_with: 'or continue with...',
    vip_status: 'VIP Status',
    current_level: 'Current Level',
    next_level: 'Next Level',
    progress_to_next: 'Progress to Next Level',
    total_wagered: 'Total Wagered',
    total_wins: 'Total Wins',
    total_bets: 'Total Bets',
    goal: 'Goal',
    benefits: 'VIP Benefits',
    exclusive_bonuses: 'Exclusive Bonuses',
    priority_support: 'Priority Support',
    higher_limits: 'Higher Limits',
    quick_stats: 'Quick Stats',
    balance: 'Balance',
    games_played: 'Games Played',
    win_rate: 'Win Rate',
    recent_activity: 'Recent Activity',
    last_game: 'Last Game',
    big_win: 'Big Win',
    bonus_claimed: 'Bonus Claimed',
    achievements: 'Achievements',
    first_deposit: 'First Deposit',
    high_roller: 'High Roller',
    lucky_streak: 'Lucky Streak',
    view_all: 'View All',
    loading: 'Loading...'
  },
  tr: {
    title: 'Eşi benzeri olmayan online casino ve spor bahisleri',
    sign_up: 'Kaydol',
    or_continue_with: 'veya bununla devam et...',
    vip_status: 'VIP Durumu',
    current_level: 'Mevcut Seviye',
    next_level: 'Sonraki Seviye',
    progress_to_next: 'Sonraki Seviyeye İlerleme',
    total_wagered: 'Toplam Bahis',
    total_wins: 'Toplam Kazanç',
    total_bets: 'Toplam Bahis',
    goal: 'Hedef',
    benefits: 'VIP Avantajları',
    exclusive_bonuses: 'Özel Bonuslar',
    priority_support: 'Öncelikli Destek',
    higher_limits: 'Yüksek Limitler',
    quick_stats: 'Hızlı İstatistikler',
    balance: 'Bakiye',
    games_played: 'Oynanan Oyunlar',
    win_rate: 'Kazanma Oranı',
    recent_activity: 'Son Aktiviteler',
    last_game: 'Son Oyun',
    big_win: 'Büyük Kazanç',
    bonus_claimed: 'Bonus Alındı',
    achievements: 'Başarımlar',
    first_deposit: 'İlk Yatırım',
    high_roller: 'Yüksek Bahisçi',
    lucky_streak: 'Şanslı Seri',
    view_all: 'Tümünü Gör',
    loading: 'Yükleniyor...'
  }
}







export const Hero = ({ i18n, isAuthenticated, language }) => {
  // State for user data and VIP information
  const [userData, setUserData] = useState(null)
  const [vipData, setVipData] = useState(null)
  const [statsData, setStatsData] = useState(null)
  const [lastPlayedGame, setLastPlayedGame] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  // Fetch user data and VIP state when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchUserData()
    }
  }, [isAuthenticated])

  const fetchUserData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // First, get user data to obtain the nickname
      const user = await getUser()
      if (!user) {
        throw new Error('Failed to fetch user data')
      }

      setUserData(user)

      // Then, get VIP state using the nickname
      const vipState = await getUserVipState(user.username)
      if (vipState) {
        // Helper function to get level icon based on level name
        const getLevelIcon = (levelName) => {
          const level = levelName?.toLowerCase() || ''
          if (level.includes('bronze') || level.includes('copper')) {
            return <FaStar size={24} color="#18cffb" />
          } else if (level.includes('silver')) {
            return <FaStar size={24} color="#18cffb" />
          } else if (level.includes('gold')) {
            return <FaCrown size={24} color="#18cffb" />
          } else if (level.includes('platinum') || level.includes('diamond')) {
            return <FaCrown size={24} color="#18cffb" />
          } else if (level.includes('vip') || level.includes('premium')) {
            return <FaCrown size={24} color="#18cffb" />
          } else {
            return <FaStar size={24} color="#18cffb" />
          }
        }

        // Transform API data to component format
        const transformedVipData = {
          currentLevel: vipState.current?.name || 'Unknown',
          currentLevelIcon: getLevelIcon(vipState.current?.name),
          nextLevel: vipState.next?.name || 'Max Level',
          nextLevelIcon: getLevelIcon(vipState.next?.name),
          currentProgress: parseFloat(vipState.percentage || '0'),
          totalWagered: `$${parseFloat(user.turnover || '0').toFixed(2)}`,
          nextLevelRequirement: vipState.next?.wager_limit ?
            `$${parseFloat(vipState.next.wager_limit).toLocaleString()}` : 'Max Level',
          benefits: [
            'exclusive_bonuses',
            'priority_support',
            'higher_limits'
          ]
        }
        setVipData(transformedVipData)
      }

      // Fetch user stats
      const stats = await getUserStats(user.username)
      if (stats) {
        setStatsData(stats)
      }

      // Fetch last played games
      const lastPlayedGames = await getLastPlayedGames()
      if (lastPlayedGames && lastPlayedGames.length > 0) {
        setLastPlayedGame(lastPlayedGames[0]) // Get the first (most recent) game
      }
    } catch (err) {
      console.error('Error fetching user data:', err)
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  // Default slides data
  const slides = [
    `https://d11caojl6op1au.cloudfront.net/img/main-slider/BETROZ-SLOT-GAMES-${language.toUpperCase()}.png`,
    `https://d11caojl6op1au.cloudfront.net/img/main-slider/BETROZ-LIVE-CASINO-${language.toUpperCase()}.png`,
    `https://d11caojl6op1au.cloudfront.net/img/main-slider/BETROZ-SPORT-BETS-${language.toUpperCase()}.png`,
  ]

  const openSignUp = () => {
    document.querySelector('button.header__signup').click()
  }

  return (
    <div className={styles.heroContainer}>
      <div className={isAuthenticated ? styles.authenticatedGrid : styles.heroGrid}>
        {isAuthenticated ? (
          <>
            <div className={styles.vipSection}>
              <VIPProgressCard i18n={i18n} vipData={vipData} isLoading={isLoading} error={error} />
            </div>
            <div className={styles.statsSection}>
              <QuickStatsCard i18n={i18n} statsData={statsData} isLoading={isLoading} />
              <LastPlayedGameCard i18n={i18n} lastPlayedGame={lastPlayedGame} isLoading={isLoading} />
            </div>
          </>
        ) : (
          <>
            <div className={styles.heroContent}>
              <h1 className={styles.heroTitle}>
                {i18n['title']}
              </h1>
              <div className={styles.heroActions}>
                <Button style={{ width: 100 }} label={i18n['sign_up']} onClick={openSignUp} />
                <p className={styles.heroText}>{i18n['or_continue_with']}</p>
                <div className={styles.socialButtons}>
                  <SocialMediaButton icon={<FaGoogle size={20} />} onClick={openSignUp} />
                  <SocialMediaButton icon={<FaFacebook size={20} />} onClick={openSignUp} />
                  <SocialMediaButton icon={<FaTelegram size={20} />} onClick={openSignUp} />
                  <SocialMediaButton icon={<FaTwitch size={20} />} onClick={openSignUp} />
                </div>
              </div>
            </div>
            <div className={styles.carouselWrapper}>
              <MainCarousel slides={slides} />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default Hero
