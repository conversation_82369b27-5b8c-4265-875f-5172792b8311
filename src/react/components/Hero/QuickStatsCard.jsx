import { FaChartLine, FaGamepad, FaTrophy, FaFire } from 'react-icons/fa'
import styles from './QuickStatsCard.module.css'

export const QuickStatsCard = ({ i18n, statsData, isLoading }) => {
  // Default data for loading state
  const defaultStats = {
    totalBets: isLoading ? (i18n['loading'] || 'Loading...') : '0',
    totalWins: isLoading ? (i18n['loading'] || 'Loading...') : '0',
    winRate: isLoading ? (i18n['loading'] || 'Loading...') : '0%'
  }

  const displayStats = (isLoading || !statsData) ? defaultStats : {
    totalBets: statsData.total_bet_count.toLocaleString(),
    totalWins: statsData.total_win_count.toLocaleString(),
    winRate: statsData.total_bet_count > 0 ? 
      `${((statsData.total_win_count / statsData.total_bet_count) * 100).toFixed(1)}%` : '0%'
  }

  return (
    <div className={styles.statsCard}>
      <div className={styles.cardHeader}>
        <FaChartLine size={16} color="#18cffb" />
        <h3 className={styles.cardTitle}>{i18n['quick_stats']}</h3>
      </div>
      <div className={styles.statsGrid}>
        <div className={styles.statItem}>
          <FaGamepad size={14} color="#18cffb" />
          <div className={styles.statInfo}>
            <span className={styles.statLabel}>{i18n['total_bets']}</span>
            <span className={styles.statValue}>{displayStats.totalBets}</span>
          </div>
        </div>
        <div className={styles.statItem}>
          <FaTrophy size={14} color="#18cffb" />
          <div className={styles.statInfo}>
            <span className={styles.statLabel}>{i18n['total_wins']}</span>
            <span className={styles.statValue}>{displayStats.totalWins}</span>
          </div>
        </div>
        <div className={styles.statItem}>
          <FaFire size={14} color="#18cffb" />
          <div className={styles.statInfo}>
            <span className={styles.statLabel}>{i18n['win_rate']}</span>
            <span className={styles.statValue}>{displayStats.winRate}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export const Stylesheet = `
  /* Quick Stats Card styles are in QuickStatsCard.module.css */
`
