/* Button Component Styles - CSS Module Version */

.button {
  background: linear-gradient(135deg, #feca57 0%, #ff6b6b 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 12px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Better touch targets for mobile */
  -webkit-tap-highlight-color: transparent;
}

@media (min-width: 480px) {
  .button {
    font-size: 13px;
    padding: 11px 22px;
    min-width: 85px;
    height: 42px;
  }
}

@media (min-width: 640px) {
  .button {
    font-size: 14px;
    padding: 12px 16px;
    min-width: 90px;
    height: 44px;
  }
}

.button:hover {
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(254, 202, 87, 0.3);
}

/* Optimize for mobile touch */
@media (max-width: 767px) {
  .button:hover {
    transform: none;
    /* Disable hover transform on mobile */
  }
}

.button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 6px rgba(254, 202, 87, 0.2);
}

.button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(254, 202, 87, 0.3);
}
