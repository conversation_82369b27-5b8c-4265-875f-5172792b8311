/* Last Played Game Card Styles - Compact */
.gameCard {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 12px;
  padding: 16px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.cardTitle {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #18cffb;
  text-shadow: 0 0 8px rgba(24, 207, 251, 0.4);
}

.gameContent {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(24, 207, 251, 0.2);
  transition: all 0.3s ease;
}

.gameContent:hover {
  background: rgba(24, 207, 251, 0.05);
  border-color: rgba(24, 207, 251, 0.4);
  box-shadow: 0 4px 16px rgba(24, 207, 251, 0.2);
  transform: translateY(-2px);
}

.gameImage {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(24, 207, 251, 0.3);
  box-shadow: 0 0 8px rgba(24, 207, 251, 0.2);
}

.gameImageImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gameImagePlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.gameInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.gameName {
  font-size: 14px;
  font-weight: 600;
  color: #18cffb;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 0 6px rgba(24, 207, 251, 0.4);
}

.gameProvider {
  font-size: 11px;
  color: #9b9b9b;
  font-weight: 500;
}

.gamePayout {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #18cffb;
  font-weight: 500;
  margin-top: 2px;
  text-shadow: 0 0 4px rgba(24, 207, 251, 0.3);
}

@media (min-width: 480px) {
  .gameContent {
    padding: 16px;
  }

  .gameImage {
    width: 56px;
    height: 56px;
  }

  .gameName {
    font-size: 16px;
  }

  .gameProvider {
    font-size: 12px;
  }

  .gamePayout {
    font-size: 11px;
  }
}
