/* Hero Component Styles - CSS Module Version */

* {
  box-sizing: border-box;
}

.heroContainer {
  width: 100%;
}

/* Mobile-first responsive padding */
@media (min-width: 480px) {
  .heroContainer {
    padding: 20px;
  }
}

@media (min-width: 768px) {
  .heroContainer {
    padding: 24px;
  }
}

.heroGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  justify-items: center;
}

@media (min-width: 768px) {
  .heroGrid {
    gap: 20px;
  }
}

@media (min-width: 1280px) {
  .heroGrid {
    grid-template-columns: 1fr auto;
  }
}

.heroContent {
  text-align: center;
  width: 100%;
  max-width: 100%;
  order: 2;
}

/* Remove min-width constraint on mobile for better responsiveness */
@media (min-width: 768px) {
  .heroContent {
    min-width: 500px;
    order: 1;
  }
}

@media (min-width: 1024px) {
  .heroContent {
    width: 400px;
  }
}

@media (min-width: 1280px) {
  .heroContent {
    text-align: left;
  }
}

.heroTitle {
  margin: 0 auto;
  width: 50%;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.4;
  /* Allow text wrapping on mobile */
  animation: fadeDown 0.8s ease-out;
  color: white;
  margin-bottom: 24px;
  word-break: break-word;
  /* Prevent overflow on very long words */
}

/* Progressive font size increases */
@media (min-width: 480px) {
  .heroTitle {
    width: 100%;
    font-size: 28px;
    line-height: 1.5;
    margin-bottom: 28px;
  }
}

@media (min-width: 640px) {
  .heroTitle {
    font-size: 36px;
    line-height: 1.6;
    margin-bottom: 32px;
  }
}

@media (min-width: 768px) {
  .heroTitle {
    font-size: 42px;
  }
}

@media (min-width: 1024px) {
  .heroTitle {
    font-size: 48px;
    /* Only prevent wrapping on larger screens */
  }
}

@media (min-width: 1280px) {
  .heroTitle {
    font-size: 52px;
  }
}

.heroActions {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  justify-content: center;
  flex-wrap: wrap;
  /* Allow wrapping on very small screens */
}

/* Mobile-specific adjustments */
@media (max-width: 479px) {
  .heroActions {
    flex-direction: column;
    gap: 16px;
    margin-top: 16px;
    margin-bottom: 20px;
  }
}

@media (min-width: 480px) {
  .heroActions {
    margin-top: 24px;
    margin-bottom: 28px;
    gap: 14px;
  }
}

@media (min-width: 640px) {
  .heroActions {
    margin-top: 32px;
    margin-bottom: 32px;
    gap: 16px;
    flex-wrap: nowrap;
    /* Prevent wrapping on larger screens */
  }
}

@media (min-width: 1280px) {
  .heroActions {
    justify-content: flex-start;
  }
}

.heroText {
  color: #9b9b9b;
  font-weight: 600;
  font-size: 12px;
  margin: 0;
  text-align: center;
}

@media (min-width: 480px) {
  .heroText {
    font-size: 13px;
  }
}

@media (min-width: 640px) {
  .heroText {
    font-size: 14px;
  }
}

.socialButtons {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  /* Allow wrapping if needed */
}

/* Optimize social buttons for very small screens */
@media (max-width: 479px) {
  .socialButtons {
    gap: 16px;
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 480px) {
  .socialButtons {
    gap: 14px;
  }
}

@media (min-width: 640px) {
  .socialButtons {
    gap: 16px;
    flex-wrap: nowrap;
  }
}

.carouselWrapper {
  order: 1;
  display: flex;
  align-items: center;
  animation: fadeDown 1.2s ease-out;
  justify-content: center;
  transform: scale(0.8);
  margin: 0 auto;
  max-width: 100%;
  /* overflow: hidden; */
  /* Prevent horizontal scroll */
}

/* Progressive scaling for better mobile experience - bigger on phones */
@media (min-width: 480px) {
  .carouselWrapper {
    transform: scale(0.9);
    order: 2;
  }
}

@media (min-width: 640px) {
  .carouselWrapper {
    transform: scale(0.95);
  }
}

@media (min-width: 768px) {
  .carouselWrapper {
    transform: scale(1.0);
  }
}

@media (min-width: 1024px) {
  .carouselWrapper {
    transform: scale(1.0);
  }
}

@media (min-width: 1280px) {
  .carouselWrapper {
    margin-right: 64px;
    justify-content: flex-start;
    transform: scale(1);
  }
}

/* Optimize animations for mobile performance */
@keyframes fadeDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Reduce animation intensity on mobile for better performance */
@media (max-width: 767px) {

  .heroTitle,
  .carouselWrapper {
    animation-duration: 0.6s;
  }

  @keyframes fadeDown {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* VIP Progress Card Styles - Compact */
.vipCard {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 12px;
  padding: 16px;
  color: white;
  animation: fadeDown 0.8s ease-out;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 100%;
  order: 2;
}

@media (min-width: 768px) {
  .vipCard {
    order: 1;
  }
}

@media (min-width: 1024px) {
  .vipCard {
    padding: 20px;
  }
}

.vipHeader {
  margin-bottom: 16px;
}

.vipTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 0;
}

.vipTitle h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #FFD700;
}

@media (min-width: 480px) {
  .vipTitle h2 {
    font-size: 20px;
  }
}

.vipLevels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 16px;
  gap: 12px;
}

@media (max-width: 479px) {
  .vipLevels {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

.currentLevel,
.nextLevel {
  flex: 1;
}

.levelInfo {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

@media (min-width: 480px) {
  .levelInfo {
    font-size: 13px;
    padding: 10px;
  }
}

.progressSection {
  margin-bottom: 16px;
}

.progressLabel {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #9b9b9b;
}

.progressBar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progressStats {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #9b9b9b;
}

@media (min-width: 480px) {
  .progressStats {
    font-size: 12px;
  }
}

.vipBenefits h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #FFD700;
}

@media (min-width: 480px) {
  .vipBenefits h3 {
    font-size: 16px;
  }
}

.vipBenefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.vipBenefits li {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  font-size: 11px;
  color: #e0e0e0;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

@media (min-width: 480px) {
  .vipBenefits li {
    font-size: 12px;
    padding: 5px 10px;
  }
}

/* Authenticated Grid Layout - VIP card spans 2 rows */
.authenticatedGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  justify-items: center;
}

@media (min-width: 768px) {
  .authenticatedGrid {
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto;
    gap: 8px;
    align-items: start;
  }
}

@media (min-width: 1024px) {
  .authenticatedGrid {
    gap: 8px;
  }
}

@media (min-width: 1280px) {
  .authenticatedGrid {
    gap: 16px;
  }
}

.vipSection {
  width: 100%;
  order: 1;
}

@media (min-width: 768px) {
  .vipSection {
    grid-row: 1 / 3;
    grid-column: 1;
  }
}

.statsSection {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
  justify-content: space-between;
  width: 100%;
  order: 2;
}

@media (min-width: 768px) {
  .statsSection {
    grid-column: 2;
    grid-row: 1 / 3;
  }
}

@media (min-width: 1024px) {
  .statsSection {
    width: 100%;
  }
}

/* Base Card Styles - Compact */
.statsCard,
.activityCard,
.achievementsCard {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 12px;
  padding: 16px;
  color: white;
  animation: fadeDown 0.8s ease-out;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.cardTitle {
  display: flex;
  align-items: center;
  gap: 6px;
}

.cardTitle h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #FFD700;
}

@media (min-width: 480px) {
  .cardTitle h3 {
    font-size: 16px;
  }
}

/* Quick Stats Card - Compact */
.statsGrid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.statInfo {
  display: flex;
  flex-direction: column;
  gap: 1px;
  flex: 1;
}

.statLabel {
  font-size: 11px;
  color: #9b9b9b;
  font-weight: 500;
}

.statValue {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

@media (min-width: 480px) {
  .statLabel {
    font-size: 12px;
  }

  .statValue {
    font-size: 16px;
  }
}

/* Recent Activity Card */
.activityList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activityItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.activityIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

.activityContent {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.activityText {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.activityType {
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.activityDetail {
  font-size: 12px;
  color: #FFD700;
  font-weight: 600;
}

.activityTime {
  font-size: 11px;
  color: #9b9b9b;
}

@media (min-width: 480px) {
  .activityType {
    font-size: 15px;
  }

  .activityDetail {
    font-size: 13px;
  }

  .activityTime {
    font-size: 12px;
  }
}

/* Last Played Game Card Styles - Compact */
.gameContent {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.gameImage {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.1);
}

.gameImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gameInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.gameName {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gameProvider {
  font-size: 11px;
  color: #9b9b9b;
  font-weight: 500;
}

.gamePayout {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #FFD700;
  font-weight: 500;
  margin-top: 2px;
}

@media (min-width: 480px) {
  .gameContent {
    padding: 16px;
  }

  .gameImage {
    width: 56px;
    height: 56px;
  }

  .gameName {
    font-size: 16px;
  }

  .gameProvider {
    font-size: 12px;
  }

  .gamePayout {
    font-size: 11px;
  }
}

/* Achievements Card */
.viewAllButton {
  background: transparent;
  border: 1px solid #FFD700;
  color: #FFD700;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.viewAllButton:hover {
  background: #FFD700;
  color: #061d2b;
}

.achievementsList {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.achievementItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.achievementItem.completed {
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.achievementItem.locked {
  opacity: 0.6;
}

.achievementIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

.achievementItem.completed .achievementIcon {
  background: rgba(255, 215, 0, 0.2);
}

.achievementName {
  font-size: 14px;
  font-weight: 500;
  color: white;
  flex: 1;
}

@media (min-width: 480px) {
  .achievementName {
    font-size: 15px;
  }

  .viewAllButton {
    font-size: 13px;
    padding: 6px 12px;
  }
}
