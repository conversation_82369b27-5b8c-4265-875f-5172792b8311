import { FaCrown, FaStar } from 'react-icons/fa'
import styles from './VIPProgressCard.module.css'

// VIP name mapping
const VIP_NAME_MAPPING = {
  'NO VİP': 'NON-VIP'
}

// Helper function to map VIP names
const mapVipName = (vipName) => {
  if (!vipName) return vipName
  return VIP_NAME_MAPPING[vipName.toUpperCase()] || vipName
}

export const VIPProgressCard = ({ i18n, vipData, isLoading, error }) => {
  // Default data for loading state
  const defaultData = {
    currentLevel: isLoading ? 'Loading...' : 'Error',
    currentLevelIcon: <FaStar size={24} color="#18cffb" />,
    nextLevel: isLoading ? 'Loading...' : 'Error',
    nextLevelIcon: <FaCrown size={24} color="#18cffb" />,
    currentProgress: 0,
    totalWagered: '$0.00',
    nextLevelRequirement: '$0.00',
    benefits: [
      'exclusive_bonuses',
      'priority_support',
      'higher_limits'
    ]
  }

  const displayData = (isLoading || error || !vipData) ? defaultData : vipData

  return (
    <div className={styles.vipCard}>
      <div className={styles.vipHeader}>
        <FaCrown size={20} color="#18cffb" />
        <h2 className={styles.vipTitle}>{i18n['vip_status']}</h2>
      </div>

      <div className={styles.vipLevels}>
        <div className={styles.levelInfo}>
          <div className={styles.levelIcon}>
            {displayData.currentLevelIcon}
          </div>
          <div className={styles.levelName}>{mapVipName(displayData.currentLevel)}</div>
          <div className={styles.levelLabel}>{i18n['current_level']}</div>
        </div>
        <div className={styles.arrowIndicator}></div>
        <div className={styles.levelInfo}>
          <div className={styles.levelIcon}>
            {displayData.nextLevelIcon}
          </div>
          <div className={styles.levelName}>{mapVipName(displayData.nextLevel)}</div>
          <div className={styles.levelLabel}>{i18n['next_level']}</div>
        </div>
      </div>

      <div className={styles.progressSection}>
        <div className={styles.progressLabel}>
          <span>{i18n['progress_to_next']}</span>
          <span>{displayData.currentProgress}%</span>
        </div>
        <div className={styles.progressBar}>
          <div
            className={styles.progressFill}
            style={{ width: `${displayData.currentProgress}%` }}
          ></div>
        </div>
        <div className={styles.progressStats}>
          <span>{i18n['total_wagered']}: {displayData.totalWagered}</span>
          <span>{i18n['goal']}: {displayData.nextLevelRequirement}</span>
        </div>
      </div>

      <div className={styles.vipBenefits}>
        <h3 className={styles.benefitsTitle}>{i18n['benefits']}</h3>
        <ul className={styles.benefitsList}>
          {displayData.benefits.map((benefit, index) => (
            <li key={index} className={styles.benefitItem}>
              <FaStar size={8} color="#18cffb" />
              {i18n[benefit]}
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

export const Stylesheet = `
  /* VIP Progress Card styles are in VIPProgressCard.module.css */
`
