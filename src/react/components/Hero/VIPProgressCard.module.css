/* VIP Progress Card Styles - 2 Row Layout */
.vipCard {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 12px;
  padding: 20px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  width: 100%;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.vipHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.vipTitle {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #18cffb;
  text-shadow: 0 0 10px rgba(24, 207, 251, 0.5);
}

.vipLevels {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 16px;
  margin-bottom: 24px;
  flex: 1;
  align-items: center;
}

.levelInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  min-height: 120px;
}

.levelInfo:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(24, 207, 251, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(24, 207, 251, 0.2);
}

.levelIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(24, 207, 251, 0.1);
  border: 2px solid rgba(24, 207, 251, 0.3);
  box-shadow: 0 0 15px rgba(24, 207, 251, 0.3);
}

.levelName {
  font-size: 16px;
  font-weight: 700;
  color: #18cffb;
  margin: 0;
  text-shadow: 0 0 8px rgba(24, 207, 251, 0.4);
}

.levelLabel {
  font-size: 11px;
  color: #18cffb;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

.arrowIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1) 0%, rgba(24, 207, 251, 0.15) 100%);
  border: 2px solid rgba(24, 207, 251, 0.3);
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(24, 207, 251, 0.3);
}

.arrowIndicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  border-right: 3px solid #18cffb;
  border-top: 3px solid #18cffb;
  transform: translate(-50%, -50%) rotate(45deg);
  animation: chevronPulse 2s ease-in-out infinite;
  filter: drop-shadow(0 0 4px rgba(24, 207, 251, 0.6));
}

.arrowIndicator::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 35%;
  width: 8px;
  height: 8px;
  border-right: 2px solid rgba(24, 207, 251, 0.7);
  border-top: 2px solid rgba(24, 207, 251, 0.7);
  transform: translate(-50%, -50%) rotate(45deg);
  animation: chevronFlow 2s ease-in-out infinite 0.3s;
  filter: drop-shadow(0 0 3px rgba(24, 207, 251, 0.5));
}

@keyframes chevronPulse {

  0%,
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) rotate(45deg) scale(0.9);
  }

  50% {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(45deg) scale(1.1);
  }
}

@keyframes chevronFlow {
  0% {
    opacity: 0.3;
    left: 30%;
    transform: translate(-50%, -50%) rotate(45deg) scale(0.7);
  }

  50% {
    opacity: 0.8;
    left: 35%;
    transform: translate(-50%, -50%) rotate(45deg) scale(1);
  }

  100% {
    opacity: 0.3;
    left: 40%;
    transform: translate(-50%, -50%) rotate(45deg) scale(0.7);
  }
}

.arrowIndicator:hover {
  border-color: rgba(24, 207, 251, 0.6);
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.15) 0%, rgba(24, 207, 251, 0.2) 100%);
  transform: scale(1.05);
  transition: all 0.3s ease;
  box-shadow: 0 0 30px rgba(24, 207, 251, 0.5);
}

.arrowIndicator:hover::before {
  animation: chevronPulse 1s ease-in-out infinite;
  border-color: #18cffb;
  filter: drop-shadow(0 0 6px rgba(24, 207, 251, 0.8));
}

.arrowIndicator:hover::after {
  animation: chevronFlow 1s ease-in-out infinite 0.2s;
  border-color: rgba(24, 207, 251, 0.9);
  filter: drop-shadow(0 0 5px rgba(24, 207, 251, 0.7));
}

.progressSection {
  margin-bottom: 16px;
}

.progressLabel {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #9b9b9b;
}

.progressBar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #18cffb 0%, #00bcd4 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(24, 207, 251, 0.5);
}

.progressStats {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #9b9b9b;
}

.vipBenefits {
  margin-top: 12px;
}

.benefitsTitle {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #18cffb;
  text-shadow: 0 0 8px rgba(24, 207, 251, 0.4);
}

.benefitsList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.benefitItem {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(24, 207, 251, 0.1);
  border-radius: 12px;
  font-size: 11px;
  color: #18cffb;
  border: 1px solid rgba(24, 207, 251, 0.3);
  box-shadow: 0 0 8px rgba(24, 207, 251, 0.2);
}

@media (min-width: 480px) {
  .vipCard {
    padding: 24px;
  }

  .vipTitle {
    font-size: 20px;
  }

  .levelInfo {
    font-size: 15px;
    padding: 24px 20px;
    min-height: 140px;
  }

  .levelIcon {
    width: 56px;
    height: 56px;
  }

  .levelName {
    font-size: 18px;
  }

  .arrowIndicator {
    width: 70px;
    height: 70px;
  }

  .arrowIndicator::before {
    width: 14px;
    height: 14px;
    border-width: 3.5px;
  }

  .arrowIndicator::after {
    width: 10px;
    height: 10px;
    border-width: 2.5px;
  }

  .progressStats {
    font-size: 12px;
  }

  .benefitsTitle {
    font-size: 16px;
  }

  .benefitItem {
    font-size: 12px;
    padding: 5px 10px;
  }
}

@media (min-width: 768px) {
  .levelInfo {
    min-height: 160px;
  }
}

@media (max-width: 479px) {
  .vipLevels {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 12px;
    text-align: center;
  }

  .levelInfo {
    min-height: 100px;
    padding: 16px 12px;
  }

  .levelIcon {
    width: 40px;
    height: 40px;
  }

  .levelName {
    font-size: 14px;
  }

  .arrowIndicator {
    width: 50px;
    height: 50px;
    justify-self: center;
    transform: rotate(90deg);
  }

  .arrowIndicator::before {
    width: 10px;
    height: 10px;
    border-width: 2px;
  }

  .arrowIndicator::after {
    width: 6px;
    height: 6px;
    border-width: 1.5px;
  }
}
