/* Quick Stats Card Styles - Compact */
.statsCard {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 12px;
  padding: 16px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.cardTitle {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #18cffb;
  text-shadow: 0 0 8px rgba(24, 207, 251, 0.4);
}

.statsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(24, 207, 251, 0.2);
  transition: all 0.3s ease;
}

.statItem:hover {
  background: rgba(24, 207, 251, 0.05);
  border-color: rgba(24, 207, 251, 0.4);
  box-shadow: 0 2px 12px rgba(24, 207, 251, 0.2);
  transform: translateY(-1px);
}

.statInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.statLabel {
  font-size: 11px;
  color: #9b9b9b;
  font-weight: 500;
}

.statValue {
  font-size: 14px;
  font-weight: 600;
  color: #18cffb;
  text-shadow: 0 0 6px rgba(24, 207, 251, 0.4);
}

@media (min-width: 480px) {
  .statLabel {
    font-size: 12px;
  }

  .statValue {
    font-size: 16px;
  }
}
