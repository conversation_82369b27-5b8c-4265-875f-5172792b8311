import { FaGamepad, FaStar } from 'react-icons/fa'
import styles from './LastPlayedGameCard.module.css'

export const LastPlayedGameCard = ({ i18n, lastPlayedGame, isLoading }) => {
  const defaultGame = {
    name: isLoading ? 'Loading...' : 'No games played',
    provider: isLoading ? 'Loading...' : '',
    image: '',
    payout: isLoading ? '0.00' : '0.00'
  }

  const displayGame = (isLoading || !lastPlayedGame) ? defaultGame : lastPlayedGame

  return (
    <div className={styles.gameCard}>
      <div className={styles.cardHeader}>
        <FaGamepad size={16} color="#18cffb" />
        <h3 className={styles.cardTitle}>{i18n['last_game']}</h3>
      </div>
      <div className={styles.gameContent}>
        <div className={styles.gameImage}>
          {displayGame.image ? (
            <img
              src={displayGame.image}
              alt={displayGame.name}
              className={styles.gameImageImg}
            />
          ) : (
            <div className={styles.gameImagePlaceholder}>
              <FaGamepad size={20} color="#9b9b9b" />
            </div>
          )}
        </div>
        <div className={styles.gameInfo}>
          <div className={styles.gameName}>{displayGame.name}</div>
          <div className={styles.gameProvider}>{displayGame.provider}</div>
          {displayGame.payout && displayGame.payout !== '0.00' && (
            <div className={styles.gamePayout}>
              <FaStar size={8} color="#18cffb" />
              RTP: {displayGame.payout}%
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export const Stylesheet = `
  /* Last Played Game Card styles are in LastPlayedGameCard.module.css */
`
