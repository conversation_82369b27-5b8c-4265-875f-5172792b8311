/* SocialMediaButton Component Styles - CSS Module Version */

.socialButton {
  background: linear-gradient(135deg, #181c32 0%, #2a2f4a 100%);
  border: 2px solid rgba(254, 202, 87, 0.3);
  border-radius: 8px;
  color: #feca57;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  /* Better touch targets for mobile */
  -webkit-tap-highlight-color: transparent;
}

@media (min-width: 480px) {
  .socialButton {
    width: 42px;
    height: 42px;
  }
}

@media (min-width: 640px) {
  .socialButton {
    width: 44px;
    height: 44px;
  }
}

.socialButton:hover {
  background: linear-gradient(135deg, #2a2f4a 0%, #3a3f5a 100%);
  border-color: #feca57;
  transform: scale(1.1);
  color: white;
  box-shadow: 0 4px 12px rgba(254, 202, 87, 0.2);
}

/* Optimize for mobile touch */
@media (max-width: 767px) {
  .socialButton:hover {
    transform: none;
    /* Disable hover transform on mobile */
  }
}

.socialButton:active {
  transform: scale(0.95);
  box-shadow: 0 2px 6px rgba(254, 202, 87, 0.1);
}

.socialButton:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(254, 202, 87, 0.3);
}

.socialIcon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 480px) {
  .socialIcon {
    width: 22px;
    height: 22px;
  }
}

@media (min-width: 640px) {
  .socialIcon {
    width: 24px;
    height: 24px;
  }
}
