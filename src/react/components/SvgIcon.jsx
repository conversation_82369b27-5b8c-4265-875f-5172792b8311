import { useMemo } from "react"

export const SvgIcon = ({ icon: htmlIcon, ...props }) => {
  // const /
  const icon = useMemo(() => {
    const start = htmlIcon.indexOf('>')  
    const end = htmlIcon.indexOf('</svg>')

    return htmlIcon.slice(start + 1, end)
  }, [htmlIcon])

  const attributes = useMemo(() => {
    const regex = /(\w+)\s*=\s*"([^"]*)"/g;
    let match;
    const attributes = {};

    while ((match = regex.exec(htmlIcon)) !== null) {
      // match[1] is the attribute name, match[2] is the attribute value
      attributes[match[1]] = match[2];
    }

    return attributes;
  }, [htmlIcon])

  return <svg {...attributes} {...props} dangerouslySetInnerHTML={{ __html: icon }} ></svg>
}
