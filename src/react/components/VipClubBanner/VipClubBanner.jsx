import styles from './VipClubBanner.module.css'

export const i18n = {
  en: {
    alt: 'VIP Club Banner'
  },
  tr: {
    alt: 'VIP Kulüp Banner'
  }
}

const VipClubBanner = ({ i18n = {}, language = 'en' }) => {
  // Determine the image URL based on language
  const getImageUrl = (lang) => {
    // Both languages use the same image URL as specified
    return `https://d11caojl6op1au.cloudfront.net/img/button-banner-${lang}.jpg`
  }

  const imageUrl = getImageUrl(language)
  const altText = i18n.alt || 'VIP Club Banner'

  // Handle banner click to navigate to VIP page
  const handleBannerClick = (e) => {
    e.preventDefault()
    const vipUrl = `/${language}/vip`
    window.location.href = vipUrl
  }

  return (
    <div className={styles.container}>
      <div className={styles.bannerWrapper}>
        <a
          href={`/${language}/vip`}
          className={styles.bannerLink}
          onClick={handleBannerClick}
          aria-label={altText}
        >
          <img
            src={imageUrl}
            alt={altText}
            className={styles.bannerImage}
            loading="lazy"
            onError={(e) => {
              console.error('Failed to load VIP Club banner image:', e.target.src)
              e.target.style.display = 'none'
            }}
          />
        </a>
      </div>
    </div>
  )
}

export { VipClubBanner }
export default VipClubBanner
