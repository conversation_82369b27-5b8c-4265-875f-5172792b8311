.container {
  width: 100%;
  margin: 40px 0;
  box-sizing: border-box;
}

.bannerWrapper {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  box-sizing: border-box;
}

.bannerLink {
  display: block;
  width: 100%;
  text-decoration: none;
  cursor: pointer;
}

.bannerImage {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.bannerImage:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(24, 207, 251, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    margin: 20px 0;
  }

  .bannerImage {
    border-radius: 16px;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 16px 0;
  }

  .bannerImage {
    border-radius: 12px;
  }
}
