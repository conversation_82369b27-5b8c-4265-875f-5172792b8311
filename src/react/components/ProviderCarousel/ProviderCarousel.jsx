import { useMemo } from 'react'
import styles from './ProviderCarousel.module.css'

import alg from '../../assets/alg.svg'
import booming from '../../assets/booming.svg'
import egt from '../../assets/egt.svg'
import endorphina from '../../assets/endorphina.svg'
import evolution from '../../assets/evolution.svg'
import ezugi from '../../assets/ezugi.svg'
import popiplay from '../../assets/popiplay.svg'
import quickspin from '../../assets/quickspin.svg'
import sexy from '../../assets/sexy.svg'
import thunderkick from '../../assets/thunderkick.svg'

export const ProviderCarousel = ({ providers }) => {
  const randomizedProviders = useMemo(() => {
  const providers = [
    alg,
    booming,
    egt,
    endorphina,
    evolution,
    ezugi,
    sexy,
    popiplay,
    quickspin,
    thunderkick
  ]
    // Shuffle the providers for variety
    // const shuffled = shuffleArray(providers)
    // Duplicate the array to ensure seamless infinite scrolling
    return [...providers, ...providers]
  }, [providers])

  return (
    <div className={styles.providerCarouselContainer}>
      <div className={styles.marqueeWrapper}>
        <div className={styles.marqueeTrack}>
          {randomizedProviders.map((Provider, idx) => (
            <div key={`first-${idx}`} className={styles.carouselItem} dangerouslySetInnerHTML={{ __html: Provider }} />
          ))}
        </div>
        <div className={styles.marqueeTrack}>
          {[...randomizedProviders].map((Provider, idx) => (
            <div key={`second-${idx}`} className={styles.carouselItem} dangerouslySetInnerHTML={{ __html: Provider }} />
          ))}
        </div>
      </div>
    </div>
  )
}

export default ProviderCarousel
