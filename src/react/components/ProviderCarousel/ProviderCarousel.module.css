/* ProviderCarousel Component Styles - CSS Module Version */

* {
  box-sizing: border-box;
}

button {
  margin: 0;
  padding: 0;
}

.providerCarouselContainer {
  position: relative;
  width: 100%;
  max-width: calc(1440px - 48px);
  height: 100%;
  margin: 40px auto;
  overflow: hidden;
  box-sizing: border-box;
  mask-image: linear-gradient(to right, transparent, black 10%, black 90%, transparent);
  -webkit-mask-image: linear-gradient(to right, transparent, black 10%, black 90%, transparent);
}

@media (max-width: 1199px) {
  .providerCarouselContainer {
    max-width: 1200px;
  }
}

@media (max-width: 991px) {
  .providerCarouselContainer {
    max-width: 960px;
  }
}

@media (max-width: 767px) {
  .providerCarouselContainer {
    max-width: none;
  }
}

.marqueeWrapper {
  display: flex;
  width: fit-content;
  animation: marqueeScroll 30s linear infinite;
}

.marqueeTrack {
  display: flex;
  flex-shrink: 0;
}

.carouselItem {
  flex: 0 0 auto;
  width: clamp(100px, 10vw, 200px);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  transition: transform 0.3s ease;
}

.carouselItem:hover {
  transform: scale(1.05);
}

.providerImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
  filter: brightness(0.9);
  transition: filter 0.3s ease;
}

.carouselItem:hover .providerImage {
  filter: brightness(1.1);
}

@keyframes marqueeScroll {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .carouselItem {
    width: clamp(80px, 15vw, 150px);
    padding: 8px;
  }

  .marqueeWrapper {
    animation-duration: 25s;
  }
}

@media (max-width: 480px) {
  .carouselItem {
    width: clamp(60px, 20vw, 120px);
    padding: 6px;
  }

  .marqueeWrapper {
    animation-duration: 20s;
  }
}
