.sidebarButtonsSmall {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 10px;
}

.buttonLink {
  text-decoration: none;
  display: block;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 8px;
  width: 46px;
  height: 46px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-family: inherit;
}

.button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.buttonIcon {
  width: 90%;
  height: 90%;
  object-fit: contain;
  flex-shrink: 0;
}

/* Casino Button - Dark and Red tones */
.casinoButton {
  background: linear-gradient(60deg, #1a1010 0%, #8a2020 50%, #c53030 100%);
  color: white;
}

.casinoButton:hover {
  background: linear-gradient(60deg, #2a1515 0%, #a52828 50%, #e53e3e 100%);
}

/* Sports Button - Dark and Blue tones */
.sportsButton {
  background: linear-gradient(60deg, #0f1a1a 0%, #2080a0 50%, rgb(24, 207, 251) 100%);
  color: white;
}

.sportsButton:hover {
  background: linear-gradient(60deg, #152525 0%, #3090b0 50%, rgb(34, 217, 255) 100%);
}

/* Missions Button - Mixed tones */
.missionsButton {
  background: linear-gradient(60deg, #1a101a 0%, #7a4a7a 50%, #b366b3 100%);
  color: white;
}

.missionsButton:hover {
  background: linear-gradient(60deg, #2a1525 0%, #8a5a8a 50%, #c376c3 100%);
}
