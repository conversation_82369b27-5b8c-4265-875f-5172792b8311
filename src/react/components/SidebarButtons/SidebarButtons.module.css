.sidebarButtons {
  font-family: Poppins, sans-serif;

  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 10px;
  margin-bottom: 10px;
}

.sidebarButtons>.buttonLink:nth-child(1) {
  grid-column: 1;
  grid-row: 1;
}

.sidebarButtons>.buttonLink:nth-child(2) {
  grid-column: 2;
  grid-row: 1;
}

.sidebarButtons>.buttonLink:nth-child(3) {
  grid-column: 1 / span 2;
  grid-row: 2;
}

.buttonLink {
  text-decoration: none;
  display: block;
}

.button {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 8px 0px 8px 8px;
  border-radius: 6px;
  height: 60px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-family: inherit;
  position: relative;
  /* overflow: hidden; */
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.buttonTextContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  line-height: 1;
  position: relative;
  z-index: 2;
}

.buttonText {
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
  margin: 0;
}

.buttonTextSmall {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin: 0;
  margin-bottom: 2px;
}

.buttonIcon {
  position: absolute;
  top: 57%;
  right: -28px;
  transform: translateY(-50%);
  width: 70px;
  height: 70px;
  object-fit: contain;
  /* opacity: 0.8; */
  z-index: 1;
}

.archerIcon {
  width: 70px;
  height: 80px;
  position: absolute;
  top: 50%;
  left: 80px;
  transform: translateY(-50%);
  object-fit: contain;
  z-index: 2;
}

.arrowIcon {
  display: none;
}

@media (max-width: 768px) {
  .arrowIcon {
    display: block;
    width: 63px;
    height: 57px;
    position: absolute;
    top: 36%;
    left: 197px;
    transform: translateY(-50%) rotate(11deg);
    object-fit: contain;
    z-index: 2;
  }

  .archerIcon {
    left: 100px;
  }
}

/* Casino Button - Dark and Red tones */
.casinoButton {
  background: linear-gradient(60deg, #1a1010 25%, #8a2020 75%, #c53030 100%);
  color: white;
}

.casinoButton:hover {
  background: linear-gradient(60deg, #2a1515 25%, #a52828 75%, #e53e3e 100%);
}

/* Sports Button - Dark and Blue tones */
.sportsButton {
  background: linear-gradient(60deg, #0f1a1a 25%, #2080a0 75%, rgb(24, 207, 251) 100%);
  color: white;
}

.sportsButton:hover {
  background: linear-gradient(60deg, #152525 25%, #3090b0 75%, rgb(34, 217, 255) 100%);
}

/* Missions Button - Mixed tones */
.missionsButton {
  background: linear-gradient(60deg, #1a101a 25%, #7a4a7a 75%, #b366b3 100%);
  color: white;
}

.missionsButton:hover {
  background: linear-gradient(60deg, #2a1525 25%, #8a5a8a 75%, #c376c3 100%);
}

/* Bright text colors for each button type */
.casinoButton .buttonTextSmall {
  color: #c53030;
}

.sportsButton .buttonTextSmall {
  color: rgb(24, 207, 251);
}

.missionsButton .buttonTextSmall {
  color: #b366b3;
}
