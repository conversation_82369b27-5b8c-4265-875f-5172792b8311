import { useEffect } from 'react'
import styles from './SidebarButtons.module.css'

const SidebarButtons = ({ language }) => {
  useEffect(() => {
    document.querySelector('.sidebar__links').style.display = 'none'
  }, [])
  
  return <div className={styles.sidebarButtons}>
    <a href={`/${language}/casino`} className={styles.buttonLink}>
      <div className={`${styles.button} ${styles.casinoButton}`}>
        <div className={styles.buttonTextContainer}>
          <span className={styles.buttonTextSmall}>Play now</span>
          <span className={styles.buttonText}>Casino</span>
        </div>
        <img
          src="https://d3r2lacstledxo.cloudfront.net/img/sidebar/casino.png"
          alt="Casino"
          className={styles.buttonIcon}
        />
      </div>
    </a>
    <a href={`/${language}/sportsbook`} className={styles.buttonLink}>
      <div className={`${styles.button} ${styles.sportsButton}`}>
        <div className={styles.buttonTextContainer}>
          <span className={styles.buttonTextSmall}>Play now</span>
          <span className={styles.buttonText}>Sports</span>
        </div>
        <img
          src="https://d3r2lacstledxo.cloudfront.net/img/sidebar/sports.png"
          alt="Sports"
          className={styles.buttonIcon}
        />
      </div>
    </a>
    <a href={`/${language}/missions`} className={styles.buttonLink}>
      <div className={`${styles.button} ${styles.missionsButton}`}>
        <div className={styles.buttonTextContainer}>
          <span className={styles.buttonTextSmall}>Check out</span>
          <span className={styles.buttonText}>Missions</span>
        </div>
        <img
          src="https://d3r2lacstledxo.cloudfront.net/img/sidebar/archer.png"
          alt="Archer"
          className={styles.archerIcon}
        />
        <img
          src="https://d3r2lacstledxo.cloudfront.net/img/sidebar/arrow.png"
          alt="Arrow"
          className={styles.arrowIcon}
        />
        <img
          src="https://d3r2lacstledxo.cloudfront.net/img/sidebar/missions.png"
          alt="Missions"
          className={styles.buttonIcon}
        />
      </div>
    </a>
  </div>
}

export default SidebarButtons
