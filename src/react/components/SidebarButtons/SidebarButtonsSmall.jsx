import { useEffect } from 'react'
import styles from './SidebarButtonsSmall.module.css'

const SidebarButtons = ({ language }) => {
  useEffect(() => {
    document.querySelector('.sidebar__links-small').style.display = 'none'
  }, [])

  return <div className={styles.sidebarButtonsSmall}>
    <a href={`/${language}/casino`} className={styles.buttonLink}>
      <div className={`${styles.button} ${styles.casinoButton}`}>
        <img
          src="https://d3r2lacstledxo.cloudfront.net/img/sidebar/casino.png"
          alt="Casino"
          className={styles.buttonIcon}
        />
      </div>
    </a>
    <a href={`/${language}/sportsbook`} className={styles.buttonLink}>
      <div className={`${styles.button} ${styles.sportsButton}`}>
        <img
          src="https://d3r2lacstledxo.cloudfront.net/img/sidebar/sports.png"
          alt="Sports"
          className={styles.buttonIcon}
        />
      </div>
    </a>
    <a href={`/${language}/missions`} className={styles.buttonLink}>
      <div className={`${styles.button} ${styles.missionsButton}`}>
        <img
          src="https://d3r2lacstledxo.cloudfront.net/img/sidebar/missions.png"
          alt="Missions"
          className={styles.buttonIcon}
        />
      </div>
    </a>
  </div>
}

export default SidebarButtons
