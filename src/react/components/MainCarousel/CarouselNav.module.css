/* CarouselNav Component Styles - CSS Module Version */

.navContainer {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) translateY(50%);
  display: flex;
  align-items: end;
  gap: 12px;
  z-index: 90;
}

@media (min-width: 640px) {
  .navContainer {
    gap: 16px;
  }
}

.arrowBtn {
  color: white;
  transition: opacity 0.3s ease;
  padding: 8px 12px;
  background: linear-gradient(to top, rgba(27, 36, 85, 0.75), rgba(220, 216, 255, 0.05));
  border-radius: 6px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
  /* Better touch targets for mobile */
}

@media (min-width: 640px) {
  .arrowBtn {
    padding: 11.52px 15.52px;
    min-width: 44px;
    min-height: 44px;
  }
}

.arrowBtn:hover {
  opacity: 0.8;
}

/* Improve touch feedback on mobile */
.arrowBtn:active {
  opacity: 0.6;
  transform: scale(0.95);
}

.dotsContainer {
  display: flex;
  gap: 6px;
}

@media (min-width: 640px) {
  .dotsContainer {
    gap: 8px;
  }
}

.dot {
  padding: 0;
  margin-bottom: 4.8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
  background-color: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
  /* Larger touch targets for mobile */
}

@media (min-width: 640px) {
  .dot {
    width: 8px;
    height: 8px;
  }
}

.activeDot {
  background-color: white;
}

.dot:hover {
  background-color: rgba(255, 255, 255, 0.6);
}

/* Improve touch feedback on mobile */
.dot:active {
  background-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}
