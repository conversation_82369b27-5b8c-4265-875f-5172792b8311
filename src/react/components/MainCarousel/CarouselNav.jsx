import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import styles from './CarouselNav.module.css';

// Internationalization
export const i18n = {
  en: {
    previous_slide: 'Previous Slide',
    next_slide: 'Next Slide',
    go_to_slide: 'Go to slide'
  },
  tr: {
    previous_slide: 'Önceki Slayt',
    next_slide: 'Sonraki Slayt',
    go_to_slide: 'Slayta git'
  }
};

export const CarouselNav = ({
  currentIndex,
  totalSlides,
  onNext,
  onPrev,
  onDotClick,
  i18n: translations = {}
}) => {
  return (
    <div className={styles.navContainer}>
      {/* Left arrow */}
      <button
        onClick={onPrev}
        className={styles.arrowBtn}
        aria-label={translations.previous_slide || "Previous Slide"}
      >
        <FiChevronLeft size={24} />
      </button>

      {/* Dots */}
      <div className={styles.dotsContainer}>
        {Array.from({ length: totalSlides }).map((_, idx) => (
          <button
            key={idx}
            className={`${styles.dot} ${idx === currentIndex ? styles.activeDot : ''}`}
            onClick={() => onDotClick(idx)}
            aria-label={`${translations.go_to_slide || "Go to slide"} ${idx + 1}`}
          />
        ))}
      </div>

      {/* Right arrow */}
      <button
        onClick={onNext}
        className={styles.arrowBtn}
        aria-label={translations.next_slide || "Next Slide"}
      >
        <FiChevronRight size={24} />
      </button>
    </div>
  );
};
