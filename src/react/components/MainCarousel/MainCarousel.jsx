import { useEffect, useState } from 'react'
import { CarouselNav } from './CarouselNav'
import styles from './MainCarousel.module.css'

export const MainCarousel = ({ slides: propSlides }) => {
  // Default slides data
  const defaultSlides = [
    'https://betrozgiris.com/img/fast-access/canli-casino.png',
    'https://betrozgiris.com/img/fast-access/spor-bahis.png',
    'https://betrozgiris.com/img/fast-access/slot-oyun.png',
    'https://betrozgiris.com/img/fast-access/aviator.png',
    'https://betrozgiris.com/img/fast-access/sanal-spor.png',
    'https://betrozgiris.com/img/fast-access/bonus.png'
  ]

  const slides = propSlides || defaultSlides

  const [currentIndex, setCurrentIndex] = useState(0)
  const [manualInteraction, setManualInteraction] = useState({})

  useEffect(() => {
    const id = setInterval(() => {
      setCurrentIndex((prev) => {
        return (prev + 1) % slides.length
      })
    }, 5000)
    return () => {
      clearInterval(id)
    }
  }, [manualInteraction, slides.length])

  // Click handlers
  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % slides.length)
    setManualInteraction({})
  }

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev - 1 + slides.length) % slides.length)
    setManualInteraction({})
  }

  const handleDotClick = (index) => {
    setCurrentIndex(index)
  }

  return (
    <div className={styles.carouselContainer}>
      {slides.map((url, index) => {
        let className = styles.hiddenSlide

        // Determine position relative to currentIndex
        const prevIndex = (currentIndex - 1 + slides.length) % slides.length
        const nextIndex = (currentIndex + 1) % slides.length

        if (index === currentIndex) {
          className = styles.currentSlide
        } else if (index === prevIndex) {
          className = styles.slideLeft
        } else if (index === nextIndex) {
          className = styles.slideRight
        }

        return (
          <div
            key={index}
            className={`${styles.slide} ${className}`}
            style={{ backgroundImage: `url(${url})` }}
          ></div>
        )
      })}

      {/* Navigation (arrows and dots) */}
      <CarouselNav
        currentIndex={currentIndex}
        totalSlides={slides.length}
        onNext={handleNext}
        onPrev={handlePrev}
        onDotClick={handleDotClick}
      />
    </div>
  )
}

export default MainCarousel;
