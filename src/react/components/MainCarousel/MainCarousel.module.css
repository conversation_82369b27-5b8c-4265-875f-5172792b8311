/* MainCarousel Component Styles - CSS Module Version */

.carouselContainer {
  width: 100%;
  height: 300px;
  min-width: 320px;
  min-height: 240px;
  max-width: 450px;
  margin: 0 auto;
  position: relative;
  transition: all 300ms;
}

/* Progressive size increases for better mobile experience */
@media (min-width: 480px) {
  .carouselContainer {
    width: 380px;
    height: 320px;
    min-width: 350px;
    min-height: 280px;
  }
}

@media (min-width: 640px) {
  .carouselContainer {
    width: 420px;
    height: 350px;
    min-width: 380px;
    min-height: 300px;
  }
}

@media (min-width: 768px) {
  .carouselContainer {
    width: 450px;
    height: 360px;
    min-width: 378px;
    min-height: 314px;
  }
}

@media (min-width: 1024px) {
  .carouselContainer {
    width: 484px;
    height: 400px;
    min-width: 400px;
    min-height: 330px;
  }
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  transition: all 400ms ease-in-out;
  border-radius: 12px;
}

.currentSlide {
  z-index: 20;
  transform: translateX(0) rotateY(0deg) scale(1);
  filter: blur(0);
  opacity: 1;
}

.slideLeft {
  z-index: 10;
  transform: rotateY(30deg) translateX(-35%) scale(0.8);
  filter: blur(4px);
}

.slideRight {
  z-index: 10;
  transform: rotateY(-30deg) translateX(35%) scale(0.8);
  filter: blur(4px);
}

.hiddenSlide {
  display: none;
}
