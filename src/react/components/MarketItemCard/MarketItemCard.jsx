import styles from "./MarketItemCard.module.css";

export const i18n = {
  en: {
    shop: "Missions Shop",
    price: "Price",
    buy: "Buy",
    bought: "Bought",
    notEnoughDiamonds: "Not enough diamonds",
    diamonds: "Diamonds",
    quantity: "Quantity",
    description: "Description",
    viewMore: "View More",
  },
  tr: {
    shop: "Görev Mağazası",
    price: "Fiyat",
    buy: "Satın Al",
    bought: "Satın Alındı",
    notEnoughDiamonds: "Yeterli elmas yok",
    diamonds: "Elmas",
    quantity: "Miktar",
    description: "Açıklama",
    viewMore: "Daha Fazla Gör",
  },
};

const MarketItemCard = ({
  item,
  userPoints = 0,
  i18n = {},
  language = "en",
  onClick,
}) => {
  const canAfford = userPoints >= item.price;

  return (
    <div
      key={item.id}
      className={`${styles.shopItem} ${!canAfford ? styles.unaffordable : ""}`}
    >
      <div className={styles.itemImage}>
        {item.photoUrl && (
          <img
            src={item.photoUrl}
            alt={item.name_i18n[language]}
            className={styles.image}
            loading="lazy"
            onError={(e) => {
              e.target.style.display = "none";
            }}
          />
        )}
      </div>

       <div className={styles.itemContent}>
        <h3 className={styles.itemName}>{item.name_i18n[language]}</h3>

        {item.description && (
          <p className={styles.itemDescription}>{item.description_i18n[language]}</p>
        )}

        {item.availableAmount && (
          <div className={styles.itemQuantity}>
            <span className={styles.quantityLabel}>
              {i18n.quantity || "Quantity"}:
            </span>
            <span className={styles.quantityValue}>{item.availableAmount}</span>
          </div>
        )}

        <div className={styles.actionSection}>
          <button
            className={`${styles.buyButton} ${
              !canAfford ? styles.disabled : ""
            }`}
            onClick={onClick}
            disabled={!canAfford}
            title={
              !canAfford ? i18n.notEnoughDiamonds : ""
            }
          >
            💎 {item.price.toLocaleString()}
          </button>
        </div>
      </div>
    </div>
  );
};

export { MarketItemCard };
export default MarketItemCard;
