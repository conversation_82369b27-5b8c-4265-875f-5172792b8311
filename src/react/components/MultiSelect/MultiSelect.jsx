import { useState, useRef, useEffect } from "react";
import styles from "./MultiSelect.module.css";

const MultiSelect = ({
  options = [],
  value = [],
  onChange,
  placeholder = "Select options",
  disabled = false,
  className = "",
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      const path = event.composedPath?.() || [];
      if (selectRef.current && !path.includes(selectRef.current)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isOpen) return;

      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen]);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleOptionClick = (optionValue) => {
    const newValue = value.includes(optionValue)
      ? value.filter(v => v !== optionValue)
      : [...value, optionValue];
    
    onChange(newValue);
  };

  const handleClearAll = (e) => {
    e.stopPropagation();
    onChange([]);
  };

  const getDisplayText = () => {
    if (value.length === 0) {
      return placeholder;
    }
    
    if (value.length === 1) {
      const selectedOption = options.find(opt => opt.value === value[0]);
      return selectedOption ? selectedOption.label : value[0];
    }
    
    return `${value.length} selected`;
  };

  const selectedOptions = value.map(val => {
    const option = options.find(opt => opt.value === val);
    return option ? option.label : val;
  });

  return (
    <div 
      ref={selectRef}
      className={`${styles.multiSelect} ${className} ${disabled ? styles.disabled : ''}`}
      {...props}
    >
      <div 
        className={`${styles.selectTrigger} ${isOpen ? styles.open : ''}`}
        onClick={handleToggle}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-label={placeholder}
      >
        <div className={styles.selectedContent}>
          <span className={styles.selectedText}>
            {getDisplayText()}
          </span>
          {value.length > 0 && (
            <div className={styles.selectedTags}>
              {selectedOptions.slice(0, 3).map((label, index) => (
                <span key={index} className={styles.tag}>
                  {label}
                </span>
              ))}
              {value.length > 3 && (
                <span className={styles.tag}>
                  +{value.length - 3} more
                </span>
              )}
            </div>
          )}
        </div>
        
        <div className={styles.actions}>
          {value.length > 0 && (
            <button
              className={styles.clearButton}
              onClick={handleClearAll}
              type="button"
              aria-label="Clear all selections"
            >
              ✕
            </button>
          )}
          <div className={`${styles.arrow} ${isOpen ? styles.arrowUp : ''}`}>
            <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
              <path 
                d="M1 1.5L6 6.5L11 1.5" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>

      {isOpen && (
        <div className={styles.optionsContainer}>
          <ul 
            className={styles.optionsList}
            role="listbox"
            aria-label={placeholder}
          >
            {options.map((option) => (
              <li
                key={option.value}
                className={`${styles.option} ${
                  value.includes(option.value) ? styles.selected : ''
                }`}
                onClick={() => handleOptionClick(option.value)}
                role="option"
                aria-selected={value.includes(option.value)}
              >
                <div className={styles.checkbox}>
                  {value.includes(option.value) && (
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                      <path 
                        d="M2 6L5 9L10 3" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
                <span className={styles.optionLabel}>{option.label}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export { MultiSelect };
export default MultiSelect;
