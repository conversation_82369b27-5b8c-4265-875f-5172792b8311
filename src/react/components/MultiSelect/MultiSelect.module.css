/* MultiSelect Component Styles */
.multiSelect {
  position: relative;
  width: 100%;
  font-family: inherit;
}

.selectTrigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 48px;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.selectTrigger:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
  border-color: rgba(255, 255, 255, 0.3);
}

.selectTrigger:focus {
  border-color: #18cffb;
  box-shadow: 0 0 0 2px rgba(24, 207, 251, 0.2);
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1) 0%, rgba(10, 195, 198, 0.05) 100%);
}

.selectTrigger.open {
  border-color: #18cffb;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1) 0%, rgba(10, 195, 198, 0.05) 100%);
}

.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.disabled .selectTrigger {
  cursor: not-allowed;
}

.selectedContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
}

.selectedText {
  font-size: 0.9rem;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selectedTags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  background: rgba(24, 207, 251, 0.2);
  border: 1px solid rgba(24, 207, 251, 0.4);
  border-radius: 12px;
  font-size: 0.75rem;
  color: #18cffb;
  font-weight: 500;
}

.actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
}

.clearButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.clearButton:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  transition: transform 0.2s ease;
}

.arrowUp {
  transform: rotate(180deg);
}

.optionsContainer {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  max-height: 200px;
  overflow: hidden;
}

.optionsList {
  list-style: none;
  margin: 0;
  padding: 8px 0;
  max-height: 192px;
  overflow-y: auto;
}

.option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #ffffff;
}

.option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.option.selected {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.15) 0%, rgba(10, 195, 198, 0.1) 100%);
  color: #18cffb;
}

.option.selected:hover {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.25) 0%, rgba(10, 195, 198, 0.15) 100%);
}

.checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.option.selected .checkbox {
  background: #18cffb;
  border-color: #18cffb;
  color: #000000;
}

.optionLabel {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Scrollbar styling */
.optionsList::-webkit-scrollbar {
  width: 6px;
}

.optionsList::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.optionsList::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.optionsList::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .selectTrigger {
    min-height: 44px;
    padding: 10px 14px;
  }
  
  .selectedText {
    font-size: 0.85rem;
  }
  
  .tag {
    font-size: 0.7rem;
    padding: 1px 6px;
  }
  
  .option {
    padding: 10px 14px;
  }
  
  .optionLabel {
    font-size: 0.85rem;
  }
}
