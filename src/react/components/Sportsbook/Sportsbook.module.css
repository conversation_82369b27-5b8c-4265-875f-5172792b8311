/* Sportsbook Component Styles - CSS Module Version */

* {
  box-sizing: border-box;
}

/* Main container */
.container {
  width: 100%;
  margin: 40px auto;
  position: relative;
  overflow: hidden;
}

/* Sportsbook section with left block and slider */
.sportsbookSection {
  position: relative;
  display: flex;
  /* height: 200px; */
  width: 100%;
}

/* Left side block */
.leftBlock {
  position: relative;
  width: 280px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 32px 24px;
  box-sizing: border-box;
  min-height: 200px;
}

.leftBlockBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% + 50px);
  height: 100%;
  background:
    /* Primary gradient overlay */
    linear-gradient(to right,
      rgba(24, 207, 251, 0.18) 0%,
      rgba(24, 207, 251, 0.14) 60%,
      rgba(24, 207, 251, 0.10) 75%,
      rgba(24, 207, 251, 0.06) 85%,
      rgba(24, 207, 251, 0.03) 95%,
      transparent 100%),
    /* Depth gradient */
    linear-gradient(135deg,
      rgba(24, 207, 251, 0.12) 0%,
      rgba(10, 195, 198, 0.08) 50%,
      rgba(6, 29, 43, 0.15) 100%),
    /* Subtle accent spots */
    radial-gradient(circle at 25% 75%, rgba(24, 207, 251, 0.06), transparent 60%),
    radial-gradient(circle at 75% 25%, rgba(10, 195, 198, 0.06), transparent 60%),
    /* Base dark overlay */
    linear-gradient(to right, rgba(6, 29, 43, 0.4), transparent);
  backdrop-filter: blur(24px);
  border-radius: 0 28px 28px 0;
  z-index: 1;
  /* Subtle border enhancement */
  border: 1px solid rgba(24, 207, 251, 0.15);
  border-left: none;
  /* Very subtle shadow for depth */
  box-shadow:
    0 8px 32px rgba(24, 207, 251, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.leftBlockContent {
  position: relative;
  z-index: 2;
}

/* Football player image with pseudo 3D effect */
.footballPlayerImage {
  position: absolute;
  right: -120px;
  bottom: -7px;
  width: 200px;
  height: 200px;
  z-index: 3;
  overflow: visible;
  pointer-events: none;
}

.footballPlayerImage img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: bottom;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

/* Hover effect for pseudo 3D */
.leftBlock:hover .footballPlayerImage img {
  transform: scale(1.05) translateY(-5px);
}

.title {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 18px 0;
  line-height: 1.1;
  letter-spacing: -0.02em;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.4),
    0 1px 0 rgba(24, 207, 251, 0.3);
  text-transform: uppercase;
  position: relative;
}

.title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -4px;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 2px;
}

.subtitle {
  font-size: 1.15rem;
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: 0.01em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin-top: 8px;
}

/* Slider section */
.sliderSection {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
  margin-left: -120px;
  padding-left: 120px;
}

.sportsbookContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Responsive marquee container */
.marqueeContainer {
  width: 100%;
  height: 100%;
  display: grid;
}

/* Slider wrapper with mask for smooth edges */
.sliderWrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 12px 0;
  overflow: hidden;
  box-sizing: border-box;
}

/* Animated slider track */
.sliderTrack {
  display: flex;
  width: fit-content;
  animation: marqueeScroll 240s linear infinite;
  gap: 16px;
}

/* Pause animation on hover */
.sliderWrapper:hover .sliderTrack {
  animation-play-state: paused;
}

/* Game card styling */
.gameCard {
  flex: 0 0 auto;
  width: 350px;
  height: 100%;
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border-radius: 12px;
  border: 1px solid rgba(24, 207, 251, 0.2);
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.gameCard:hover {
  transform: translateY(-4px);
  border-color: rgba(24, 207, 251, 0.4);
  box-shadow: 0 8px 25px rgba(24, 207, 251, 0.15);
}

/* Card content */
.cardContent {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

/* Top row layout */
.topRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 8px;
}

/* Category and tournament name */
.categoryTournament {
  color: #18cffb;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex: 1;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Time section */
.timeSection {
  color: #18cffb;
  font-size: 12px;
  font-weight: 600;
  text-align: right;
  line-height: 1.2;
  flex-shrink: 0;
}

/* Live indicator */
.liveIndicator {
  background: #18cffb;
  color: #000;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 4px;
}

.liveDot {
  width: 6px;
  height: 6px;
  background: #000;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

/* Teams section */
.teamsSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

.team {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.teamLogo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(24, 207, 251, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(24, 207, 251, 0.3);
  position: relative;
}

.teamLogo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: relative;
  z-index: 2;
}

.teamAbbr {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #18cffb;
  font-size: 10px;
  z-index: 1;
  text-transform: uppercase;
}

.logoPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #18cffb;
  font-size: 16px;
}

.teamName {
  font-size: 12px;
  color: #fff;
  text-align: center;
  font-weight: 500;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* VS section */
.vsSection {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 16px;
}

.vsText {
  font-size: 14px;
  font-weight: bold;
  color: #18cffb;
  background: rgba(24, 207, 251, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid rgba(24, 207, 251, 0.3);
}

/* Markets section */
.marketsSection {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 12px;
}

.marketGroup {
  display: flex;
  gap: 4px;
  justify-content: space-between;
}

.marketButton {
  flex: 1;
  background: rgba(24, 207, 251, 0.1);
  border: 1px solid rgba(24, 207, 251, 0.3);
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-height: 40px;
}

.marketButton:hover {
  background: rgba(24, 207, 251, 0.2);
  border-color: rgba(24, 207, 251, 0.5);
  transform: translateY(-1px);
}

.marketButton:active {
  transform: translateY(0);
}

.outcomeName {
  font-size: 10px;
  color: #fff;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.odds {
  font-size: 12px;
  color: #18cffb;
  font-weight: bold;
}

/* Score info */
.scoreInfo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}

.score {
  font-size: 16px;
  color: #18cffb;
  font-weight: bold;
  background: rgba(24, 207, 251, 0.1);
  padding: 4px 12px;
  border-radius: 8px;
  border: 1px solid rgba(24, 207, 251, 0.3);
}

/* Loading state */
.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #18cffb;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(24, 207, 251, 0.3);
  border-top: 3px solid #18cffb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Error and empty states */
.errorState,
.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
  font-size: 14px;
}

/* Animations */
@keyframes marqueeScroll {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    margin: 20px auto;
  }

  .sportsbookSection {
    flex-direction: column;
    height: auto;
  }

  .leftBlock {
    width: 100%;
    height: auto;
    padding: 40px 24px;
    text-align: center;
    min-height: 160px;
  }

  .leftBlockBackground {
    border-radius: 24px 24px 0 0;
    width: 100%;
    border: 1px solid rgba(24, 207, 251, 0.12);
    border-bottom: none;
  }

  .sliderSection {
    margin-left: 0;
    padding-left: 0;
    padding-top: 20px;
  }

  .title {
    font-size: 2.4rem;
    margin-bottom: 16px;
  }

  .title::before {
    width: 50px;
    height: 2px;
  }

  .subtitle {
    font-size: 1.08rem;
  }

  .footballPlayerImage {
    right: -40px;
    bottom: -15px;
    width: 140px;
    height: 170px;
  }

  .gameCard {
    width: 300px;
    height: 180px;
  }

  .cardContent {
    padding: 12px;
  }

  .categoryTournament {
    font-size: 9px;
  }

  .timeSection {
    font-size: 11px;
  }

  .liveIndicator {
    padding: 3px 6px;
    font-size: 9px;
  }

  .teamLogo {
    width: 32px;
    height: 32px;
  }

  .teamAbbr {
    font-size: 8px;
  }

  .teamName {
    font-size: 11px;
    max-width: 70px;
  }

  .teamsSection {
    margin-top: 6px;
  }

  .vsText {
    font-size: 12px;
    padding: 3px 6px;
  }

  .sliderTrack {
    gap: 12px;
  }

  .marketButton {
    padding: 4px 6px;
    min-height: 35px;
  }

  .outcomeName {
    font-size: 9px;
  }

  .odds {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 16px auto;
  }

  .leftBlock {
    padding: 32px 20px;
    min-height: 140px;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 14px;
  }

  .title::before {
    width: 40px;
    height: 2px;
  }

  .subtitle {
    font-size: 1rem;
  }

  .footballPlayerImage {
    right: -30px;
    bottom: -10px;
    width: 120px;
    height: 150px;
  }

  .gameCard {
    width: 260px;
    height: 160px;
  }

  .categoryTournament {
    font-size: 8px;
  }

  .timeSection {
    font-size: 10px;
  }

  .liveIndicator {
    padding: 2px 5px;
    font-size: 8px;
  }

  .teamLogo {
    width: 28px;
    height: 28px;
  }

  .teamAbbr {
    font-size: 7px;
  }

  .teamName {
    font-size: 10px;
    max-width: 60px;
  }

  .teamsSection {
    margin-top: 4px;
  }

  .sliderTrack {
    gap: 8px;
  }

  .marketButton {
    padding: 3px 4px;
    min-height: 30px;
  }

  .outcomeName {
    font-size: 8px;
  }

  .odds {
    font-size: 10px;
  }
}
