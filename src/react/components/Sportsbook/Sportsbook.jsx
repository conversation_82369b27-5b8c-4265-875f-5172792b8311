import { useState, useEffect } from 'react'
import { getToken, getMarkets, getTree } from '../../api/sportbook'
import styles from './Sportsbook.module.css'

// Internationalization
export const i18n = {
  en: {
    loading_sports: 'Loading sports events...',
    failed_load_sports: 'Failed to load sports events',
    no_upcoming_events: 'No upcoming sports events',
    live: 'LIVE',
    vs: 'VS',
    started: 'Started',
    popular_matches: 'Popular Matches',
    subtitle: 'Live sports betting and upcoming matches'
  },
  tr: {
    loading_sports: 'Spor etkinlikleri yükleniyor...',
    failed_load_sports: 'Spor etkinlikleri yüklenemedi',
    no_upcoming_events: 'Yaklaşan spor etkinliği yok',
    live: 'CANLI',
    vs: 'KARŞI',
    started: 'Başladı',
    popular_matches: 'Popüler Maçlar',
    subtitle: 'Canlı spor bahisleri ve yaklaşan maçlar'
  }
}

export const Sportsbook = ({ i18n, language }) => {
  const [games, setGames] = useState([])
  const [markets, setMarkets] = useState([])
  const [outcomes, setOutcomes] = useState([])
  const [tournaments, setTournaments] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch sportsbook data
  useEffect(() => {
    const fetchSportsbookData = async () => {
      try {
        setIsLoading(true)

        // 1) First fetch the token and then the tree
        const tokens = await getToken()
        if (!tokens?.accessToken) {
          throw new Error('No access token available')
        }

        const treeData = await getTree(tokens)

        if (treeData?.games) {
          // Store tournaments data for lookup
          if (treeData.tournaments) {
            setTournaments(treeData.tournaments)
          }

          // 2) Filter by start time - upcoming games within 0-7 days
          const now = Date.now()
          const sevenDaysFromNow = now + (7 * 24 * 60 * 60 * 1000) // 7 days in milliseconds

          let filteredGames = treeData.games
            .filter(game => {
              const gameStartTime = new Date(game.start_time).getTime()

              // Include only upcoming games that start within the next 7 days
              return game.status !== 'ended' &&
                     game.status !== 'cancelled' &&
                     gameStartTime >= now && // Game hasn't started yet
                     gameStartTime <= sevenDaysFromNow // Game starts within 7 days
            })
            .sort((a, b) => {
              // Sort by sort attribute first
              if (a.sort !== undefined && b.sort !== undefined) {
                return b.sort - a.sort
              }

              // Sort by start time - earliest games first (closest to now)
              return new Date(a.start_time) - new Date(b.start_time)
            })
            .slice(0, 20) // Limit to 20 games for performance

          // For the filtered subset, fetch market outcomes
          const entityIds = filteredGames.map(game => game.id)

          const marketsData = await getMarkets(tokens, {
            entityIds,
            group: "main",
            names: true
          })

          // Store markets and outcomes from markets endpoint
          if (marketsData?.markets) {
            setMarkets(marketsData.markets)
          }
          if (marketsData?.outcomes) {
            setOutcomes(marketsData.outcomes)

            // 1) Filter out games that have no outcomes
            const gamesWithOutcomes = filteredGames.filter(game => {
              const gameMarkets = marketsData.markets.filter(market =>
                market.game_id === game.id && market.status !== 'ended'
              )

              return gameMarkets.some(market => {
                return marketsData.outcomes.some(outcome =>
                  outcome.market_id === market.id && outcome.active
                )
              })
            })

            console.dir(gamesWithOutcomes)

            setGames(gamesWithOutcomes)
          }
        }
      } catch (err) {
        console.error('Error fetching sportsbook data:', err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSportsbookData()
  }, [])

  // Helper function to format start time for upcoming games
  const formatStartTime = (startTime) => {
    const gameDate = new Date(startTime)
    const now = new Date()
    const diffInHours = (gameDate - now) / (1000 * 60 * 60)
    const diffInDays = (gameDate - now) / (1000 * 60 * 60 * 24)

    // Since we only show upcoming games, all times will be in the future
    if (diffInHours < 24) {
      // Show time for games starting today
      return gameDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffInDays < 7) {
      // Show day and time for games within a week
      return gameDate.toLocaleDateString([], {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } else {
      // Show full date for games more than a week away (shouldn't happen with our 7-day filter)
      return gameDate.toLocaleDateString([], {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    }
  }

  const formatOutcomes = (outcomes, idx) => {
    if (outcomes.length === 2) {
      return idx + 1
    } else if (outcomes.length === 3) {
      return idx === 1 ? 'X' : idx + 1
    } else {
      return idx + 1
    }
  }

  // Helper function to get team logo URL
  const getTeamLogoUrl = (teamExternalId) => {
    if (!teamExternalId) return null
    return `https://static-sportsbet.esportings.com/ls/crest/big/${teamExternalId}.png`
  }

  // Helper function to get tournament name by ID
  const getTournamentName = (tournamentId) => {
    if (!tournamentId || !tournaments.length) return null
    const tournament = tournaments.find(t => t.id === tournamentId)
    return tournament?.name || null
  }

  // Helper function to find all markets by game_id
  const getGameMarkets = (gameId) => {
    return markets.filter(market =>
      market.game_id === gameId &&
      market.status !== 'ended'
    )
  }

  // Helper function to get outcomes by market_id and determine display format
  const getDisplayOutcomes = (gameId) => {
    const gameMarkets = getGameMarkets(gameId)

    // Get all outcomes for all markets of this game
    const allOutcomes = []
    gameMarkets.forEach(market => {
      const marketOutcomes = outcomes.filter(outcome =>
        outcome.market_id === market.id && outcome.active
      )
      allOutcomes.push(...marketOutcomes)
    })

    // Check for 1x2 format (external IDs 1, 2, 3)
    const has1x2 = allOutcomes.some(o => o.external_id === '1') &&
                   allOutcomes.some(o => o.external_id === '2') &&
                   allOutcomes.some(o => o.external_id === '3')

    // Check for 2-outcome format (external IDs 4, 5)
    const has2Outcomes = allOutcomes.some(o => o.external_id === '4') &&
                         allOutcomes.some(o => o.external_id === '5')

    if (has1x2) {
      // Return 1x2 outcomes in order: 1, 3, 2 (Home, Draw, Away)
      return allOutcomes
        .filter(o => ['1', '2', '3'].includes(o.external_id))
        .sort((a, b) => {
          const order = { '1': 1, '3': 2, '2': 3 } // Home, Draw, Away
          return order[a.external_id] - order[b.external_id]
        })
        .slice(0, 3)
    } else if (has2Outcomes) {
      // Return 2 outcomes in order: 4, 5
      return allOutcomes
        .filter(o => ['4', '5'].includes(o.external_id))
        .sort((a, b) => {
          const order = { '4': 1, '5': 2 }
          return order[a.external_id] - order[b.external_id]
        })
        .slice(0, 2)
    }

    // Fallback: return first 3 active outcomes if no specific pattern found
    return allOutcomes.slice(0, 3)
  }

  // Helper function to format odds
  const formatOdds = (odds) => {
    if (!odds || odds <= 0) return '-'
    return odds.toFixed(2)
  }

  if (isLoading) {
    return (
      <div className={styles.sportsbookContainer}>
        <div className={styles.loadingState}>
          <div className={styles.loadingSpinner}></div>
          <p>{i18n['loading_sports'] || 'Loading sports events...'}</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.sportsbookContainer}>
        <div className={styles.errorState}>
          <p>{i18n['failed_load_sports'] || 'Failed to load sports events'}</p>
        </div>
      </div>
    )
  }

  if (games.length === 0) {
    return (
      <div className={styles.sportsbookContainer}>
        <div className={styles.emptyState}>
          <p>{i18n['no_upcoming_events'] || 'No upcoming sports events'}</p>
        </div>
      </div>
    )
  }

  // Duplicate games for infinite scrolling
  const duplicatedGames = [...games, ...games]

  return (
    <div className={styles.container}>
      <div className={styles.sportsbookSection}>
        {/* Left side block with title and background */}
        <div className={styles.leftBlock}>
          <div className={styles.leftBlockBackground}></div>
          <div className={styles.leftBlockContent}>
            <h2 className={styles.title}>
              {i18n['popular_matches'] || 'Popular Matches'}
            </h2>
            <p className={styles.subtitle}>
              {i18n['subtitle'] || 'Live sports betting and upcoming matches'}
            </p>
          </div>
          <div className={styles.footballPlayerImage}>
            <img
              src="https://d11caojl6op1au.cloudfront.net/img/football_player.png"
              alt="Football Player"
            />
          </div>
        </div>

        {/* Slider section */}
        <div className={styles.sliderSection}>
          <div className={styles.sportsbookContainer}>
            <div className={styles.marqueeContainer}>
              <div className={styles.sliderWrapper}>
                <div className={styles.sliderTrack}>
          {duplicatedGames.map((game, index) => (
            <div key={`${game.id}-${index}`} className={styles.gameCard}>
              <div className={styles.cardContent}>
                {/* Top row with category/tournament and time */}
                <div className={styles.topRow}>
                  <div className={styles.categoryTournament}>
                    {(game.category_name || 'Sports')}
                    {getTournamentName(game.tournament_id) && ` / ${getTournamentName(game.tournament_id)}`}
                  </div>

                  <div className={styles.timeSection}>
                    {formatStartTime(game.start_time)}
                  </div>
                </div>

                {/* Teams section */}
                <div className={styles.teamsSection}>
                  {/* Home team */}
                  <div className={styles.team}>
                    <div className={styles.teamLogo}>
                      <div className={styles.teamAbbr}>
                        {game.team_home_abbr || game.team_home_name?.substring(0, 3).toUpperCase() || 'HOM'}
                      </div>
                      {getTeamLogoUrl(game.team_home_external_id) && (
                        <img
                          src={getTeamLogoUrl(game.team_home_external_id)}
                          alt={game.team_home_name}
                          onError={(e) => {
                            e.target.style.display = 'none'
                          }}
                        />
                      )}
                    </div>
                    <span className={styles.teamName}>{game.team_home_name}</span>
                  </div>

                  {/* VS separator */}
                  <div className={styles.vsSection}>
                    <span className={styles.vsText}>{i18n['vs'] || 'VS'}</span>
                  </div>

                  {/* Away team */}
                  <div className={styles.team}>
                    <div className={styles.teamLogo}>
                      <div className={styles.teamAbbr}>
                        {game.team_away_abbr || game.team_away_name?.substring(0, 3).toUpperCase() || 'AWY'}
                      </div>
                      {getTeamLogoUrl(game.team_away_external_id) && (
                        <img
                          src={getTeamLogoUrl(game.team_away_external_id)}
                          alt={game.team_away_name}
                          onError={(e) => {
                            e.target.style.display = 'none'
                          }}
                        />
                      )}
                    </div>
                    <span className={styles.teamName}>{game.team_away_name}</span>
                  </div>
                </div>

                {/* Markets section */}
                <div className={styles.marketsSection}>
                  {(() => {
                    const displayOutcomes = getDisplayOutcomes(game.id)
                    if (displayOutcomes.length === 0) return null

                    return (
                      <div className={styles.marketGroup}>
                        {displayOutcomes.map((outcome, idx) => (
                          <button
                            key={outcome.id}
                            className={styles.marketButton}
                            onClick={() => {
                              // Redirect to sportsbook page
                              const lang = language || 'en'
                              window.location.href = `/${lang}/sportsbook`
                            }}
                          >
                            <span className={styles.outcomeName}>
                              {formatOutcomes(displayOutcomes, idx)}
                            </span>
                            <span className={styles.odds}>
                              {formatOdds(outcome.odds)}
                            </span>
                          </button>
                        ))}
                      </div>
                    )
                  })()}
                </div>

                {/* Score info (if available) */}
                {/* {game.info?.home_score !== undefined && game.info?.away_score !== undefined && (
                  <div className={styles.scoreInfo}>
                    <div className={styles.score}>
                      {game.info.home_score} - {game.info.away_score}
                    </div>
                  </div>
                )} */}
              </div>
            </div>
          ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Sportsbook
