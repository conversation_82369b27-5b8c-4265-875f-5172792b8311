import { useState, useEffect, useRef } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay } from 'swiper/modules'
import { getPromotionCards } from '../../api/promotions'

// Import Swiper styles
import 'swiper/swiper-bundle.css'
import styles from './PromotionCards.module.css'

export const i18n = {
  en: {
    title: 'Promotions',
    subtitle: 'Discover our latest offers and bonuses',
    loading: 'Loading promotions...',
    error: 'Failed to load promotions. Please try again later.',
    authError: 'Please log in to view promotions.',
    noPromotions: 'No promotions available at the moment.',
    viewPromotion: 'View Promotion',
    viewAll: 'View All',
    learnMore: 'Learn more...',
    promotion: 'PROMOTION',
    reviewNow: 'REVIEW NOW'
  },
  tr: {
    title: 'Promosyonlar',
    subtitle: 'En son tekliflerimizi ve bonuslarımızı keşfedin',
    loading: 'Promosyonlar yükleniyor...',
    error: 'Promosyonlar yüklenemedi. Lütfen daha sonra tekrar deneyin.',
    authError: 'Promosyonları görüntülemek için lütfen giriş yapın.',
    noPromotions: 'Şu anda mevcut promosyon bulunmamaktadır.',
    viewPromotion: 'Promosyonu Görüntüle',
    viewAll: 'Tümünü Görüntüle',
    learnMore: 'Daha fazla...',
    promotion: 'PROMOSYON',
    reviewNow: 'ŞİMDİ İNCELE'
  }
}

const PromotionCards = ({ i18n = {}, language = 'en' }) => {
  const [promotions, setPromotions] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [animatedCards, setAnimatedCards] = useState(new Set())
  const [activeSlide, setActiveSlide] = useState(0)
  const [swiperInstance, setSwiperInstance] = useState(null)
  const containerRef = useRef(null)

  useEffect(() => {
    const fetchPromotions = async () => {
      try {
        setIsLoading(true)
        setError(null)
        const data = await getPromotionCards({ language })

        setPromotions(data || [])
      } catch (err) {
        console.error('Failed to fetch promotions:', err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPromotions()
  }, [language])

  // Fallback: ensure all cards are visible after promotions load
  useEffect(() => {
    if (!isLoading && promotions.length > 0) {
      // Set a fallback timeout to ensure all cards are visible
      const fallbackTimeout = setTimeout(() => {
        const allIndices = promotions.map((_, index) => index)
        setAnimatedCards(new Set(allIndices))
      }, 1000) // 1 second fallback

      return () => clearTimeout(fallbackTimeout)
    }
  }, [isLoading, promotions])

  // Intersection Observer for scroll animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isLoading && promotions.length > 0) {
            // Animate all promotion cards
            promotions.forEach((_, index) => {
              setTimeout(() => {
                setAnimatedCards(prev => new Set([...prev, index]))
              }, index * 100) // 100ms delay between each card
            })
          }
        })
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current)
      }
    }
  }, [isLoading, promotions])

  // Navigation functions
  const handlePrevSlide = () => {
    if (swiperInstance) {
      swiperInstance.slidePrev()
    }
  }

  const handleNextSlide = () => {
    if (swiperInstance) {
      swiperInstance.slideNext()
    }
  }

  const handleSlideChange = (swiper) => {
    setActiveSlide(swiper.realIndex)
  }

  const handleCardClick = (slug) => {
    // Navigate to promotion page
    const currentUrl = window.location.origin
    const promotionUrl = `${currentUrl}/${language}/promotion/${slug}`
    window.open(promotionUrl, '_blank')
  }

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>
            {i18n.title || 'Promotions'}
          </h2>
        </div>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p className={styles.loadingText}>
            {i18n.loading || 'Loading promotions...'}
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>
            {i18n.title || 'Promotions'}
          </h2>
        </div>
        <div className={styles.errorContainer}>
          <p className={styles.errorText}>
            {error.includes('Authentication required')
              ? (i18n.authError || 'Please log in to view promotions.')
              : (i18n.error || 'Failed to load promotions. Please try again later.')
            }
          </p>
        </div>
      </div>
    )
  }

  if (!promotions.length) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>
            {i18n.title || 'Promotions'}
          </h2>
        </div>
        <div className={styles.emptyContainer}>
          <p className={styles.emptyText}>
            {i18n.noPromotions || 'No promotions available at the moment.'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          {i18n.title || 'Promotions'}
        </h2>
        <p className={styles.subtitle}>
          {i18n.subtitle || 'Discover our latest offers and bonuses'}
        </p>
      </div>

      <div className={styles.promotionsSlider}>
        <div className={styles.sliderWrapper}>
          <Swiper
            modules={[Autoplay]}
            spaceBetween={20}
            slidesPerView={3}
            centeredSlides={false}
            loop={promotions.length > 3}
            autoplay={promotions.length > 3 ? {
              delay: 5000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true
            } : false}
            speed={600}
            onSwiper={setSwiperInstance}
            onSlideChange={handleSlideChange}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 16,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 18,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 20,
              },
            }}
            className={styles.swiperContainer}
          >
          {promotions.map((promotion, index) => (
            <SwiperSlide key={promotion.slug + index} className={styles.swiperSlideItem}>
              <div className={styles.slideContent}>
                <div className={`${styles.promotionCard} ${animatedCards.has(index) ? styles.animate : ''} ${index === activeSlide ? styles.activeSlide : ''}`}>
                  <div className={styles.cardContent}>
                    <div className={styles.textSection}>
                      <div className={styles.promotionMark}>
                        {i18n.promotion || 'PROMOTION'}
                      </div>
                      <h3 className={styles.cardTitle}>
                        {promotion.title}
                      </h3>
                      {promotion.description && (
                        <p className={styles.description}>
                          {promotion.description}
                        </p>
                      )}
                      <button
                        className={styles.reviewButton}
                        onClick={() => handleCardClick(promotion.slug)}
                        type="button"
                      >
                        {i18n.reviewNow || 'REVIEW NOW'}
                      </button>
                    </div>
                    <div className={styles.imageSection}>
                      <div className={styles.imageContainer}>
                        <img
                          src={promotion.image}
                          alt={promotion.title}
                          className={`${styles.image} ${language}`}
                          loading="lazy"
                          onError={(e) => {
                            e.target.style.display = 'none'
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
          </Swiper>

          {/* Custom Navigation Buttons */}
          <button
            className={styles.customPrevButton}
            type="button"
            aria-label="Previous slide"
            onClick={handlePrevSlide}
            disabled={!swiperInstance}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          <button
            className={styles.customNextButton}
            type="button"
            aria-label="Next slide"
            onClick={handleNextSlide}
            disabled={!swiperInstance}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
}

export { PromotionCards }
export default PromotionCards
