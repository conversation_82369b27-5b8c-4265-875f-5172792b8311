.container {
  width: 100%;
  margin: 40px 0;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(24, 207, 251, 0.3);
}

.subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
}











/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(24, 207, 251, 0.2);
  border-top: 3px solid #18cffb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes cardFadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.loadingText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin: 0;
}

/* Error State */
.errorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.errorText {
  color: #ff6b6b;
  font-size: 1rem;
  text-align: center;
  margin: 0;
}

/* Empty State */
.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.emptyText {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1rem;
  text-align: center;
  margin: 0;
}

/* ===== PROMOTIONS SLIDER STYLES ===== */

/* Main slider container */
.promotionsSlider {
  position: relative;
  width: 100%;
  margin-top: 32px;
  /* Space for navigation buttons */
}

/* Slider wrapper with navigation buttons */
.sliderWrapper {
  position: relative;
  width: 100%;
  /* No padding - buttons will be positioned outside */
}

/* Swiper container */
.swiperContainer {
  width: 100%;
  height: auto;
  padding: 20px 4px 20px 4px;
  box-sizing: border-box;
  overflow: hidden !important;
}

/* Individual slide wrapper */
.swiperSlideItem {
  display: flex;
  align-items: stretch;
}

/* Slide content wrapper */
.slideContent {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Promotion card container */
.promotionCard {
  width: 100%;
  height: 100%;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
  animation: cardFadeInUp 0.6s ease-out;
}

.promotionCard.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Active slide indicator */
.promotionCard.activeSlide .cardContent {
  border-color: rgba(24, 207, 251, 0.4);
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.12), rgba(10, 195, 198, 0.06));
}

/* Card content */
.cardContent {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.08), rgba(10, 195, 198, 0.04));
  border: 1px solid rgba(24, 207, 251, 0.2);
  border-radius: 20px;
  padding: 24px;
  height: 100%;
  display: flex;
  align-items: center;
  gap: 24px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
}

.cardContent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.02), transparent);
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.promotionCard:hover .cardContent {
  transform: translateY(-4px);
  border-color: rgba(24, 207, 251, 0.4);
}

.promotionCard:hover .cardContent::before {
  opacity: 1;
}

/* Text section */
.textSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 0;
}

/* Promotion mark badge */
.promotionMark {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  color: #061d2b;
  font-size: 0.6rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  padding: 4px 10px;
  border-radius: 15px;
  display: inline-block;
  width: fit-content;
  position: relative;
  overflow: hidden;
}

.promotionMark::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.promotionCard:hover .promotionMark::before {
  left: 100%;
}

/* Card title */
.cardTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Description */
.description {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.75);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Review button */
.reviewButton {
  background: transparent;
  color: #18cffb;
  border: 1px solid #18cffb;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.65rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  width: fit-content;
  margin-top: auto;
  backdrop-filter: blur(8px);
}

.reviewButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  transition: left 0.3s ease;
  z-index: -1;
}

.reviewButton:hover {
  color: #061d2b;
  border-color: #18cffb;
  transform: translateY(-2px);
}

.reviewButton:hover::before {
  left: 0;
}

.reviewButton:active {
  transform: translateY(0);
}

/* Image section */
.imageSection {
  flex: 0 0 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.imageContainer {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1), rgba(10, 195, 198, 0.05));
  border: 2px solid rgba(24, 207, 251, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.imageContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.promotionCard:hover .imageContainer {
  border-color: rgba(24, 207, 251, 0.6);
  transform: scale(1.05);
}

.promotionCard:hover .imageContainer::before {
  opacity: 1;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 20px;
}

.promotionCard:hover .image {
  transform: scale(1.1);
}

/* ===== CUSTOM NAVIGATION BUTTONS ===== */

/* Custom navigation buttons positioned outside overflow container */
.customNextButton,
.customPrevButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 52px;
  height: 52px;
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.1), rgba(10, 195, 198, 0.05));
  border: 2px solid rgba(24, 207, 251, 0.3);
  border-radius: 50%;
  color: #18cffb;
  backdrop-filter: blur(12px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
}

.customNextButton:hover,
.customPrevButton:hover {
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.2), rgba(10, 195, 198, 0.1));
  border-color: rgba(24, 207, 251, 0.6);
  transform: translateY(-50%) scale(1.1);
}

.customNextButton:active,
.customPrevButton:active {
  transform: translateY(-50%) scale(0.95);
}

.customNextButton:disabled,
.customPrevButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(24, 207, 251, 0.05);
  border-color: rgba(24, 207, 251, 0.1);
}

.customNextButton:disabled:hover,
.customPrevButton:disabled:hover {
  transform: translateY(-50%);
  background: rgba(24, 207, 251, 0.05);
  border-color: rgba(24, 207, 251, 0.1);
}

.customNextButton {
  right: -24px;
}

.customPrevButton {
  left: -24px;
}

.customNextButton svg,
.customPrevButton svg {
  transition: transform 0.2s ease;
}

.customNextButton:hover svg,
.customPrevButton:hover svg {
  transform: scale(1.1);
}

.customNextButton:disabled svg,
.customPrevButton:disabled svg {
  transform: none;
}



/* ===== RESPONSIVE DESIGN ===== */

/* Tablet styles */
@media (max-width: 768px) {
  .container {
    margin: 20px auto;
    padding: 0 16px;
    max-width: none;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .promotionsSlider {
    padding: 0 60px;
    /* Reduced padding for tablet */
  }

  .cardContent {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 20px;
    min-height: auto;
  }

  .textSection {
    order: 2;
    gap: 16px;
    align-items: center;
  }

  .imageSection {
    order: 1;
    flex: 0 0 auto;
  }

  .imageContainer {
    width: 200px;
    height: 200px;
  }

  .cardTitle {
    font-size: 1rem;
    text-align: center;
  }

  .description {
    font-size: 0.7rem;
    text-align: center;
  }

  .reviewButton {
    padding: 4px 10px;
    font-size: 0.6rem;
  }

  .promotionMark {
    font-size: 0.55rem;
    padding: 3px 8px;
  }

  /* Smaller navigation buttons on tablet */
  .customNextButton,
  .customPrevButton {
    width: 44px;
    height: 44px;
  }

  .customNextButton {
    right: -50px;
  }

  .customPrevButton {
    left: -50px;
  }

  .customNextButton svg,
  .customPrevButton svg {
    width: 18px;
    height: 18px;
  }
}

/* Mobile styles */
@media (max-width: 480px) {
  .container {
    margin: 16px auto;
    padding: 0 12px;
    max-width: none;
  }

  .title {
    font-size: 1.8rem;
  }

  .header {
    margin-bottom: 24px;
  }

  .promotionsSlider {
    padding: 0;
    /* No padding on mobile */
  }

  .cardContent {
    padding: 18px;
    gap: 18px;
  }

  .imageContainer {
    width: 160px;
    height: 160px;
  }

  .cardTitle {
    font-size: 0.95rem;
  }

  .description {
    font-size: 0.65rem;
  }

  .reviewButton {
    padding: 3px 8px;
    font-size: 0.55rem;
  }

  .promotionMark {
    font-size: 0.5rem;
    padding: 2px 6px;
  }

  /* Hide navigation buttons on mobile for cleaner look */
  .customNextButton,
  .customPrevButton {
    display: none;
  }
}

/* ===== ADDITIONAL ENHANCEMENTS ===== */

/* Swiper container enhancements */
.promotionsSlider :global(.swiper) {
  overflow: visible;
  border-radius: 16px;
}

.promotionsSlider :global(.swiper-wrapper) {
  align-items: stretch;
}

.promotionsSlider :global(.swiper-slide) {
  transition: transform 0.3s ease;
  will-change: transform;
}

/* Active slide enhancement */
.promotionsSlider :global(.swiper-slide-active) .slideContent {
  transform: scale(1.02);
}

/* Hover effects for non-active slides */
.promotionsSlider :global(.swiper-slide:not(.swiper-slide-active):hover) .slideContent {
  transform: scale(1.01);
}

/* Disable default Swiper navigation buttons */
.promotionsSlider :global(.swiper-button-next),
.promotionsSlider :global(.swiper-button-prev) {
  display: none !important;
}

/* Loading state */
.promotionsSlider.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Animation keyframes */
@keyframes cardFadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Shimmer effect for loading */
.promotionCard.loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
