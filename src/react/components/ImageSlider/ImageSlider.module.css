/* ImageSlider Component Styles - CSS Module Version */

.imageSlider {
  width: 100%;
  /* No background, no padding, no borders */
  overflow: visible;
  /* Ensure hover effects aren't cut off */
}

/* Swiper container */
.swiperContainer {
  height: 100%;
  width: 100%;
  padding: 10px 0;
}

.swiperSlideItem {
  position: relative;
  height: 100%;
  width: auto !important;
  /* Force auto width to match image */
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 4px;
  /* Reduced horizontal padding for smaller gap */
}

.slideContent {
  position: relative;
  height: 100%;
  width: auto;
  /* Match image width exactly */
  display: inline-block;
}

.slideLink {
  text-decoration: none;
  transition: transform 0.3s ease-out;
  display: inline-block;
  height: 100%;
  width: auto;
  /* Match image width exactly */
  will-change: transform;
}

.slideLink:hover {
  transform: translateY(-2px);
}

.slideLink:hover .slideImage {
  transform: scale(1.05);
}

.slideImage {
  transition: transform 0.3s ease-out;
  border-radius: 8px;
  width: auto;
  /* Natural width - slide will match this */
  height: 100%;
  /* Limit height only */
  max-height: 100%;
  object-fit: contain;
  /* Maintain aspect ratio */
  display: block;
}

/* Text-related styles removed since we're not displaying text */

/* Custom Swiper Styles - Casino Theme */
:global(.swiper-button-next),
:global(.swiper-button-prev) {
  color: #feca57 !important;
  background: linear-gradient(135deg, #181c32 0%, #2a2f4a 100%) !important;
  border: 2px solid rgba(254, 202, 87, 0.3) !important;
  border-radius: 50% !important;
  width: 48px !important;
  height: 48px !important;
  margin-top: -24px !important;
  /* No shadow */
  transition: all 0.3s ease !important;
}

:global(.swiper-button-next:after),
:global(.swiper-button-prev:after) {
  font-size: 16px !important;
  font-weight: bold !important;
}

:global(.swiper-button-next:hover),
:global(.swiper-button-prev:hover) {
  background: linear-gradient(135deg, #2a2f4a 0%, #3a3f5a 100%) !important;
  border-color: #feca57 !important;
  transform: scale(1.1) !important;
  /* No shadow */
}

:global(.swiper-pagination-bullet) {
  background: rgba(255, 255, 255, 0.3) !important;
  opacity: 1 !important;
  width: 10px !important;
  height: 10px !important;
  border: 1px solid rgba(254, 202, 87, 0.2) !important;
  transition: all 0.3s ease !important;
  margin: 0 4px !important;
}

:global(.swiper-pagination-bullet-active) {
  background: linear-gradient(45deg, #feca57, #ff6b6b) !important;
  border-color: #feca57 !important;
  transform: scale(1.2) !important;
  /* No shadow */
}

/* Responsive Styles */
@media (max-width: 768px) {
  .swiperSlideItem {
    padding: 0;
    /* No padding on mobile for full width */
    width: 100% !important;
    /* Full width on mobile */
  }

  .slideContent {
    width: 100%;
    /* Full width content on mobile */
  }

  .slideLink {
    width: 100%;
    /* Full width link on mobile */
  }

  .slideImage {
    width: 100%;
    /* Full width image on mobile */
    height: auto;
    /* Auto height on mobile - no height limitation */
    max-height: none;
    /* Remove height limitation on mobile */
    object-fit: cover;
    /* Cover for full width on mobile */
  }

  :global(.swiper-button-next),
  :global(.swiper-button-prev) {
    width: 40px !important;
    height: 40px !important;
    margin-top: -20px !important;
  }

  :global(.swiper-button-next:after),
  :global(.swiper-button-prev:after) {
    font-size: 14px !important;
  }

  :global(.swiper-pagination-bullet) {
    width: 8px !important;
    height: 8px !important;
    margin: 0 3px !important;
  }

  :global(.swiper-pagination-bullet-active) {
    transform: scale(1.1) !important;
  }
}

/* Enhanced Swiper customizations */
.imageSlider :global(.swiper-pagination) {
  bottom: 15px !important;
  z-index: 10;
  padding: 5px 0 !important;
  height: auto !important;
}

.imageSlider :global(.swiper-pagination-bullet) {
  margin: 0 4px !important;
}

.imageSlider :global(.swiper-pagination-bullet:hover) {
  transform: scale(1.3);
  background: rgba(254, 202, 87, 0.7) !important;
  /* No shadow */
}

/* Smooth loading animation */
.imageSlider .slideImage {
  opacity: 0;
  animation: fadeInSlide 0.4s ease-out forwards;
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: scale(1.02) translateY(5px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Loading state with casino theme */
.imageSlider.loading {
  background: linear-gradient(90deg,
      #181c32 25%,
      #2a2f4a 50%,
      #181c32 75%);
  background-size: 200% 100%;
  animation: casinoLoading 2s infinite;
}

@keyframes casinoLoading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

/* Swiper container enhancements */
.imageSlider :global(.swiper) {
  border-radius: 12px;
  overflow: hidden;
}

.imageSlider :global(.swiper-slide) {
  transition: transform 0.3s ease-out;
  will-change: transform;
}

.imageSlider :global(.swiper-slide-active) .slideContent {
  transform: scale(1.02);
  /* No shadow */
}

/* Hover effects for non-active slides */
.imageSlider :global(.swiper-slide:not(.swiper-slide-active):hover) .slideContent {
  transform: scale(1.01);
  /* No shadow */
}

/* Text styles removed since we're not displaying text */
