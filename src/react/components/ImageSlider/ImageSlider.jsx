import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';
import styles from './ImageSlider.module.css';

// Internationalization
export const i18n = {
  en: {
    live_casino: 'Live Casino',
    live_casino_subtitle: 'Real Dealers',
    sports_betting: 'Sports Betting',
    sports_betting_subtitle: 'Live Betting Excitement',
    slot_games: 'Slot Games',
    slot_games_subtitle: 'Best Slot Experience',
    aviator_game: 'Aviator',
    aviator_subtitle: 'Flight Game Excitement',
    virtual_sports: 'Virtual Sports',
    virtual_sports_subtitle: 'Virtual Game Excitement',
    bonuses: 'Bonuses',
    bonuses_subtitle: 'Special Bonuses'
  },
  tr: {
    live_casino: 'Canlı Casino',
    live_casino_subtitle: 'Gerçek Krupiyeler',
    sports_betting: 'Spor Bahisleri',
    sports_betting_subtitle: 'Canlı Bahis Heyecanı',
    slot_games: 'Slot Oyunları',
    slot_games_subtitle: 'En İyi Slot Deneyimi',
    aviator_game: 'Aviator',
    aviator_subtitle: 'Uçak Oyunu Heyecanı',
    virtual_sports: 'Sanal Spor',
    virtual_sports_subtitle: 'Sanal Oyun Heyecanı',
    bonuses: 'Promosyonlar',
    bonuses_subtitle: 'Özel Bonuslar'
  }
};

const ImageSlider = ({
  i18n: translations = {},
  language = 'en',
  images = [],
  autoplay = true,
  navigation = true,
  pagination = true,
  loop = true,
  effect = 'slide',
  className = '',
  height = '400px'
}) => {
  // Default images with internationalization
  const defaultImages = [
    {
      id: 1,
      src: 'https://betrozgiris.com/img/fast-access/canli-casino.png',
      alt: translations.live_casino || 'Live Casino',
      title: translations.live_casino || 'Live Casino',
      subtitle: translations.live_casino_subtitle || 'Real Dealers',
      link: `/${language}/casino/group/lobby`
    },
    {
      id: 2,
      src: 'https://betrozgiris.com/img/fast-access/spor-bahis.png',
      alt: translations.sports_betting || 'Sports Betting',
      title: translations.sports_betting || 'Sports Betting',
      subtitle: translations.sports_betting_subtitle || 'Live Betting Excitement',
      link: `/${language}/sportsbook`
    },
    {
      id: 3,
      src: 'https://betrozgiris.com/img/fast-access/slot-oyun.png',
      alt: translations.slot_games || 'Slot Games',
      title: translations.slot_games || 'Slot Games',
      subtitle: translations.slot_games_subtitle || 'Best Slot Experience',
      link: `/${language}/casino/group/lobby`
    },
    {
      id: 4,
      src: 'https://betrozgiris.com/img/fast-access/aviator.png',
      alt: translations.aviator_game || 'Aviator',
      title: translations.aviator_game || 'Aviator',
      subtitle: translations.aviator_subtitle || 'Flight Game Excitement',
      link: `/${language}/games/aviator`
    },
    {
      id: 5,
      src: 'https://betrozgiris.com/img/fast-access/sanal-spor.png',
      alt: translations.virtual_sports || 'Virtual Sports',
      title: translations.virtual_sports || 'Virtual Sports',
      subtitle: translations.virtual_sports_subtitle || 'Virtual Game Excitement',
      link: `/${language}/games/virtual`
    },
    {
      id: 6,
      src: 'https://betrozgiris.com/img/fast-access/bonus.png',
      alt: translations.bonuses || 'Bonuses',
      title: translations.bonuses || 'Bonuses',
      subtitle: translations.bonuses_subtitle || 'Special Bonuses',
      link: `/${language}/promotions`
    }
  ];

  const slideImages = images.length > 0 ? images : defaultImages;

  const swiperConfig = {
    modules: [Navigation, Pagination, Autoplay, EffectFade],
    slidesPerView: 'auto', // Auto width based on image size
    spaceBetween: 8, // Reduced spacing between slides
    loop: loop,
    effect: effect,
    navigation: navigation,
    pagination: pagination ? {
      clickable: true,
      dynamicBullets: true
    } : false,
    autoplay: autoplay ? {
      delay: 4000,
      disableOnInteraction: false,
      pauseOnMouseEnter: true
    } : false,
    speed: 600, // Smooth animation speed
    breakpoints: {
      // Mobile: 1 full-width slide
      320: {
        slidesPerView: 1,
        spaceBetween: 0
      },
      // Tablet: auto width with small spacing
      768: {
        slidesPerView: 'auto',
        spaceBetween: 8
      },
      // Desktop: auto width with small spacing
      1024: {
        slidesPerView: 'auto',
        spaceBetween: 12
      }
    }
  };

  return (
    <div className={`${styles.imageSlider} ${className}`} style={{ height }}>
      <Swiper {...swiperConfig} className={styles.swiperContainer}>
        {slideImages.map((image) => (
          <SwiperSlide key={image.id} className={styles.swiperSlideItem}>
            <div className={styles.slideContent}>
              {image.link ? (
                <a href={image.link} className={styles.slideLink}>
                  <img
                    src={image.src}
                    alt={image.alt}
                    className={styles.slideImage}
                    loading="lazy"
                  />
                </a>
              ) : (
                <img
                  src={image.src}
                  alt={image.alt}
                  className={styles.slideImage}
                  loading="lazy"
                />
              )}
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

// Export the component as default and stylesheet as named export
export default ImageSlider;
