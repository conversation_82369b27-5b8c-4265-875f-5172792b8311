import {useState, useEffect} from "react";
import missionIcon from "../../assets/mission.svg?raw";
import { getMissionsData, participateInMission, completeMission } from "../../api/missions";
import { useMissionResetTimes } from "../../hooks/useMissionResetTimes";
import styles from "./MissionsModal.module.css";

// Sample mission data based on the screenshot
const sampleMissions = [
  {
    id: 1,
    title: "Play 370x on The Dog House - Muttley Crew",
    description:
        "Play 370 consecutive rounds on The Dog House - Muttley Crew and get 1.000 BYTES! Minimum bet: 0,20€ per round.",
    reward: 1000,
    progress: 0,
    target: 370,
    gameHighlight: "The Dog House - Muttley Crew",
    minBet: "0,20€",
    type: "consecutive",
  },
  {
    id: 2,
    title: "HIT of 450x on 3 Pyramids of Gold",
    description:
        "Make a HIT on 3 Pyramids of Gold and get 2.500 BYTES! Minimum multiplier: 450x.",
    reward: 2500,
    progress: 0,
    target: 1,
    gameHighlight: "3 Pyramids of Gold",
    minMultiplier: "450x",
    type: "hit",
  },
  {
    id: 3,
    title: "Bet 500€ on Candy Blitz",
    description:
        "Place a SINGLE high bet on Candy Blitz and get 5.000 BYTES! Minimum bet: 500€.",
    reward: 5000,
    progress: 0,
    target: 1,
    gameHighlight: "Candy Blitz",
    minBet: "500€",
    type: "single_bet",
  },
  {
    id: 4,
    title: "Play 40x on Betify Blackjack",
    description:
        "Play 40 consecutive rounds on Betify Blackjack and get 1.000 BYTES! Minimum bet: 5€ per round.",
    reward: 1000,
    progress: 0,
    target: 40,
    gameHighlight: "Betify Blackjack",
    minBet: "5€",
    type: "consecutive",
  },
  {
    id: 5,
    title: "Complete all missions",
    description: "Complete all missions and get extra bonus of 2.500 BYTES.",
    reward: 2500,
    progress: 0,
    target: 4,
    type: "complete_all",
  },
];

const MissionsModal = ({i18n = {}, language = "en"}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [missionsData, setMissionsData] = useState(null);
  const [activeTab, setActiveTab] = useState('daily');
  const [userBalance, setUserBalance] = useState(1250); // User's diamond balance

  // Use the reset times hook
  const { countdowns, loading: resetTimesLoading, error: resetTimesError } = useMissionResetTimes();

  // Load missions data
  useEffect(() => {
    const loadMissions = async () => {
      try {
        console.log('MissionsModal: Loading missions with extended user creation...');
        const data = await getMissionsData({ language });
        setMissionsData(data);
        setUserBalance(data.stats.diamonds);
        console.log('MissionsModal: Missions loaded successfully');
      } catch (error) {
        console.error("Failed to load missions:", error);
        // Fallback to sample data
        setMissionsData({
          dailyMissions: sampleMissions.slice(0, 4),
          weeklyMissions: sampleMissions.slice(0, 3),
          monthlyMissions: sampleMissions.slice(0, 2),
          stats: { diamonds: 1250 }
        });
      }
    };

    loadMissions();
  }, [language]);



  // URL parameter handling for modal state
  useEffect(() => {
    const checkModalState = () => {
      const urlParams = new URLSearchParams(window.location.search);
      setIsModalOpen(urlParams.get("modal") === "missions");
    };

    checkModalState();
    window.addEventListener("popstate", checkModalState);

    return () => window.removeEventListener("popstate", checkModalState);
  }, []);

  useEffect(() => {
    const menu = document.querySelector(".header__user-menu");
    const menuItem = menu.querySelector("li:nth-child(5)");
    const clonedMenuItem = menuItem.cloneNode(true);

    const a = clonedMenuItem.querySelector("a");
    for (const node of a.childNodes) {
      if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
        node.textContent = "Missions";
      }
    }

    const tmp = document.createElement("div");
    tmp.innerHTML = missionIcon;
    const newSvg = tmp.querySelector("svg");
    newSvg.classList.add("svg-icon");
    newSvg.style.strokeWidth = "0.3px";

    a.querySelector("svg").replaceWith(newSvg);
    a.href = `/${language}/?modal=missions`;
    a.addEventListener("click", (e) => {
      e.preventDefault();
      e.stopPropagation();
      const url = new URL(window.location);
      url.searchParams.set("modal", "missions");
      window.history.pushState({}, "", url);
      setIsModalOpen(true);
    });
    menu.insertBefore(clonedMenuItem, menuItem);
  }, []);

  useEffect(() => {
    if (isModalOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }, [isModalOpen])

  const closeModal = () => {
    setIsModalOpen(false);
    const url = new URL(window.location);
    url.searchParams.delete("modal");
    window.history.pushState({}, "", url);
  };

  // Calculate mission state based on assignments (same logic as missions page)
  const calculateMissionState = (mission) => {
    if (!mission.userAssignments || mission.userAssignments.length === 0) {
      return 'not_started';
    }

    // Check if all assignments are complete
    const allComplete = mission.objectives.every(objective => {
      const assignment = mission.userAssignments.find(
          assignment => assignment.missionObjectiveId === objective.id
      );

      if (!assignment) return false;

      // Use the isCompleted field if available, otherwise fallback to calculation
      if (assignment.hasOwnProperty('isCompleted')) {
        return assignment.isCompleted;
      }

      // Fallback to manual calculation for backward compatibility
      const progress = parseInt(assignment.progress || '0');
      const target = parseInt(objective.targetValue || '1');

      // Check completion based on operator
      switch (objective.operator) {
        case 'ge': return progress >= target;
        case 'gt': return progress > target;
        case 'le': return progress <= target;
        case 'lt': return progress < target;
        case 'eq': return progress === target;
        default: return progress >= target;
      }
    });

    return allComplete ? 'complete' : 'incomplete';
  };

  // Handle mission participation
  const handleParticipate = async (missionId) => {
    try {
      console.log('Participating in mission:', missionId);

      const result = await participateInMission({ missionId });

      if (result.success) {
        console.log('Successfully participated in mission:', result);

        // Refresh missions data
        const data = await getMissionsData({ language });
        setMissionsData(data);
        setUserBalance(data.stats.diamonds);

        console.log('Missions data refreshed after participation');
      } else {
        throw new Error(result.message || 'Participation failed');
      }
    } catch (error) {
      console.error('Failed to participate in mission:', error);
      alert(`Failed to participate: ${error.message}`);
    }
  };

  // Handle mission claim
  const handleClaimReward = async (missionId) => {
    try {
      console.log('Claiming reward for mission:', missionId);

      const result = await completeMission({ missionId });

      if (result.success) {
        console.log('Successfully claimed mission reward:', result);

        // Refresh missions data
        const data = await getMissionsData({ language });
        setMissionsData(data);
        setUserBalance(data.stats.diamonds);

        console.log('Missions data refreshed after claim');
      } else {
        throw new Error(result.message || 'Claim failed');
      }
    } catch (error) {
      console.error('Failed to claim mission reward:', error);
      alert(`Failed to claim reward: ${error.message}`);
    }
  };

  const formatTime = (value) => value.toString().padStart(2, "0");

  if (!isModalOpen) {
    return null;
  }

  // Show loading state if missions data is not loaded yet
  if (!missionsData) {
    return (
        <div className={styles.modalOverlay} onClick={closeModal}>
          <div className={styles.loadingModalContent} onClick={(e) => e.stopPropagation()}>
            <div className={styles.modalHeader}>
              <button className={styles.closeButton} onClick={closeModal}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                      d="M18 6L6 18M6 6L18 18"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner}></div>
              <p className={styles.loadingText}>Loading missions...</p>
            </div>
          </div>
        </div>
    );
  }

  return <>
    <div className={styles.modalOverlay} onClick={closeModal}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        {/* Modal Header */}
        <div className={styles.modalHeader}>
          <button className={styles.closeButton} onClick={closeModal}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                  d="M18 6L6 18M6 6L18 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
              />
            </svg>
          </button>
        </div>

        {/* Missions Header */}
        <div className={styles.missionsHeader}>
          {/* Big Reward Banner - Top Right Corner */}
          <div className={styles.bigRewardBanner}>
            <div className={styles.bigRewardText}>Big</div>
            <div className={styles.bigRewardText}>Reward</div>
            <div className={styles.completionBadge}>
              {missionsData ? (() => {
                const currentMissions = activeTab === 'daily' ? (missionsData.dailyMissions || []) :
                                      activeTab === 'weekly' ? (missionsData.weeklyMissions || []) :
                                      (missionsData.monthlyMissions || []);
                const completed = currentMissions.filter(m => {
                  const missionState = calculateMissionState(m);
                  return missionState === 'complete' || m.claimed;
                }).length;
                return `${completed}/${currentMissions.length}`;
              })() : '0/0'}
            </div>
          </div>

          <div className={styles.headerContent}>
            {/* Top Row */}
            <div className={styles.topRow}>
              {/* Top Left - Mission Type and Title */}
              <div className={styles.topLeft}>
                <div className={styles.missionTypeLabel}>
                  {activeTab === 'daily' ? (language === 'tr' ? 'GÜNLÜK' : 'DAILY') :
                   activeTab === 'weekly' ? (language === 'tr' ? 'HAFTALIK' : 'WEEKLY') :
                   (language === 'tr' ? 'AYLIK' : 'MONTHLY')}
                </div>
                <h1 className={styles.missionsTitle}>Missions</h1>
                <p className={styles.subtitle}>
                  {language === 'tr' ? 'Elmas ödülleri kazanmak için görevleri tamamlayın' : 'Complete missions to earn diamond rewards'}
                </p>
              </div>


            </div>

            {/* Bottom Row */}
            <div className={styles.bottomRow}>
              {/* Bottom Left - Timer */}
              <div className={styles.bottomLeft}>
                <div className={styles.timerContainer}>
                  {resetTimesLoading ? (
                    <div className={styles.timerLoading}>
                      {language === 'tr' ? 'Yükleniyor...' : 'Loading...'}
                    </div>
                  ) : resetTimesError ? (
                    <div className={styles.timerError}>
                      {language === 'tr' ? 'Hata' : 'Error'}
                    </div>
                  ) : (() => {
                    const currentCountdown = countdowns[activeTab];
                    if (activeTab === 'daily') {
                      return (
                        <>
                          <div className={styles.timeBlock}>
                            <span className={styles.timeValue}>
                              {formatTime(currentCountdown.hours)}
                            </span>
                            <span className={styles.timeLabel}>HH</span>
                          </div>
                          <span className={styles.timeSeparator}>:</span>
                          <div className={styles.timeBlock}>
                            <span className={styles.timeValue}>
                              {formatTime(currentCountdown.minutes)}
                            </span>
                            <span className={styles.timeLabel}>MM</span>
                          </div>
                          <span className={styles.timeSeparator}>:</span>
                          <div className={styles.timeBlock}>
                            <span className={styles.timeValue}>
                              {formatTime(currentCountdown.seconds)}
                            </span>
                            <span className={styles.timeLabel}>SS</span>
                          </div>
                        </>
                      );
                    } else {
                      return (
                        <>
                          <div className={styles.timeBlock}>
                            <span className={styles.timeValue}>
                              {currentCountdown.days}
                            </span>
                            <span className={styles.timeLabel}>DD</span>
                          </div>
                          <span className={styles.timeSeparator}>:</span>
                          <div className={styles.timeBlock}>
                            <span className={styles.timeValue}>
                              {formatTime(currentCountdown.hours)}
                            </span>
                            <span className={styles.timeLabel}>HH</span>
                          </div>
                          <span className={styles.timeSeparator}>:</span>
                          <div className={styles.timeBlock}>
                            <span className={styles.timeValue}>
                              {formatTime(currentCountdown.minutes)}
                            </span>
                            <span className={styles.timeLabel}>MM</span>
                          </div>
                        </>
                      );
                    }
                  })()}
                </div>
              </div>

              {/* Bottom Center - Progress Train */}
              <div className={styles.bottomCenter}>
                <div className={styles.progressTrain}>
                  {missionsData && (() => {
                    const currentMissions = activeTab === 'daily' ? (missionsData.dailyMissions || []) :
                                          activeTab === 'weekly' ? (missionsData.weeklyMissions || []) :
                                          (missionsData.monthlyMissions || []);
                    return currentMissions.slice(0, 3).map((mission, index) => {
                      const missionState = calculateMissionState(mission);
                      const isCompleted = missionState === 'complete' || mission.claimed;
                      return (
                        <div key={mission.id} className={`${styles.progressStep} ${isCompleted ? styles.completed : ''}`}>
                          <div className={styles.stepIcon}>💎</div>
                        </div>
                      );
                    });
                  })()}
                </div>
              </div>

              {/* Bottom Right - Safe Icon */}
              <div className={styles.bottomRight}>
                <div className={styles.safeContainer}>
                  <svg className={styles.safeIcon} viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    {/* Safe body */}
                    <rect x="15" y="25" width="70" height="60" rx="8" fill="#2c3e50" stroke="#34495e" strokeWidth="2"/>
                    {/* Safe door */}
                    <rect x="20" y="30" width="60" height="50" rx="6" fill="#34495e" stroke="#4a6741" strokeWidth="1"/>
                    {/* Handle */}
                    <circle cx="70" cy="55" r="8" fill="#f39c12" stroke="#e67e22" strokeWidth="2"/>
                    <circle cx="70" cy="55" r="4" fill="#e67e22"/>
                    {/* Lock mechanism */}
                    <rect x="45" y="50" width="8" height="10" rx="2" fill="#95a5a6"/>
                    {/* Coins scattered around */}
                    <circle cx="25" cy="20" r="3" fill="#f1c40f" stroke="#f39c12" strokeWidth="1"/>
                    <circle cx="75" cy="18" r="2.5" fill="#f1c40f" stroke="#f39c12" strokeWidth="1"/>
                    <circle cx="35" cy="15" r="2" fill="#f1c40f" stroke="#f39c12" strokeWidth="1"/>
                    {/* Diamonds */}
                    <polygon points="30,90 35,85 30,80 25,85" fill="#3498db" stroke="#2980b9" strokeWidth="1"/>
                    <polygon points="70,92 74,88 70,84 66,88" fill="#3498db" stroke="#2980b9" strokeWidth="1"/>
                    <polygon points="50,88 53,85 50,82 47,85" fill="#3498db" stroke="#2980b9" strokeWidth="1"/>
                    {/* Safe legs */}
                    <rect x="20" y="85" width="6" height="8" fill="#2c3e50"/>
                    <rect x="74" y="85" width="6" height="8" fill="#2c3e50"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Background decorative elements */}
          <div className={styles.headerDecorations}>
            <div className={styles.decorativeCircle1}></div>
            <div className={styles.decorativeCircle2}></div>
            <div className={styles.decorativeCircle3}></div>
            <div className={styles.decorativeGlow1}></div>
            <div className={styles.decorativeGlow2}></div>
          </div>
        </div>

        {/* Mission Tabs */}
        <div className={styles.missionTabs}>
          <button
              className={`${styles.tabButton} ${activeTab === 'daily' ? styles.activeTab : ''}`}
              onClick={() => setActiveTab('daily')}
          >
            <span className={styles.tabIcon}>📅</span>
            <span className={styles.tabText}>Daily</span>
            <span className={styles.tabCount}>
              {missionsData?.dailyMissions?.length || 0}
            </span>
          </button>
          <button
              className={`${styles.tabButton} ${activeTab === 'weekly' ? styles.activeTab : ''}`}
              onClick={() => setActiveTab('weekly')}
          >
            <span className={styles.tabIcon}>📊</span>
            <span className={styles.tabText}>Weekly</span>
            <span className={styles.tabCount}>
              {missionsData?.weeklyMissions?.length || 0}
            </span>
          </button>
          <button
              className={`${styles.tabButton} ${activeTab === 'monthly' ? styles.activeTab : ''}`}
              onClick={() => setActiveTab('monthly')}
          >
            <span className={styles.tabIcon}>🏆</span>
            <span className={styles.tabText}>Monthly</span>
            <span className={styles.tabCount}>
              {missionsData?.monthlyMissions?.length || 0}
            </span>
          </button>
        </div>

        {/* Missions List */}
        <div className={styles.missionsList}>
          {missionsData && (() => {
            let currentMissions = [];
            switch(activeTab) {
              case 'daily':
                currentMissions = missionsData.dailyMissions || [];
                break;
              case 'weekly':
                currentMissions = missionsData.weeklyMissions || [];
                break;
              case 'monthly':
                currentMissions = missionsData.monthlyMissions || [];
                break;
              default:
                currentMissions = [];
            }

            return currentMissions.map((mission) => {
              const missionState = calculateMissionState(mission);
              const isCompleted = missionState === 'complete';
              const isClaimed = mission.claimed;
              const isInactive = mission.isInactive;
              const isParticipating = mission.userAssignments && mission.userAssignments.length > 0;

              // Calculate progress for the mission (same logic as missions page)
              let progressPercentage = 0;
              let current = 0;
              let target = 1;

              if (mission.objectives && mission.objectives.length > 0) {
                // Use first objective for progress calculation
                const firstObjective = mission.objectives[0];
                const assignment = mission.userAssignments?.find(
                    assignment => assignment.missionObjectiveId === firstObjective.id
                );
                current = assignment ? parseInt(assignment.progress || '0') : 0;
                target = parseInt(firstObjective.targetValue || '1');
                progressPercentage = target > 0 ? (current / target) * 100 : 0;
              } else if (mission.current !== undefined && mission.target !== undefined) {
                current = mission.current;
                target = mission.target;
                progressPercentage = target > 0 ? (current / target) * 100 : 0;
              }

              // Determine button text and action
              let buttonText = "Start";
              let buttonAction = () => handleParticipate(mission.id);
              let buttonDisabled = false;
              let buttonClass = styles.startButton;

              if (isInactive) {
                buttonText = language === 'tr' ? 'Bekleme Süresi' : 'In Cooldown';
                buttonAction = null;
                buttonDisabled = true;
                buttonClass = `${styles.startButton} ${styles.inactive}`;
              } else if (isClaimed) {
                buttonText = language === 'tr' ? 'Alındı' : 'Claimed';
                buttonAction = null;
                buttonDisabled = true;
                buttonClass = `${styles.startButton} ${styles.claimed}`;
              } else if (isCompleted && isParticipating) {
                buttonText = language === 'tr' ? 'Al' : 'Claim';
                buttonAction = () => handleClaimReward(mission.id);
                buttonDisabled = false;
                buttonClass = `${styles.startButton} ${styles.completed}`;
              } else if (isParticipating) {
                buttonText = language === 'tr' ? 'Devam Ediyor' : 'In Progress';
                buttonAction = null;
                buttonDisabled = true;
                buttonClass = `${styles.startButton} ${styles.inProgress}`;
              }

              return (
                <div key={mission.id} className={`${styles.missionCard} ${isInactive ? styles.inactiveCard : ''}`} style={{position: 'relative'}}>
                  <div className={styles.missionContent} style={{position: 'relative'}}>
                    {/* Mission Info (left) */}
                    <div className={styles.missionInfo}>
                      <h3 className={styles.missionTitle}>{mission.title}</h3>
                      <p className={styles.missionDescription}>{mission.description}</p>
                    </div>
                    {/* Mission Reward (right, vertically centered) */}
                    <div className={styles.missionReward}>
                      <span className={styles.rewardAmount}>{mission.reward}</span>
                      <span className={styles.rewardIcon}>💎</span>
                    </div>
                    {/* Mission Stats (progress + button, full width below) */}
                    <div className={styles.missionStats}>
                      <div className={styles.missionProgress}>
                        <span className={styles.progressText}>{current} / {target}</span>
                        <div className={styles.progressBar}>
                          <div
                            className={styles.progressFill}
                            style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                          />
                        </div>
                      </div>
                      <button
                        className={buttonClass}
                        onClick={buttonAction}
                        disabled={buttonDisabled}
                      >
                        {buttonText}
                      </button>
                    </div>
                  </div>
                </div>
              );
            });
          })()}
        </div>
      </div>
    </div>
  </>
};

export default MissionsModal;
