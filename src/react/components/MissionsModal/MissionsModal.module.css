/* Missions Modal Styles */

/* Modal Overlay */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Rajdhani-SemiBold', serif;
}

/* Modal Content */
.modalContent {
  background: linear-gradient(135deg, #061d2b 0%, #071e2d 30%, #082030 60%, #092235 80%, #0a2438 100%);
  border: 1px solid #2a2d47;
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: calc(100vh - 180px);
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.balanceTimerSection {
  display: flex;
  flex-direction: row;
  gap: 24px;
  width: 100%;
}

@media (max-width: 1024px) {
  .balanceTimerSection {
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
    gap: 20px;
    flex-wrap: nowrap;
  }
}

@media (max-width: 768px) {
  .balanceTimerSection {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .balanceTimerSection {
    gap: 12px;
  }
}

/* Modal Header */
.modalHeader {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.closeButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 40px;
  min-height: 300px;
}

.loadingSpinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(24, 207, 251, 0.2);
  border-top: 4px solid #18cffb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loadedModalContent {
  height: 100%;
  margin: 20px 0 0 0;
  top: 0;
  position: relative;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loadingText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0;
  font-weight: 500;
}

/* Missions Header */
.missionsHeader {
  display: flex;
  background:
    /* Primary gradient overlay */
          linear-gradient(135deg,
          rgba(24, 207, 251, 0.15) 0%,
          rgba(10, 195, 198, 0.12) 30%,
          rgba(6, 29, 43, 0.8) 70%,
          rgba(7, 30, 45, 0.9) 100%),
            /* Depth gradient */
          linear-gradient(45deg,
          #061d2b 0%,
          #071e2d 25%,
          #082030 50%,
          #092235 75%,
          #0a2438 100%),
            /* Accent spots */
          radial-gradient(circle at 20% 20%, rgba(24, 207, 251, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(71, 140, 245, 0.08) 0%, transparent 50%);
  padding: 20px 35px;
  position: relative;
  overflow: hidden;
  min-height: 200px;
  border: 1px solid rgba(24, 207, 251, 0.2);
}

/* Big Reward Banner - Top Right Corner */
.bigRewardBanner {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(135deg, #ffe02e 20%, #ffb000 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  /* border: 2px solid rgba(255, 255, 255, 0.2); */
  /* border-bottom-left-radius: 20px; */
  /* animation: rewardGlow 2s ease-in-out infinite alternate; */
  z-index: 10;
  min-width: 100px;
  text-align: center;
  clip-path: polygon(
    0   0,              /* top‑left */
    100% 0,             /* top‑right */
    100% calc(100% - 20px),   /* start of bottom‑right cut */
    50% 100%,           /* bottom center point */
    0   calc(100% - 20px)      /* start of bottom‑left cut */
  );
  /* fall back for old WebKit browsers */
  -webkit-clip-path: polygon(
    0   0,
    100% 0,
    100% calc(100% - 20px),
    50% 100%,
    0   calc(100% - 20px)
  );
}

.headerContent {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  gap: 32px;
  padding: 0;
}

/* Top Row */
.topRow {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 32px;
}

/* Bottom Row */
.bottomRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 32px;
}

/* Top Left - Mission Type and Title */
.topLeft {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

/* Bottom Left - Timer */
.bottomLeft {
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

/* Bottom Center - Progress Train */
.bottomCenter {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

/* Bottom Right - Safe Icon */
.bottomRight {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.missionTypeLabel {
  color: #18cffb;
  font-size: 1.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(24, 207, 251, 0.4);
}

.missionsTitle {
  margin: 0;
  font-size: 3.5rem;
  font-weight: 400;
  color: white;
  text-shadow: 0 2px 65px rgb(221 233 255);
  line-height: 1;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
  max-width: 320px;
  line-height: 1.4;
}

.updateLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  backdrop-filter: blur(8px);
}

.updateIcon {
  font-size: 1rem;
}

.updateText {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Safe Icon */
.safeContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.safeIcon {
  width: 80px;
  height: 80px;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.4));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

/* Timer Container */
.timerContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg,
    rgba(6, 29, 43, 0.9) 0%,
    rgba(7, 30, 45, 0.95) 100%);
  padding: 20px 24px 16px 24px;
  border-radius: 16px;
  border: 2px solid rgba(24, 207, 251, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 20px rgba(24, 207, 251, 0.2);
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
  margin-top: 16px;
  border-top-left-radius: 0;
}

.timerContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(24, 207, 251, 0.1) 50%,
    transparent 100%);
  animation: timerShimmer 4s infinite;
}

@keyframes timerShimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Update Label - Convex part of timer */
.updateLabel {
  position: absolute;
  top: -9px;
  left: 0;
  background: linear-gradient(90deg, #07212e 0%, #06212e 100%);
  color: #18cffb;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 16px;
  border-radius: 12px 12px 12px 0;
  border: 2px solid rgba(24, 207, 251, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  backdrop-filter: blur(10px);
  z-index: 2;
  overflow: hidden;
  border-bottom-right-radius: 0;
}

.updateLabel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(24, 207, 251, 0.1) 50%,
    transparent 100%);
  animation: timerShimmer 4s infinite;
}

/* Progress Train */
.progressTrain {
  position: relative;
  width: 100%;
  height: 48px;
  background: #2a353e;
  border-radius: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.progressDiamonds {
  width: 300px;
  top: -173px;
  position: absolute;
  left: 46%;
  transform: translateX(-50%);
  z-index: -1;
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.4));
  animation: diamondsGlow 3s ease-in-out infinite alternate;
}

@keyframes diamondsGlow {
  0% {
    filter: drop-shadow(0 0 20px rgba(0, 200, 255, 0.4));
  }
  100% {
    filter: drop-shadow(0 0 30px rgba(0, 255, 242, 0.6));
  }
}

.progressSteps {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-evenly;
  position: relative;
  margin-right: 20px;
}

.progressStep {
  border: 1px #0484cd solid;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #304055;
  transition: all 0.3s ease;
}

.progressStep.completed {
  background: #2f5f89;
  border: 4px #47b7ff solid;
  filter: drop-shadow(rgba(32, 182, 237, 0.58) 0px 0px 10px);
}

.stepIcon {
  font-size: 1.25rem;
}

/* Safe at the end */
.progressSafe {
  /* position: absolute; */
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  animation: safeGlow 2s ease-in-out infinite alternate;
}

.progressSafe img {
  transform: translateY(-19px);
  filter: drop-shadow(0 0 15px rgba(24, 207, 251, 0.6));
  animation: safeImageGlow 2s ease-in-out infinite alternate;
}

@keyframes safeGlow {
  0% {
    box-shadow: 0 0 15px rgba(24, 207, 251, 0.2);
  }
  100% {
    box-shadow: 0 0 25px rgba(24, 207, 251, 0.4);
  }
}

@keyframes safeImageGlow {
  0% {
    filter: drop-shadow(0 0 10px rgba(24, 207, 251, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 18px rgba(24, 207, 251, 0.5));
  }
}

@keyframes rewardGlow {
  0% {
    box-shadow:
      0 8px 32px rgba(254, 202, 87, 0.4),
      inset 0 2px 4px rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow:
      0 12px 40px rgba(254, 202, 87, 0.6),
      inset 0 2px 4px rgba(255, 255, 255, 0.4);
  }
}

.bigRewardText {
  font-size: 1.6rem;
  font-weight: 500;
  color: #2d1810;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.completionBadge {
  border: 1px #2d1810 solid;
  /* background: rgba(0, 0, 0, 0.25); */
  border-radius: 12px;
  padding: 2px 10px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d1810;
  margin-top: 4px;
}

/* Custom Scrollbar - Update these selectors */
.modalContent::-webkit-scrollbar {
  width: 8px;
}

.modalContent::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.modalContent::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.modalContent::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #20d4ff 0%, #0ed1d4 100%);
}

/* Firefox scrollbar */
.modalContent {
  scrollbar-width: thin;
  scrollbar-color: #18cffb rgba(255, 255, 255, 0.05);
}



/* Timer Styles */
.timerDisplay {
  display: flex;
  align-items: center;
  gap: 8px;
}

.timeBlock {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.timeValue {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  line-height: 1;
  min-width: 32px;
  text-align: center;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.timeLabel {
  font-size: 0.7rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.timeSeparator {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0 4px;
  opacity: 0.8;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}











.coins,
.gems,
.chest {
  position: absolute;
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.9;
  animation: float 3s ease-in-out infinite;
}

/* Floating animation */
@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-10px) rotate(5deg);
  }
}

/* Enhanced decorative elements */
.coins {
  width: 60px;
  height: 60px;
  top: 15%;
  right: 62%;
  background:
          radial-gradient(circle at 30% 30%, #feca57 0%, #ff9f43 100%),
          linear-gradient(135deg, #feca57 0%, #ff6b6b 100%);
  border-radius: 50%;
  box-shadow:
          0 4px 20px rgba(254, 202, 87, 0.3),
          inset 0 2px 4px rgba(255, 255, 255, 0.3);
  animation-delay: 0s;
}

.coins::before {
  content: '💰';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.gems {
  width: 50px;
  height: 50px;
  top: 22%;
  right: 24%;
  background:
          radial-gradient(circle at 30% 30%, #18cffb 0%, #0ac3c6 100%),
          linear-gradient(135deg, #18cffb 0%, #478cf5 100%);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  box-shadow:
          0 4px 20px rgba(24, 207, 251, 0.4),
          inset 0 2px 4px rgba(255, 255, 255, 0.3);
  animation-delay: 1s;
}

.gems::before {
  content: '💎';
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.chest {
  width: 70px;
  height: 55px;
  bottom: 16%;
  right: 28%;
  background:
          radial-gradient(circle at 30% 30%, #daa520 0%, #b8860b 100%),
          linear-gradient(135deg, #8b4513 0%, #daa520 100%);
  border-radius: 12px;
  box-shadow:
          0 4px 20px rgba(218, 165, 32, 0.3),
          inset 0 2px 4px rgba(255, 255, 255, 0.2);
  animation-delay: 2s;
}

.chest::before {
  content: '🎁';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}





/* Mission Tabs */
.missionTabs {
  display: flex;
  padding: 0 30px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg,
  rgba(24, 207, 251, 0.05) 0%,
  rgba(10, 195, 198, 0.02) 100%);
}

.tabButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-bottom: 3px solid transparent;
}

.tabButton:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.05);
}

.tabButton.activeTab {
  color: #18cffb;
  border-bottom-color: #18cffb;
  background: rgba(24, 207, 251, 0.1);
}

.tabIcon {
  font-size: 1.1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.tabText {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tabCount {
  background: rgba(24, 207, 251, 0.2);
  color: #18cffb;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 700;
  min-width: 20px;
  text-align: center;
}

.activeTab .tabCount {
  background: #18cffb;
  color: #000000;
}

/* Missions List */
.missionsList {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

/* Mission Card */
.missionCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.missionCard:hover {
  border-color: rgba(24, 207, 251, 0.3);
  box-shadow: 0 8px 32px rgba(24, 207, 251, 0.1);
}

.missionContent {
  display: flex;
  gap: 20px;
  width: 100%;
  min-height: 80px;
  justify-content: space-between;
}

.missionInfo {
  flex: 1;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
}

.missionTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin: 0 0 8px 0;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.missionDescription {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.gameHighlight {
  color: #18cffb;
  font-weight: 600;
}

.missionStats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: stretch;
  min-width: 120px;
  z-index: 2;
}

/* Mission Reward */
.missionReward {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 0;
  gap: 8px;
  padding: 8px;
  background: rgba(24, 207, 251, 0.1);
  border: 1px solid rgba(24, 207, 251, 0.3);
  border-radius: 12px;
  align-self: center;
}

.rewardAmount {
  font-size: 1.1rem;
  font-weight: 700;
  color: #18cffb;
}

.rewardIcon {
  font-size: 1.2rem;
}

/* Mission Progress */
.missionProgress {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 8px 16px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 80px;
  width: 100%;
  box-sizing: border-box;
}

.progressText {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.progressBar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #18cffb 0%, #0ac3c6 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
  box-shadow: 0 0 8px rgba(24, 207, 251, 0.5);
}

/* Start Button */
.startButton {
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 80px;
  width: 100%;
  box-sizing: border-box;
}

.startButton:hover {
  background: linear-gradient(135deg, #0ac3c6 0%, #18cffb 100%);
  box-shadow: 0 4px 16px rgba(24, 207, 251, 0.3);
  transform: translateY(-2px);
}

.startButton:active {
  transform: translateY(0);
}

.startButton.completed {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
}

.startButton.completed:hover {
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(34, 197, 94, 0.3);
}

.startButton.claimed {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: rgba(255, 255, 255, 0.7);
  cursor: not-allowed;
}

.startButton.claimed:hover {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  transform: none;
  box-shadow: none;
}

.startButton.inProgress {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #000000;
  cursor: not-allowed;
}

.startButton.inProgress:hover {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  transform: none;
  box-shadow: none;
}

.startButton.inactive {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: rgba(255, 255, 255, 0.9);
  cursor: not-allowed;
}

.startButton.inactive:hover {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  transform: none;
  box-shadow: none;
}

.missionCard.inactiveCard {
  opacity: 0.7;
  position: relative;
}

.missionCard.inactiveCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  pointer-events: none;
}

/* Timer loading and error states */
.timerLoading,
.timerError {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.timerLoading {
  background: rgba(24, 207, 251, 0.1);
  color: #18cffb;
  border: 1px solid rgba(24, 207, 251, 0.2);
}

.timerError {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .balanceSection {
    justify-self: end;
  }

  .timerSection {
    justify-self: start;
  }

  .modalOverlay {
    padding-left: 0;
    padding-right: 0;
    border-radius: 0;
    max-height: calc(100vh - 60px);
  }
  .modalContent {
    top: 0;
    position: absolute;
    border-radius: 0;
    margin: 0;
    max-height: calc(100vh - 60px);
  }
  .loadingModalContent {
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
    margin: 0 !important;
    position: fixed !important;
    top: 0;
    left: 0;
  }
  .missionsHeader {
    padding: 12px 20px;
    min-height: 160px;
    border-radius: 0;
  }

  .bigRewardBanner {
    padding: 12px 16px;
    min-width: 80px;
  }

  .bigRewardText {
    font-size: 1rem;
  }

  .completionBadge {
    font-size: 0.8rem;
    padding: 3px 8px;
  }

  .topRow {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .topLeft {
    align-items: center;
  }

  .missionsTitle {
    font-size: 2.8rem;
  }

  .subtitle {
    text-align: center;
    max-width: none;
  }

  .centerRow {
    margin: 16px 0;
  }

  .timerContainer {
    padding: 24px 32px;
  }

  .timeValue {
    font-size: 2.8rem;
    min-width: 50px;
  }

  .timeSeparator {
    font-size: 2rem;
    margin: 0 6px;
  }

  .bottomRow {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .bottomLeft {
    order: 1;
  }

  .bottomCenter {
    order: 2;
  }

  .bottomRight {
    order: 3;
  }

  .progressTrain {
    justify-content: center;
    gap: 16px;
  }

  .progressStep {
    width: 50px;
    height: 50px;
  }

  .progressStep::after {
    width: 16px;
  }

  .stepIcon {
    font-size: 1.3rem;
  }

  .safeIcon {
    width: 60px;
    height: 60px;
  }

  .balanceCard {
    padding: 16px 20px;
    min-width: 160px;
  }

  .balanceAmount {
    font-size: 1.6rem;
  }

  .bigRewardBadge {
    padding: 20px 28px;
    min-width: 120px;
  }



  .missionTabs {
    padding: 0 20px;
  }

  .tabButton {
    padding: 12px 16px;
    font-size: 0.8rem;
  }

  .tabText {
    display: none;
  }

  .missionsList {
    padding: 20px;
    gap: 16px;
    min-height: 50vh;
  }

  .missionInfo {
    text-align: left;
  }

  .missionReward {
    justify-self: center;
    margin: 0;
  }

  .missionStats {
    align-items: stretch;
    justify-content: space-between;
    gap: 12px;
  }

  .missionProgress {
    flex: 1;
    border-radius: 12px;
    min-width: 100px;
    width: 100%;
  }

  .bigRewardBadge {
    padding: 16px 20px;
  }

  .missionCard {
    padding: 20px;
  }

  .missionTitle {
    font-size: 1rem;
  }

  .missionDescription {
    font-size: 0.85rem;
  }

  .loadingContainer {
    height: 100%;
  }

  .loadedModalContent {
    height: 100%;
    max-height: 100%;
    position: static;
  }

  .missionStats {
  }
}

@media (max-width: 480px) {
  .modalOverlay {
    padding-left: 0;
    padding-right: 0;
  }
  .modalContent {
    border-radius: 0;
    margin: 0;

  }

  .missionContent {
    display: flex;
    flex-direction: column;
    gap: 16px;
    text-align: left;
    align-items: stretch;
  }

  .loadingModalContent {
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
    margin: 0 !important;
    position: fixed !important;
    top: 0;
    left: 0;
  }
  .missionsHeader {
    border-radius: 0;
    padding: 20px 36px;
    min-height: 140px;
  }

  .missionReward{
    position: absolute;
    top: 0;
    right: 0;
    bottom: unset;
    left: unset;
  }

  .titleText {
    font-size: 1.5rem;
  }

  .missionsIcon {
    font-size: 2rem;
  }

  .balanceHeader {
    margin-bottom: 4px;
  }

  .balanceCard {
    padding: 12px 20px;
  }

  .startButton {
    padding: 10px 20px;
    font-size: 0.8rem;
    min-width: 100px;
    width: 100%;
  }


  .balanceAmount {
    font-size: 1.4rem;
  }

  .statsRow {
    gap: 8px;
  }

  .statItem {
    padding: 6px 10px;
  }

  .timer {
    padding: 10px 12px;
    gap: 3px;
    min-width: 140px;
  }

  .timeValue {
    font-size: 1.1rem;
    min-width: 24px;
  }

  .timeSeparator {
    font-size: 1rem;
    margin: 0 1px;
  }

  .missionTabs {
    padding: 0 16px;
  }

  .tabButton {
    padding: 10px 12px;
    font-size: 0.75rem;
    gap: 6px;
  }

  .missionsList {
    padding: 16px;
    gap: 12px;
    min-height: 50vh;
  }

  .missionCard {
    padding: 16px;
  }

  .startButton {
    padding: 10px 20px;
    font-size: 0.8rem;
    min-width: 100px;
    width: 100%;
  }

  .balanceTimerSection {
    gap: 16px;
  }
}

@media (max-width: 380px) {
  .timer {
    min-width: 110px;
  }

  .balanceCard {
    padding: 12px 12px;
  }
}
