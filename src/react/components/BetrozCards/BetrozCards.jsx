import { useMemo, memo } from 'react'
import styles from './BetrozCards.module.css'

// Internationalization
export const i18n = {
  en: {
    casinoTitle: "Casino",
    casinoGames: "8,500+ GAMES",
    casinoDesc: "Enjoy Betroz Originals and other casino games from top providers.",
    casinoBtn: "Go to Casino",
    sportsTitle: "Sports",
    sportsFreeBets: "FREE BETS",
    sportsDesc: "Bet on popular sports events with high odds and other great features.",
    sportsBtn: "Go to Sports"
  },
  tr: {
    casinoTitle: "Casino",
    casinoGames: "8,500+ OYUN",
    casinoDesc: "Betroz Orijinalleri ve en iyi sağlayıcılardan diğer casino oyunlarının keyfini çıkarın.",
    casinoBtn: "Casino'ya Git",
    sportsTitle: "Spor",
    sportsFreeBets: "ÜCRETSİZ BAHİSLER",
    sportsDesc: "Yüksek oranlar ve diğer harika özelliklerle popüler spor etkinliklerine bahis yapın.",
    sportsBtn: "Spor'a Git"
  },
  de: {
    casinoTitle: "Casino",
    casinoGames: "8,500+ SPIELE",
    casinoDesc: "Genießen Sie Betroz Originals und andere Casino-Spiele von Top-Anbietern.",
    casinoBtn: "Zum Casino",
    sportsTitle: "Sport",
    sportsFreeBets: "GRATIS WETTEN",
    sportsDesc: "Setzen Sie auf beliebte Sportereignisse mit hohen Quoten und anderen großartigen Features.",
    sportsBtn: "Zum Sport"
  }
}

// Pre-computed card data to avoid re-computation
const CARD_DATA = [
  {
    id: 'casino',
    type: 'casino',
    iconFrames: [
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/10.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/9.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/8.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/7.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/6.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/5.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/4.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/3.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/2.png',
      'https://d11caojl6op1au.cloudfront.net/img/slotmachine/1.png'
    ],
    iconAlt: 'Casino',
    urlPath: '/casino'
  },
  {
    id: 'sports',
    type: 'sports',
    iconSrc: 'https://i.ibb.co/Cp3jkgh7/sport3d-card.png',
    iconAlt: 'Sports',
    urlPath: '/sports'
  }
]

const BetrozCards = ({ i18n: translations, language = 'en' }) => {
  // Memoize base URL calculation
  const baseUrl = useMemo(() => {
    return window.location.origin.replace('betfury.com', 'betroz.com')
  }, [])

  // Memoize URLs to prevent recalculation on every render
  const urls = useMemo(() => ({
    casino: `${baseUrl}/${language}/casino`,
    sports: `${baseUrl}/${language}/sports`
  }), [baseUrl, language])

  // Render optimized cards using map for better performance
  return (
    <div className={styles.betrozCards}>
      <div className={styles.cardsWrapper}>
        {CARD_DATA.map((card) => {
          const cardTranslations = card.type === 'casino'
            ? {
                title: translations.casinoTitle,
                badge: translations.casinoGames,
                description: translations.casinoDesc,
                button: translations.casinoBtn
              }
            : {
                title: translations.sportsTitle,
                badge: translations.sportsFreeBets,
                description: translations.sportsDesc,
                button: translations.sportsBtn
              }

          return (
            <a
              key={card.id}
              href={urls[card.type]}
              className={`${styles.card} ${styles[`${card.type}Card`]}`}
            >
              <div className={styles.cardContent}>
                <div className={styles.iconContainer}>
                  {card.type === 'casino' ? (
                    <div
                      className={styles.animatedIcon}
                      style={{
                        '--total-frames': card.iconFrames.length,
                        '--animation-duration': '0.5s',
                        '--frame-delay': '0.1s'
                      }}
                    >
                      {card.iconFrames.map((frameSrc, index) => (
                        <img
                          key={index}
                          src={frameSrc}
                          alt={card.iconAlt}
                          className={`${styles.icon} ${styles.animationFrame}`}
                          style={{
                            '--frame-index': index,
                            '--is-last-frame': index === card.iconFrames.length - 1 ? 1 : 0
                          }}
                          loading="lazy"
                          decoding="async"
                        />
                      ))}
                    </div>
                  ) : (
                    <img
                      src={card.iconSrc}
                      alt={card.iconAlt}
                      className={styles.icon}
                      loading="lazy"
                      decoding="async"
                    />
                  )}
                </div>
                <div className={styles.content}>
                  <h3 className={styles.title}>
                    {cardTranslations.title}
                    <span className={styles.badge}>{cardTranslations.badge}</span>
                  </h3>
                  <p className={styles.description}>{cardTranslations.description}</p>
                  <div className={styles.buttonArea}>
                    <span className={styles.button}>
                      {cardTranslations.button}
                      <span className={styles.buttonArrow}>→</span>
                    </span>
                  </div>
                </div>
              </div>
            </a>
          )
        })}
      </div>
    </div>
  )
}

// Memoize the component to prevent unnecessary re-renders
export default memo(BetrozCards)
