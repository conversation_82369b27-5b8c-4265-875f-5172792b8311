/* Main container for Betroz cards - Optimized for performance */
.betrozCards {
  width: 100%;
  margin: 30px 0;
  padding: 0;
  box-sizing: border-box;
  /* Remove perspective to reduce GPU load */
  contain: layout style paint;
}

/* Cards wrapper - Optimized layout */
.cardsWrapper {
  display: flex;
  gap: 24px;
  width: 100%;
  box-sizing: border-box;
  /* Use will-change for better performance */
  will-change: transform;
}

/* Individual card styling - Performance optimized */
.card {
  flex: 1;
  min-height: 100px;
  border-radius: 13px;
  padding: 0;
  color: white;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  /* Optimized transitions - only transform and opacity for GPU acceleration */
  transition: transform 0.2s cubic-bezier(0.4, 0.0, 0.2, 1),
    opacity 0.2s ease;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  /* Use solid background instead of gradient for better performance */
  background: #071e2d;
  /* Force GPU acceleration */
  transform: translateZ(0);
  will-change: transform;
  /* Optimize for compositing */
  backface-visibility: hidden;
}

/* Content wrapper - Performance optimized */
.cardContent {
  position: relative;
  z-index: 1;
  padding: 22px;
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
  /* Optimize for compositing */
  transform: translateZ(0);
}

/* Simplified shimmer overlay - Reduced complexity for performance */
.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  /* Simplified gradient for better performance */
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: transform 0.3s ease;
  border-radius: 13px;
  z-index: 2;
  /* Force GPU layer */
  transform: translateZ(0);
  will-change: transform;
}

/* Optimized hover effects - GPU accelerated */
.card:hover {
  /* Use only transform for smooth GPU acceleration */
  transform: translate3d(0, -3px, 0) scale(1.002);
  /* Subtle background change without gradient */
  background: #082030;
}

/* Optimized shimmer animation */
.card:hover::after {
  transform: translateX(200%);
}

/* Icon container - Performance optimized */
.iconContainer {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
  /* Simplified transition for better performance */
  transition: transform 0.3s ease;
  /* Remove floating animation to reduce CPU usage */
  transform: translateZ(0);
  will-change: transform;
}

/* Removed floating animation for better performance */
/* @keyframes float removed to reduce CPU usage */

/* Optimized icon hover effect */
.card:hover .iconContainer {
  transform: translate3d(0, 0, 0) scale(1.3);
}

/* Icon styling - Optimized for performance */
.icon {
  width: 130px;
  height: 130px;
  object-fit: contain;
  /* Simplified filter for better performance */
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: filter 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
  /* Force GPU acceleration and flip horizontally */
  transform: translateZ(0) scaleX(-1);
}

.sportsCard .icon {
  width: 80px;
  height: 80px;
}

.card:hover .icon {
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4)) brightness(1.1);
  transform: translateZ(0) scaleX(-1) scale(1.15);
}

/* Animated casino icon container */
.animatedIcon {
  position: relative;
  width: 130px;
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animation frames - all positioned absolutely */
.animationFrame {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.1s ease;
  /* Flip images horizontally */
  transform: scaleX(-1);
}

/* Show only the last frame by default */
.animationFrame[style*="--is-last-frame: 1"] {
  opacity: 1;
}

/* Hide last frame initially when hovering to allow sequence to play */
.card:hover .animationFrame[style*="--is-last-frame: 1"] {
  opacity: 0;
}

/* Reverse animation keyframes - for intermediate frames */
@keyframes slotReverse {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  40% {
    opacity: 0;
  }

  100% {
    opacity: 0;
  }
}

/* Last frame animation - stays visible after animation */
@keyframes slotReverseLast {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

/* Dynamic animation timing using CSS custom properties */
.card:hover .animationFrame {
  animation-name: slotReverse;
  animation-duration: var(--animation-duration, 0.5s);
  animation-timing-function: ease-in-out;
  animation-fill-mode: forwards;
  animation-delay: calc(var(--frame-index, 0) * var(--frame-delay, 0.1s));
}

/* Last frame uses different animation to stay visible */
.card:hover .animationFrame[style*="--is-last-frame: 1"] {
  animation-name: slotReverseLast;
  animation-delay: calc(var(--frame-index, 0) * var(--frame-delay, 0.1s));
}

/* Content area - Performance optimized */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
  z-index: 2;
  /* Optimize for compositing */
  transform: translateZ(0);
}

/* Title - Performance optimized */
.title {
  font-size: 22px;
  font-weight: 800;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ffffff;
  letter-spacing: -0.3px;
  /* Simplified transition */
  transition: transform 0.2s ease;
  transform: translateZ(0);
}

.card:hover .title {
  transform: translate3d(1px, 0, 0);
}

/* Badge - Performance optimized */
.badge {
  font-size: 9px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 9px;
  /* Simplified background for better performance */
  background: rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: background 0.2s ease;
  /* Remove pulsing animation to reduce CPU usage */
  transform: translateZ(0);
}

/* Removed pulsing animation for better performance */
/* @keyframes pulse removed to reduce CPU usage */

.card:hover .badge {
  background: rgba(255, 255, 255, 0.25);
}

/* Description - Performance optimized */
.description {
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
  color: #ffffff;
  /* Simplified transition */
  transition: opacity 0.2s ease;
  transform: translateZ(0);
}

.card:hover .description {
  opacity: 1;
}

/* Button area - Performance optimized */
.buttonArea {
  display: flex;
  align-items: center;
  margin-top: 8px;
  transform: translateZ(0);
}

/* Button - Performance optimized */
.button {
  font-size: 13px;
  font-weight: 700;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.1);
  /* Simplified transition for better performance */
  transition: background 0.2s ease, transform 0.2s ease;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transform: translateZ(0);
  will-change: transform;
}

/* Simplified button effects - Remove complex pseudo-elements for performance */
.card:hover .button {
  background: rgba(255, 255, 255, 0.15);
  transform: translate3d(1px, 0, 0);
}

/* Simplified button arrow animation */
.buttonArrow {
  transition: transform 0.2s ease;
  transform: translateZ(0);
}

.card:hover .buttonArrow {
  transform: translate3d(1px, 0, 0);
}

/* Responsive design - Performance optimized */
@media (max-width: 768px) {
  .betrozCards {
    margin: 16px 0;
  }

  .cardsWrapper {
    flex-direction: column;
    gap: 16px;
  }

  .card {
    min-height: 90px;
    border-radius: 11px;
  }

  .cardContent {
    padding: 12px;
    gap: 12px;
  }

  .card:hover {
    transform: translate3d(0, -2px, 0) scale(1.001);
  }

  .iconContainer {
    width: 90px;
    height: 90px;
    /* Remove animation for mobile performance */
  }

  .animatedIcon {
    width: 95px;
    height: 95px;
  }

  .icon {
    width: 95px;
    height: 95px;
  }

  .sportsCard .icon {
    width: 45px;
    height: 45px;
  }

  .title {
    font-size: 18px;
    gap: 10px;
  }

  .description {
    font-size: 12px;
    line-height: 1.3;
  }

  .button {
    font-size: 12px;
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .betrozCards {
    margin: 16px 0;
  }

  .card {
    min-height: 100px;
    border-radius: 11px;
  }

  .cardContent {
    padding: 16px;
    gap: 12px;
  }

  .card:hover {
    transform: translate3d(0, -2px, 0) scale(1.001);
  }

  .iconContainer {
    width: 90px;
    height: 90px;
  }

  .animatedIcon {
    width: 95px;
    height: 95px;
  }

  .icon {
    width: 95px;
    height: 95px;
  }

  .sportsCard .icon {
    width: 45px;
    height: 45px;
  }

  .title {
    font-size: 18px;
    gap: 10px;
  }

  .badge {
    font-size: 9px;
    padding: 4px 8px;
  }

  .description {
    font-size: 12px;
  }

  .button {
    font-size: 12px;
    padding: 8px 12px;
    gap: 6px;
  }
}

/* Ultra-wide screen enhancements - Performance optimized */
@media (min-width: 1400px) {
  .cardContent {
    padding: 24px;
  }

  .card {
    min-height: 140px;
  }

  .iconContainer {
    width: 140px;
    height: 140px;
  }

  .animatedIcon {
    width: 150px;
    height: 150px;
  }

  .icon {
    width: 150px;
    height: 150px;
  }

  .sportsCard .icon {
    width: 100px;
    height: 100px;
  }

  .title {
    font-size: 26px;
  }

  .description {
    font-size: 15px;
  }

  .button {
    font-size: 14px;
    padding: 12px 20px;
  }
}

/* Performance and accessibility optimizations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .card:hover {
    transform: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid white;
    background: #000 !important;
  }

  .title,
  .description {
    color: white !important;
  }

  .badge {
    background: white !important;
    color: black !important;
  }
}
