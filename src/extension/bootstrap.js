const fs = require('fs')
const path = require('path')

const files = fs.readdirSync(path.join(__dirname, 'scripts')).filter(file => file.endsWith('.js'))
const manifest = JSON.parse(fs.readFileSync(path.join(__dirname, 'manifest.json')))

const content_scripts = [{
  js: [
    ...files.map(file => path.join('scripts', file)),
    'main.js',
  ],
  matches: ["https://betroz.com/*"],
}]

manifest.content_scripts = content_scripts

fs.writeFileSync(path.join(__dirname, 'manifest.json'), JSON.stringify(manifest, null, 2))
