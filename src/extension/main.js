setTimeout(async () => {
  const ws = new WebSocket('http://127.0.0.1:6969/ws')

  ws.onopen = () => {
    console.log('Connected to WebSocket')
  }

  ws.onmessage = (event) => {
    ws.close()
    window.location.reload()
  }

  ws.onerror = (err) => {
    console.error('Error connecting to WebSocket', err)
  }

  const data = await fetch('http://127.0.0.1:6969/scripts').then(r => r.json())

  for (const group of data.scripts) {
    await Promise.all(group.map(async source => {
      return new Promise(res => {
        const script = document.createElement('script')
        script.src = source
        script.onload = () => {
          res()
        }
        document.head.appendChild(script)
      })
    }))
  }
}, 1000)
