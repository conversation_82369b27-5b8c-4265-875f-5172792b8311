const path = require('path')
const fs = require('fs')
const package = require(path.join(__dirname, '..', '..', 'package.json'))

const formatDepName = (name) => name.replace(/[^a-z]/gi, '_')

const dependencies = Object.keys(package.dependencies)
const imports = dependencies.map(d => `import * as ${formatDepName(d)} from '${d}'`).join('\n')
const defs = dependencies.map(d => `window['$ebit'].${formatDepName(d)} = ${formatDepName(d)}`).join('\n')

const vendor = `
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.

${imports}

window['$ebit'] = window['$ebit'] || {}
${defs}
`

const externals = dependencies.reduce((acc, n) => {
  acc[n] = ['$ebit', formatDepName(n)]
  return acc
}, {})

fs.writeFileSync(path.join(__dirname, '..', 'react', 'vendor', 'vendor.js'), vendor)
fs.writeFileSync(path.join(__dirname, '..', 'react', 'vendor', 'vendor.externals.json'), JSON.stringify(externals, null, 2))
