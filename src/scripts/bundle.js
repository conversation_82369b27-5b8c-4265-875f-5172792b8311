const fs = require('fs')
const path = require('path')

const files = fs.readdirSync(path.join(__dirname, '..', '..', 'dist'))

const vendor = files.find(file => file === 'vendor.js')
const core = files.find(file => file === 'core.js')
const playground = files.find(file => file === 'playground.js')
const other = files.filter(file => file !== vendor && file !== core && file !== playground)

const scripts = [
  vendor,
  core,
  ...other,
  playground
]

let bundle = `// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.\n\n`;
for (const script of scripts) {
  bundle += fs.readFileSync(path.join(__dirname, '..', '..', 'dist', script)) + '\n\n'
}

fs.writeFileSync(path.join(__dirname, '..', '..', 'dist', '__all__.js'), bundle)

