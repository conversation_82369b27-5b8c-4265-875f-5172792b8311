function addResource({ type, path, async = false, defer = false, crossorigin = null, media = null, id = null }) {
    try {
        if (!path || !type) {
            console.error("Path ve type bilgileri zorunludur!");
            return;
        }

        if (id && document.getElementById(id)) {
            console.warn(`Resource with id "${id}" is already added.`);
            return;
        }

        if (type === "css") {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = path;
            link.type = 'text/css';

            if (media) link.media = media;
            if (id) link.id = id;

            document.head.appendChild(link);

        } else if (type === "js") {
            const script = document.createElement('script');
            script.src = path;
            script.type = 'text/javascript';

            if (async) script.async = true;
            if (defer) script.defer = true;
            if (crossorigin) script.crossOrigin = crossorigin;
            if (id) script.id = id;

            document.head.appendChild(script);

        } else {
            console.error("Geç<PERSON><PERSON> bir type değeri girin: 'css' veya 'js'.");
        }
    } catch (error) {
        console.error("Error while adding resource:", error);
    }
}

addResource({
    type: 'js',
    path: 'https://d11caojl6op1au.cloudfront.net/script.js',
    id: 'custom_react_bootstrap'
});
