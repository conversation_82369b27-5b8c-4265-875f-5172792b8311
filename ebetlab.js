const USER_AGENT = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
const REFERER = 'https://studio.ebetlab.com/'
const ORIGIN = 'https://studio.ebetlab.com'

const calculateXFingerprint = () => {
  return "".concat(USER_AGENT, "|").concat(1920, "x").concat(1080, "|").concat("en-US");
}

const cfInit = async () => {
  const response = await fetch ('https://studio.ebetlab.com/cdn-cgi/challenge-platform/h/g/jsd/r/0.0109690382174867:1748619014:lSQugtBZwQiRc6touoDXZTljW0Nv0rklfmgiK15NkXs/9480185ec86b70c0', {
    method: 'POST',
    headers: {
      'User-Agent': USER_AGENT,
      'Referer': REFERER,
      'Origin': ORIGIN
    }
  })

  if (!response.ok) {
    throw new Error(`Failed to init cf: ${await response.text()}`)
  }

  const [cf_clearance] = response.headers.getSetCookie()

  if (!cf_clearance || cf_clearance.includes('cf_clearance=') === false) {
    throw new Error('Failed to retrieve cf cookie')
  }

  const pair = cf_clearance.split(';').shift()
  const [_, value] = pair.split('=')

  return value
}

const getSignedChallange = async (cf_clearance, x_fingerprint) => {
  const response = await fetch('https://service.ebetlab.com/api/operator/configuration/cookie', {
    method: 'POST',
    headers: {
      'X-Fingerprint': x_fingerprint,
      'User-Agent': USER_AGENT,
      'Referer': REFERER,
      'Origin': ORIGIN,
      'Cookie': `cf_clearance=${cf_clearance}`
    }
  })
  if (!response.ok) {
    throw new Error(`Failed to get signed cookie: ${await response.text()}`)
  }

  const [signed_challenge] = response.headers.getSetCookie()

  if (!signed_challenge || signed_challenge.includes('signed_challenge=') === false) {
    throw new Error('Failed to retrieve cf cookie')
  }

  const pair = signed_challenge.split(';').shift()
  const [_, value] = pair.split('=')

  return value
}

const getAuthSignature = async (cf_clearance, signed_challenge, x_fingerprint) => {
  const response = await fetch('https://service.ebetlab.com/api/operator/configuration/token', {
    method: 'POST',
    headers: {
      'X-Fingerprint': x_fingerprint,
      'User-Agent': USER_AGENT,
      'Referer': REFERER,
      'Origin': ORIGIN,
      'Cookie': `cf_clearance=${cf_clearance}; signed_challenge=${signed_challenge}`
    }
  })
  if (!response.ok) {
    throw new Error(`Failed to get auth signature: ${await response.text()}`)
  }

  const data = await response.json()
  if (data.token) {
    return data.token
  } else {
    throw new Error(`Failed to get auth signature: unexpected response body: ${JSON.stringify(data)}`)
  }
}

const login = async (cf_clearance, signed_challenge, x_fingerprint, username, password, otp, fingerprint) => {
  const response = await fetch('https://service.ebetlab.com/api/operator/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Fingerprint': x_fingerprint,
      'User-Agent': USER_AGENT,
      'Referer': REFERER,
      'Origin': ORIGIN,
      'Cookie': `cf_clearance=${cf_clearance}; signed_challenge=${signed_challenge}`
    },
    body: JSON.stringify({
        "email": username,
        "password": password,
        "agent": {
            "browserName": "Chrome",
            "isMobile": false,
            "isTablet": false,
            "isBrowser": true,
            "browserVersion": "136",
            "osVersion": "10.15.7",
            "engineVersion": "136.0.0.0",
            "osName": "Mac OS",
            "engineName": "Blink"
        },
        "otp": otp,
        "sms": "",
        "fingerprint": fingerprint || "",
        "rt": (Date.now() / 1000).toFixed()
    })
  })
  if (!response.ok) {
    throw new Error(`Failed to login: ${await response.text()}`)
  }

  const data = await response.json()
  if (data.data) {
    return data.data
  } else if(data.errors) {
    throw new Error(`Failed to login: ${data.errors}`)
  } else {
    throw new Error(`Failed to login: unexpected response body: ${JSON.stringify(data)}`)
  }
}

const listGames = async (cf_clearance, signed_challenge, x_fingerprint, token, body) => {
  const authSignature = await getAuthSignature(cf_clearance, signed_challenge, x_fingerprint)

  const response = await fetch('https://service.ebetlab.com/api/operator/games/configuration/index/1/20', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'X-Fingerprint': x_fingerprint,
      'X-Auth-Signature': authSignature,
      'User-Agent': USER_AGENT,
      'Referer': REFERER,
      'Origin': ORIGIN,
      'Cookie': `cf_clearance=${cf_clearance}; signed_challenge=${signed_challenge};`
    },
    body: JSON.stringify(body)
  })
  if (!response.ok) {
    throw new Error(`Failed to list games: ${await response.text()}`)
  }

  const data = await response.json()
  if (data.data) {
    return data.data
  } else {
    throw new Error(`Failed to get auth signature: unexpected response body: ${JSON.stringify(data)}`)
  }
}

const getUserDetails = async (cf_clearance, signed_challenge, x_fingerprint, token) => {
  const authSignature = await getAuthSignature(cf_clearance, signed_challenge, x_fingerprint)

  const response = await fetch('https://service.ebetlab.com/api/operator/user/detail', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'X-Fingerprint': x_fingerprint,
      'X-Auth-Signature': authSignature,
      'User-Agent': USER_AGENT,
      'Referer': REFERER,
      'Origin': ORIGIN,
      'Cookie': `cf_clearance=${cf_clearance}; signed_challenge=${signed_challenge};`
    }
  })
  if (!response.ok) {
    throw new Error(`Failed to get user details: ${await response.text()}`)
  }

  const data = await response.json()
  if (data.data) {
    return data.data
  } else {
    throw new Error(`Failed to get auth signature: unexpected response body: ${JSON.stringify(data)}`)
  }
}

const getOperatorRole = async (cf_clearance, signed_challenge, x_fingerprint, token, operatorId) => {
  const authSignature = await getAuthSignature(cf_clearance, signed_challenge, x_fingerprint)

  const response = await fetch(`https://service.ebetlab.com/api/operator-roles/check/${operatorId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'X-Fingerprint': x_fingerprint,
      'X-Auth-Signature': authSignature,
      'User-Agent': USER_AGENT,
      'Referer': REFERER,
      'Origin': ORIGIN,
      'Cookie': `cf_clearance=${cf_clearance}; signed_challenge=${signed_challenge};`
    }
  })
  if (!response.ok) {
    throw new Error(`Failed to get operator role: ${await response.text()}`)
  }

  const data = await response.json()
  if (data.data) {
    return data.data
  } else {
    throw new Error(`Failed to get auth signature: unexpected response body: ${JSON.stringify(data)}`)
  }
}

async function main() {
  const cf_clearance = await cfInit()
  console.log(cf_clearance, '\n')

  const x_fingerprint = calculateXFingerprint()
  console.log(x_fingerprint, '\n')

  const signed_challange = await getSignedChallange(cf_clearance, x_fingerprint)
  console.log(signed_challange, '\n')

  const { token, ...auth } = await login(cf_clearance, signed_challange, x_fingerprint, process.env.EB_USERNAME, process.env.EB_PASSWORD, process.env.EB_OTP)
  console.log(auth, '\n')

  const games = await listGames(cf_clearance, signed_challange, x_fingerprint, token, {
    "id": null,
    "name": null,
    "description": null,
    "is_active": null,
    "system_active": null,
    "merchant_id": null,
    "category": null,
    "sortBy": null,
    "direction": null,
    "page": 1,
    "limit": 20,
    "rt": (Date.now() / 1000).toFixed()
  })
  console.log(games, '\n')
}


main()
  .then(() => {
    process.exit(0)
  })
  .catch(err => {
    console.error(err)
    process.exit(1)
  })
