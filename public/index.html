<!DOCTYPE html>
<html>
  <head>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="./index.js" defer></script>
    <style>
      * {
        margin: 0;
      }

      html, body {
        height: 100%;
      }
      
      body {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr;
        gap: 16px;
      }

      #root1, #root2 {
        border: 1px solid black;
        padding: 16px;
      }
    </style>
  </head>
  <body>
    <div id="root1"></div>
    <div id="root2"></div>
  </body>
</html>
