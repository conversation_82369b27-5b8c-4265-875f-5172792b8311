{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "author": "bobby <<EMAIL>>", "license": "MIT", "scripts": {"build:vendor": "npx webpack -c webpack/webpack.vendor.js", "build:react": "npx webpack -c webpack/webpack.react.js", "build:react:single": "npx webpack -c webpack/webpack.react.single.js", "dev:vendor": "nodemon --config nodemon.vendor.json", "dev:server": "nodemon --config nodemon.server.json", "dev:react": "nodemon --config nodemon.react.json", "dev": "concurrently \"yarn dev:server\" \"yarn dev:vendor\" \"yarn dev:react\"", "bundle": "yarn build:vendor && yarn build:react && node src/scripts/bundle.js"}, "dependencies": {"react": "^18", "react-dom": "^18", "react-icons": "^5.5.0", "react-shadow-root": "^6.2.0", "swiper": "^11.2.8", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "babel-loader": "^10.0.0", "chokidar": "^4.0.3", "concurrently": "^9.1.2", "css-loader": "^7.1.2", "express": "^5.1.0", "mini-css-extract-plugin": "^2.9.2", "nodemon": "^3.1.10", "prop-types": "^15.8.1", "style-loader": "^4.0.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "ws": "^8.18.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}