# Missions Extended User Implementation

## Overview

This implementation adds extended user creation functionality to the missions system, similar to how the VIP club handles token authentication. Before the missions page loads, the system now obtains the bearer token from local storage and uses it to create an extended user via the custom API.

## Implementation Details

### 1. Missions Lifecycle (`src/react/legacy/missions.js`)

Created a new lifecycle file similar to `vip_club.js` that handles:
- Bearer token retrieval from localStorage
- Extended user creation via POST to `https://gateway.makroz.org/api/makroz/extended-users/me`
- Initialization logic that runs before missions page loads
- Error handling and logging

### 2. Updated Missions API (`src/react/api/missions.js`)

Enhanced the existing missions API with:
- Token retrieval functionality
- Extended user creation function
- Integration with `getMissionsData()` to create extended user before fetching missions
- Session storage of extended user data for later use
- Exported utility functions for external use

### 3. Updated React Components

Modified the following components to support extended user creation:
- `MissionsPage.jsx`: Added logging for extended user creation process
- `MissionsModal.jsx`: Updated to use the enhanced missions API

### 4. Test Utilities (`src/react/api/missions.test.js`)

Created test functions for development/debugging:
- `testExtendedUserCreation()`: Tests the complete flow
- `checkExtendedUserData()`: Checks stored extended user data

## API Endpoint Details

### Extended User Creation
**Endpoint**: `POST https://gateway.makroz.org/api/makroz/extended-users/me`

**Headers**:
- `Authorization: Bearer {token}` (from localStorage 'bearer' key)
- `Content-Type: application/json`
- `Accept: application/json`

**Response**: Extended user data (stored in sessionStorage as 'extendedUser')
```json
{
    "success": true,
    "message": "Extended user already exists",
    "data": {
        "id": 1,
        "externalId": 2169576,
        "points": 5,
        "createdAt": "2025-06-25T17:01:29.146Z",
        "updatedAt": "2025-06-25T17:14:31.440Z"
    }
}
```

### Real Missions Data
**Endpoint**: `GET https://gateway.makroz.org/api/makroz/extended-users/me/missions`

**Headers**:
- `Authorization: Bearer {token}` (from localStorage 'bearer' key)
- `Content-Type: application/json`
- `Accept: application/json`

**Response**: Real missions data from backend (stored in sessionStorage as 'realMissionsData')
```json
{
    "success": true,
    "message": "Eligible missions retrieved successfully",
    "data": {
        "eligibleMissions": [
            {
                "id": 4,
                "name": "abc",
                "missionType": "daily",
                "reward": 1,
                "description": "1",
                "startDate": "1750873577",
                "endDate": "1750959977",
                "objectives": [...],
                "userAssignments": []
            }
        ],
        "totalEligible": 1,
        "totalActive": 1
    }
}
```

### Mission Statistics
**Endpoint**: `GET https://gateway.makroz.org/api/makroz/extended-users/me/missions/stats`

**Headers**:
- `Authorization: Bearer {token}` (from localStorage 'bearer' key)
- `Content-Type: application/json`
- `Accept: application/json`

**Response**: User mission statistics
```json
{
    "success": true,
    "message": "User mission statistics retrieved successfully",
    "data": {
        "balance": 200,
        "totalParticipations": 1,
        "completedParticipations": 1,
        "eligibleMissions": {
            "daily": 0,
            "weekly": 0,
            "monthly": 0,
            "custom": 0
        },
        "completedEligibleMissions": {
            "daily": 1,
            "weekly": 0,
            "monthly": 0,
            "custom": 0
        }
    }
}
```

### Mission Participation
**Endpoint**: `POST https://gateway.makroz.org/api/makroz/missions/:id/participations`

**Headers**:
- `Authorization: Bearer {token}` (from localStorage 'bearer' key)
- `Content-Type: application/json`
- `Accept: application/json`

**Request Body**: None

**Response**: Participation confirmation
```json
{
    "success": true,
    "message": "Mission participation started successfully",
    "data": {
        "missionId": 2,
        "userId": 2169576,
        "message": "You have successfully joined the mission and all objectives have been assigned to you."
    }
}
```

### Mission Completion/Claim
**Endpoint**: `POST https://gateway.makroz.org/api/makroz/missions/:missionId/complete`

**Headers**:
- `Authorization: Bearer {token}` (from localStorage 'bearer' key)
- `Content-Type: application/json`
- `Accept: application/json`

**Request Body**: None

**Response Examples**:

**Success (200)**:
```json
{
    "success": true,
    "message": "Mission completed successfully",
    "data": {
        "missionId": 8,
        "userId": 2169576,
        "pointsAwarded": 100,
        "newPointsTotal": 200
    }
}
```

**Already Completed (409)**:
```json
{
    "success": false,
    "message": "Mission is already completed"
}
```

**Objectives Not Complete (400)**:
```json
{
    "success": false,
    "message": "Not all mission objectives are completed yet",
    "data": {
        "totalObjectives": 1,
        "completedObjectives": 0
    }
}
```

## Flow Sequence

1. User navigates to missions page
2. System checks for bearer token in localStorage
3. If token exists, makes POST request to create extended user
4. Extended user data is stored in sessionStorage
5. If extended user creation successful, fetches real missions data from backend
6. Backend missions data is transformed to frontend format
7. Real missions data is displayed with proper empty state handling
8. If any step fails, system falls back to mock data (graceful degradation)

## Data Transformation

The backend missions data is automatically transformed to match the frontend component expectations:
- Groups missions by type (daily, weekly, monthly)
- Creates appropriate header slides based on available missions
- Handles empty states with user-friendly messages
- Transforms objectives and user assignments to frontend format
- Distinguishes between participating and non-participating missions
- Calculates progress from user assignments
- Determines completion status based on objective operators (ge, gt, le, lt, eq)

## Mission Participation System

### Participation Status
- **Participating**: Mission has `userAssignments` array with data
- **Not Participating**: Mission has empty or no `userAssignments` array

### Mission Details Modal
- Click any mission card to open detailed view
- Shows all objectives with descriptions and progress
- Displays participation status and completion state
- "Participate" button for non-participating missions
- "In Progress" status for participating missions

### Progress Calculation
- Progress is calculated from `userAssignments[].progress` values
- Completion is determined by objective operators:
  - `ge`: greater than or equal
  - `gt`: greater than
  - `le`: less than or equal
  - `lt`: less than
  - `eq`: equal

## Usage

### Automatic Usage
The extended user creation happens automatically when:
- Loading the missions page
- Opening the missions modal
- Calling `getMissionsData()` function

### Manual Usage
For testing or manual integration:

```javascript
import { createExtendedUserAPI, getBearerTokenAPI } from './api/missions.js';

// Check if user is logged in
const token = getBearerTokenAPI();

// Create extended user
const extendedUser = await createExtendedUserAPI();

// Check stored data
const storedData = JSON.parse(sessionStorage.getItem('extendedUser'));
```

### Browser Console Testing
```javascript
// Test the complete flow
testExtendedUserCreation();

// Check stored extended user data
checkExtendedUserData();
```

## Error Handling

- Graceful degradation: If extended user creation fails, missions still load
- Comprehensive logging for debugging
- Session storage fallback for extended user data
- Token validation before API calls

## Security Considerations

- Bearer token is retrieved securely from localStorage
- API calls use proper authentication headers
- Extended user data is stored in sessionStorage (cleared on tab close)
- Error messages don't expose sensitive information

## Future Enhancements

1. Add retry logic for failed extended user creation
2. Implement extended user data refresh mechanism
3. Add extended user data validation
4. Consider moving to more persistent storage if needed
5. Add extended user data cleanup on logout
